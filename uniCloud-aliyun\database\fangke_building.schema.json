{"bsonType": "object", "required": ["number", "address", "uid", "room_num", "floor", "type"], "permission": {"read": "auth.uid != null", "create": "auth.uid != null", "update": "auth.uid != null", "delete": "auth.uid != null"}, "properties": {"_id": {"description": "存储文档 ID（房子 ID），系统自动生成,房子唯一标识,不允许重复"}, "uid": {"bsonType": "string", "title": "创建用户的id", "description": "创建用户的id", "foreignKey": "uni-id-users._id", "defaultValue": {"$env": "uid"}, "errorMessage": {"required": "{title}不能为空"}}, "name": {"bsonType": "string", "title": "房子名称", "description": "名称，用于显示房子名称,可以是自定义名称，方便查找", "label": "名称", "trim": "both"}, "number": {"bsonType": "string", "title": "楼房号", "description": "楼房号，用于显示自己房子的楼房号", "label": "楼房号", "trim": "right", "errorMessage": {"required": "{title}不能为空"}}, "desc": {"bsonType": "string", "title": "房子描述", "description": "房子描述", "label": "描述", "trim": "both"}, "address": {"bsonType": "string", "title": "房子地址", "description": "房子地址", "label": "地址", "trim": "both", "errorMessage": {"required": "{title}不能为空"}}, "avatar": {"bsonType": "array", "title": "封面大图", "description": "缩略图地址", "label": "封面大图", "trim": "both", "arrayType": "file"}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "创建时间", "defaultValue": {"$env": "now"}}, "create_ip": {"bsonType": "string", "title": "创建时IP地址", "description": "创建时 IP 地址", "forceDefaultValue": {"$env": "clientIP"}}, "update_time": {"bsonType": "timestamp", "title": "最后修改时间", "description": "最后修改时间", "defaultValue": {"$env": "now"}}, "last_update_ip": {"bsonType": "string", "description": "最后修改时 IP 地址", "forceDefaultValue": {"$env": "clientIP"}}, "sum": {"bsonType": "int", "description": "当月一共应收的房租", "defaultValue": 0}, "money": {"bsonType": "int", "description": "当前收到的房租", "defaultValue": 0}, "floor": {"bsonType": "int", "description": "一共有几层", "defaultValue": 0}, "room_num": {"bsonType": "int", "description": "房间数", "defaultValue": 0}, "type": {"bsonType": "int", "description": "房源类型： 1.集中式 2.分散式", "defaultValue": 1, "enum": [{"text": "集中式", "value": 1}, {"text": "分散式", "value": 2}]}, "province_code": {"bsonType": "string", "title": "省份编码", "description": "省份编码", "foreignKey": "opendb-city-china.code"}, "province_name": {"bsonType": "string", "title": "省份名称", "description": "省份名称"}, "city_code": {"bsonType": "string", "title": "城市编码", "description": "城市编码", "foreignKey": "opendb-city-china.code", "enum": {"collection": "opendb-city-china", "orderby": "value asc", "field": "code as value, name as text"}}, "city_name": {"bsonType": "string", "title": "城市名称", "description": "城市名称"}, "district_code": {"bsonType": "string", "title": "区县编码", "description": "区县编码", "foreignKey": "opendb-city-china.code"}, "district_name": {"bsonType": "string", "title": "区县名称", "description": "区县名称"}, "location": {"bsonType": "array", "arrayType": "string", "label": "经纬度坐标数组，坐标0为经度，坐标1为纬度"}}}