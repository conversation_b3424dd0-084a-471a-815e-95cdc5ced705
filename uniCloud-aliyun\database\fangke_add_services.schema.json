// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
    "description": "增值服务记录表",
    "bsonType": "object",
    "required": [],
    "permission": {
        "read": "auth.uid != null",
        "create": "auth.uid != null",
        "update": "auth.uid != null",
        "delete": "auth.uid != null"
    },
    "properties": {
        "_id": {
            "description": "ID，系统自动生成"
        },
        "uid": {
            "bsonType": "string",
            "description": "用户ID",
            "foreignKey": "uni-id-users._id",
            "forceDefaultValue": {
                "$env": "uid"
            }
        },
        "create_time": {
            "bsonType": "timestamp",
            "forceDefaultValue": {
                "$env": "now"
            }
        },
        "update_time": {
            "bsonType": "timestamp",
            "forceDefaultValue": {
                "$env": "now"
            }
        },
        "service_type": {
            "bsonType": "int",
            "description": "服务类型（0.未启用 1.线上签约合同 2.发短信 3.保修 4.保洁 5.巡房 6.跑腿）",
            "defaultValue": 0,
            "enum": [{
                    "text": "未启用",
                    "value": 0
                },
                {
                    "text": "线上签约合同",
                    "value": 1
                },
                {
                    "text": "发短信",
                    "value": 2
                },
                {
                    "text": "保修",
                    "value": 3
                },
                {
                    "text": "保洁",
                    "value": 4
                },
                {
                    "text": "巡房",
                    "value": 5
                },
                {
                    "text": "跑腿",
                    "value": 6
                }
            ]
        },
        "count": {
            "bsonType": "int",
            "description": "一共拥有次数",
            "label": "总次数"
        },
        "usage_count": {
            "bsonType": "int",
            "description": "使用次数",
            "label": "使用次数"
        },
        "price": {
            "bsonType": "int",
            "description": "金额",
            "label": "单价"
        },
        "status": {
            "bsonType": "bool",
            "description": "状态（0.过期 1.活跃）",
            "label": "状态"
        },
        "pay_time": {
            "bsonType": "timestamp",
            "defaultValue": {
                "$env": "now"
            }
        }

    }
}