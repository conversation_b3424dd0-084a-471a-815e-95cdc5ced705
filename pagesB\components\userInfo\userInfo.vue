<template>
	<view class="content">
		<view><text class="title">租客信息</text> </view>
		
		<uni-forms ref="userform" :rules="rules" :modelValue="userFormData" class="info">
			<uni-forms-item label="名字" required name="name">
				<uni-easyinput placeholder="输入租客名字" v-model="userFormData.name"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item label="性别" required name="sex">
				<uni-data-checkbox v-model="userFormData.sex" :localdata="sex"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item label="电话" name="tel">
				<uni-easyinput placeholder="输入电话" type="number" v-model="userFormData.tel"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item label="身份证" name ="num">
				<uni-easyinput placeholder="输入有效身份证" v-model="userFormData.num"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item label="备注" name="desc">
				<uni-easyinput placeholder="备注" v-model="userFormData.desc"></uni-easyinput>
			</uni-forms-item>
		</uni-forms>
		<view ><button type="primary" size="mini" @click="submit" class="add">添加</button></view>
		
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 定义组件状态
const userFormData = ref({
  name: '',
  sex: 0,
  tel: '',
  num: '',
  desc: '',
});

const sex = ref([
  { text: '未知', value: 0 },
  { text: '男', value: 1 },
  { text: '女', value: 2 }
]);

const rules = ref({
  name: {
    rules: [
      { required: true, errorMessage: '姓名不能为空' }
    ]
  }
});

// 定义计算属性
const alignment = computed(() => {
  if (current.value === 0) return 'left';
  if (current.value === 1) return 'top';
  return 'left';
});

// 定义方法
const emit = defineEmits(['userData']);
const userformRef = ref(null);

const submit = () => {
  console.log('submits');
  userformRef.value.validate().then(res => {
    console.log('success', res);
    emit("userData", res);
  }).catch(err => {
    console.log('err', err);
  });
};

// 生命周期钩子
onMounted(() => {
  // 设置自定义表单校验规则，必须在节点渲染完毕后执行
});
</script>

<style lang="scss">
	.content {
		padding: 30rpx;
		justify-content: center;
		background-color: white;
		border-radius: 20rpx;
		.title {
			font-size: 40rpx;
		}

		.info {
			margin-top: 20rpx;
		}

		.add {}
	}
</style>