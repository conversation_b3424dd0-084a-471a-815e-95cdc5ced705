<template>
	<view class="content">
		<view class="form-section">
			<uni-section title="租客信息" type="line"></uni-section>
			<view class="form-item">
				<text class="label required">姓名</text>
				<input type="text" placeholder="请输入" v-model="formData.name" />
			</view>
			<view class="form-item">
				<text class="label required">手机号码</text>
				<input type="number" placeholder="请输入" v-model="formData.phone" />
			</view>
			<view class="form-item">
				<text class="label required">性别</text>
				<view class="gender-group">
					<view class="gender-item" :class="{ active: formData.gender === 1 }"
						@click="formData.gender = 1">
						<text>男</text>
					</view>
					<view class="gender-item" :class="{ active: formData.gender === 2 }"
						@click="formData.gender = 2">
						<text>女</text>
					</view>
				</view>
			</view>

			<view class="form-item">
				<text class="label required">证件号码</text>
				<input type="text" placeholder="请输入" v-model="formData.id_card" />
			</view>
			<view class="form-item">
				<uni-collapse ref="collapse" :open="false" style="width: 100%;">
					<uni-collapse-item title="证件照片">
						<view class="cardId">
							<view class="upload-area">
								<uni-icons type="camera" size="24" color="#999" />
								<text>上传照片</text>
							</view>
							<text>身份证正面</text>
						</view>

					</uni-collapse-item>
				</uni-collapse>
			</view>

		</view>

		<view class="footer">
					<button class="submit-btn" type="primary" @click="save">保存</button>
		</view>
	</view>
</template>

<script setup>
	import {
		nextTick,
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	
	const formData = ref({
		// 租客信息
		name: '',
		phone: '',
		gender: 1,
		id_card: '',
	})
	let index = -1
	let eventChannel;
	onMounted(() => {
	    const instance = getCurrentInstance().proxy
	    eventChannel = instance.getOpenerEventChannel();
	
	  })
	
	onLoad((e)=>{
		if(e.item && e.index){
			let item = JSON.parse(decodeURIComponent(e.item))
			console.log("item",item);
			index = e.index
			formData.value.name = item.name
			formData.value.phone = item.phone
			formData.value.gender = item.gender
			formData.value.id_card = item.id_card
		}
	})
	
	// 表单校验
	const validateForm = () => {
		const requiredFields = [{
				field: 'name',
				name: '姓名'
			},
			{
				field: 'phone',
				name: '手机号码'
			},
			{
				field: 'id_card',
				name: '证件号码'
			}
		]
	
		for (const item of requiredFields) {
			if (!formData.value[item.field] || formData.value[item.field].toString().trim() === '') {
				uni.showToast({
					title: `请填写${item.name}`,
					icon: 'none'
				})
				return false
			}
		}
	
		// 验证手机号格式
		const phoneReg = /^1[3-9]\d{9}$/
		if (!phoneReg.test(formData.value.phone)) {
			uni.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			})
			return false
		}
	
		// 验证身份证格式
		const idReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
		if (!idReg.test(formData.value.id_card)) {
			uni.showToast({
				title: '请输入正确的身份证号码',
				icon: 'none'
			})
			return false
		}
	
	
		return true
	}
	
	
	const save = ()=>{
		console.log("save",index);
		if(!validateForm()){
			if(index !== -1){
				eventChannel.emit('editEvent', formData.value,index);
				uni.navigateBack()
			}else{
				eventChannel.emit('add', formData.value);
				uni.navigateBack()
			}
		}
		
	}
</script>

<style scoped>
	.content {
		flex: 1;
	}

	.form-section {
		background: #fff;
		margin-bottom: 20rpx;
		padding: 0 30rpx;
	}

	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		font-size: 14px;
		color: #333;
		font-weight: 500;
	}

	.required::before {
		content: '*';
		color: #ff4d4f;
		margin-right: 4rpx;
	}

	.add-btn {
		color: #07c160;
		font-size: 14px;
		font-weight: normal;
	}

	.form-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-height: 88rpx;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-right {
		display: flex;
	}

	.remark-item {
		align-items: flex-start;
		padding: 20rpx 0;
	}

	.remark-input {
		flex: 1;
		height: 160rpx;
		font-size: 14px;
		color: #333;
		margin-left: 20rpx;
		border: 1px solid #eee;
		border-radius: 4px;
		padding: 10rpx;
		box-sizing: border-box;
	}

	.label {
		width: 250rpx;
		font-size: 14px;
		color: #333;
		flex-shrink: 0;
	}

	.value {
		font-size: 14px;
		color: #999;
		margin-right: 20rpx;
	}

	input {
		font-size: 14px;
		text-align: right;
		border: none;
		outline: none;
		width: 300rpx;
	}

	.gender-group {
		display: flex;
		gap: 20rpx;
	}

	.gender-item {
		padding: 10rpx 30rpx;
		border: 1px solid #ddd;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.gender-item.active {
		border-color: #07c160;
		color: #07c160;
		background-color: #e8f5e8;
	}

	.upload-area {
		flex: 1;
		width: calc(100%/3);
		height: 160rpx;
		border: 1px dashed #ddd;
		border-radius: 4px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.upload-area text {
		margin-top: 8rpx;
		font-size: 12px;
		color: #999;
	}

	.roommate-item {
		display: flex;
		align-items: center;
		height: 88rpx;
	}

	.roommate-item .name {
		width: 200rpx;
		font-size: 14px;
		color: #333;
	}
	.footer {
		padding: 20rpx 30rpx;
		background: #fff;
		display: flex;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}
	
	.submit-btn {
		width: 100%;
		background: #07c160;
		color: #fff;
		font-size: 16px;
	}
</style>