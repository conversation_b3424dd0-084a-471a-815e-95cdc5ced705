<template>
	<view class="container">
		<!-- 租约信息 -->
		<view class="section">
			<view class="section-title">
				<view class="title-line"></view>
				<text class="title-text">租约信息</text>
			</view>
			<view class="info-item">
				<text class="label">房间</text>
				<text class="value">{{tentant.room_name}}</text>
				<uni-icons class="phone-icon" type="phone" size="20" color="#00C8B3"></uni-icons>
			</view>
			<view class="info-item">
				<text class="label">租客</text>
				<text class="value">{{tentant.name}}</text>
				<text class="phone">13262656533</text>
			</view>
			<view class="info-item">
				<text class="label">原租期</text>
				<text class="value">{{tentant.time}}</text>
			</view>
			<view class="info-item link-item" @click="showCheckoutDatePicker">
				<text class="label required">退房时间</text>
				<picker mode="date" :value="checkoutDate" :start="startDate" :end="endDate" @change="bindDateChange">
					<view class="value" style="display: flex;">
						<view class="uni-input">{{checkoutDate || time}}</view>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</picker>

			</view>
			<view class="info-item link-item" @click="showCheckoutReasonPicker">
				<text class="label required">退房原因</text>
				<view>
					<text class="value">{{checkoutReason}}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		<!-- 金额计算 -->
		<view class="section">
			<view class="section-title">
				<view class="title-line"></view>
				<text class="title-text">金额计算</text>
			</view>
			<view class="amount-item link-item">
				<text class="label">欠款</text>
				<text class="amount">{{formatAmount(amounts.debt)}}元</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			<view class="amount-item link-item">
				<text class="label">扣款</text>
				<text class="amount">{{formatAmount(amounts.deduction)}}元</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			<view class="amount-item link-item">
				<text class="label">退款</text>
				<text class="amount">{{formatAmount(amounts.refund)}}元</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			<view class="total-amount">
				<text class="total-label">应收(元)</text>
				<text class="total-desc">应收金额=当前欠款+扣款-退款</text>
				<text class="total-value">{{formatAmount(totalAmount)}}元</text>
			</view>
		</view>
		<!-- 收款方式 -->
		<view class="section">
			<text class="payment-title">收款方式</text>
			<view class="payment-methods">
				<view class="payment-grid">
					<view class="payment-item" :class="{ active: selectedPayment === '微信转账' }"
						@click="selectPayment('微信转账')">
						<text>微信转账</text>
					</view>
					<view class="payment-item" :class="{ active: selectedPayment === '支付宝转账' }"
						@click="selectPayment('支付宝转账')">
						<text>支付宝转账</text>
					</view>
					<view class="payment-item" :class="{ active: selectedPayment === '银行卡转账' }"
						@click="selectPayment('银行卡转账')">
						<text>银行卡转账</text>
					</view>
					<view class="payment-item" :class="{ active: selectedPayment === '现金' }"
						@click="selectPayment('现金')">
						<text>现金</text>
					</view>
					<view class="payment-item" :class="{ active: selectedPayment === 'POS刷卡' }"
						@click="selectPayment('POS刷卡')">
						<text>POS刷卡</text>
					</view>
					<view class="payment-item" :class="{ active: selectedPayment === '其他' }"
						@click="selectPayment('其他')">
						<text>其他</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 备注 -->
		<view class="section">
			<view class="section-title">
				<view class="title-line"></view>
				<text class="title-text">备注</text>
			</view>
			<view class="remark-item link-item">
				<text class="label">凭证</text>
			</view>
			<view class="image-upload-container">
				<view class="image-grid">
					<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
						<image :src="image" mode="aspectFill" class="uploaded-image" @click="previewImage(index)"></image>
						<view class="delete-btn" @click="deleteImage(index)">
							<uni-icons type="clear" size="16" color="#fff"></uni-icons>
						</view>
					</view>
					<view class="upload-btn" v-if="uploadedImages.length < 9" @click="chooseImage">
						<uni-icons type="plus" size="40" color="#999"></uni-icons>
						<text class="upload-text">添加图片</text>
					</view>
				</view>
				<text class="upload-tip">最多可上传9张图片</text>
			</view>
			<view class="remark-input">
				<textarea placeholder="请输入" v-model="remark" />
			</view>
		</view>
		<!-- 底部按钮 -->
		<view class="footer">
			<button class="submit-btn" type="primary" @click="showConfirmDialog">下一步</button>
		</view>
	</view>
</template>
<script setup>
	import {
		ref,
		computed
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app'
	const tentant = ref({})
	const room_id = ref('')
	const time = ref('')
	const checkoutDate = ref('')
	const checkoutReason = ref('正常退租')
	const remark = ref('')
	const selectedPayment = ref('微信转账')
	const uploadedImages = ref([])

	// 添加金额相关的响应式数据
	const amounts = ref({
		debt: 18238.10,    // 当前欠款
		deduction: 500.00, // 扣款
		refund: 1000.00    // 退款
	})

	// 计算应收金额
	const totalAmount = computed(() => {
		return amounts.value.debt + amounts.value.deduction - amounts.value.refund
	})

	// 格式化金额显示
	const formatAmount = (amount) => {
		return amount.toFixed(2)
	}

	// 更新金额数据
	const updateAmounts = (newAmounts) => {
		if (newAmounts.debt !== undefined) amounts.value.debt = newAmounts.debt
		if (newAmounts.deduction !== undefined) amounts.value.deduction = newAmounts.deduction
		if (newAmounts.refund !== undefined) amounts.value.refund = newAmounts.refund
	}

	// 获取当前应收金额
	const getTotalAmount = () => {
		return totalAmount.value
	}

	onLoad((e) => {
		let now = new Date()
		let month = (now.getMonth() + 1).toString().padStart(2, '0')
		let day = now.getDate().toString().padStart(2, '0')
		time.value = now.getFullYear() + "-" + month + "-" + day
		if (e.data && e.room_id) {
			let data = JSON.parse(decodeURIComponent(e.data))
			tentant.value = data
			room_id.value = e.room_id
		}
	})

	const bindDateChange = (event) => {
		console.log("bindData", event.detail);
		checkoutDate.value = event.detail.value
	}

	const showCheckoutReasonPicker = () => {
		uni.showActionSheet({
			itemList: ['正常退租', '违约退租', '特殊退租'],
			success: (res) => {
				const reasons = ['正常退租', '违约退租', '特殊退租'];
				checkoutReason.value = reasons[res.tapIndex];
			}
		})
	}

	const selectPayment = (payment) => {
		selectedPayment.value = payment
	}

	const showConfirmDialog = () => {
		// 表单验证

		uni.showModal({
			title: '确认退租',
			content: '是否确认退租，退租后会生成退租单',
			success: (res) => {
				if (res.confirm) {
					handleConfirm()
				}
			}
		})
	}

	const handleConfirm = () => {
		// 处理确认退租的逻辑
		uni.showLoading({
			title: '处理中...'
		});

		// 模拟提交数据
		const checkoutData = {
			roomId: room_id.value,
			tenant: tentant.value,
			checkoutDate: checkoutDate.value,
			checkoutReason: checkoutReason.value,
			paymentMethod: selectedPayment.value,
			remark: remark.value,
			images: uploadedImages.value,
			amounts: {
				debt: amounts.value.debt,
				refund: amounts.value.refund,
				deduction: amounts.value.deduction,
				total: totalAmount.value
			}
		};

		console.log('退租数据:', checkoutData);
		console.log('计算结果: 当前欠款(' + amounts.value.debt + ') + 扣款(' + amounts.value.deduction + ') - 退款(' + amounts.value.refund + ') = ' + totalAmount.value);

		// 模拟网络请求
		setTimeout(() => {
			uni.hideLoading();
			uni.showToast({
				title: '退租单已生成',
				icon: 'success',
				duration: 2000
			});

			// 2秒后跳转或返回
			setTimeout(() => {
				uni.navigateBack();
			}, 2000);
		}, 1500);
	}

	const chooseImage = () => {
		const remainingCount = 9 - uploadedImages.value.length;
		uni.chooseImage({
			count: remainingCount,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				const tempFilePaths = res.tempFilePaths;
				uploadedImages.value = uploadedImages.value.concat(tempFilePaths);
				uni.showToast({
					title: `已添加${tempFilePaths.length}张图片`,
					icon: 'success'
				});
			},
			fail: (err) => {
				console.log('选择图片失败:', err);
				uni.showToast({
					title: '选择图片失败',
					icon: 'none'
				});
			}
		});
	}

	const previewImage = (index) => {
		uni.previewImage({
			current: index,
			urls: uploadedImages.value
		});
	}

	const deleteImage = (index) => {
		uni.showModal({
			title: '提示',
			content: '确定要删除这张图片吗？',
			success: (res) => {
				if (res.confirm) {
					uploadedImages.value.splice(index, 1);
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				}
			}
		});
	}
</script>
<style>
	page {
		height: 100%;
		background-color: #F5F5F5;
	}

	.container {
		min-height: 100%;
		background-color: #F5F5F5;
	}

	.section {
		margin-top: 20rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
	}

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.title-line {
		width: 6rpx;
		height: 32rpx;
		background-color: #00C8B3;
		margin-right: 16rpx;
	}

	.title-text {
		font-size: 15px;
		font-weight: 500;
		color: #333333;
	}

	.info-item {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}

	.label {
		width: 140rpx;
		font-size: 14px;
		color: #666666;
	}

	.required::before {
		content: '*';
		color: #FF4D4F;
		margin-right: 4rpx;
	}

	.value {
		flex: 1;
		font-size: 14px;
		color: #333333;
	}

	.phone {
		font-size: 14px;
		color: #333333;
		margin-left: 20rpx;
	}

	.phone-icon {
		width: 40rpx;
		height: 40rpx;
		margin-left: 20rpx;
	}

	.link-item {
		position: relative;
	}

	.amount-item {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.amount {
		flex: 1;
		text-align: right;
		font-size: 14px;
		color: #333333;
		margin-right: 20rpx;
	}

	.total-amount {
		margin-top: 40rpx;
		padding-top: 30rpx;
		border-top: 1px solid #EEEEEE;
	}

	.total-label {
		font-size: 14px;
		color: #666666;
	}

	.total-desc {
		font-size: 12px;
		color: #999999;
		margin-left: 20rpx;
	}

	.total-value {
		float: right;
		font-size: 16px;
		font-weight: 500;
		color: #333333;
	}

	.payment-title {
		font-size: 14px;
		color: #666666;
		margin-bottom: 30rpx;
	}

	.payment-methods {
		margin-top: 20rpx;
	}

	.payment-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 30rpx;
	}

	.payment-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx 0;
		background-color: #F8F8F8;
		border-radius: 8rpx;
	}

	.payment-item text {
		font-size: 12px;
		color: #666666;
		margin-top: 10rpx;
	}

	.payment-item.active {
		background-color: #E6F7F5;
	}

	.payment-item.active text {
		color: #00C8B3;
	}

	.remark-input {
		background-color: #F5F5F5;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-top: 30rpx;
	}

	.remark-input textarea {
		width: 100%;
		height: 160rpx;
		font-size: 14px;
		color: #333333;
	}

	.footer {
		padding: 40rpx 30rpx;
		background-color: #FFFFFF;
	}

	.submit-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background-color: #00C8B3;
		color: #FFFFFF;
		font-size: 16px;
		border-radius: 44rpx;
	}

	.image-upload-container {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
	}

	.image-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.image-item {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.uploaded-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
		border: 2rpx solid #EEEEEE;
	}

	.delete-btn {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.upload-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 200rpx;
		height: 200rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		border: 2rpx dashed #CCCCCC;
	}

	.upload-btn:active {
		background-color: #EEEEEE;
	}

	.upload-text {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
	}

	.upload-tip {
		font-size: 24rpx;
		color: #999999;
		margin-top: 20rpx;
		text-align: center;
	}
</style>