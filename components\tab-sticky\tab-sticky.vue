<template>
	<view class="container">
		<scroll-view scroll-x class="scroll">
			<view class="parent">
				<view v-for="(item,tab) in list" :key="tab">
					<view class="item" @click="onclick(tab)">
						<view class="title">
							<text>{{list[tab].name}}</text>
						</view>
						<view v-if="tab == position" class="block">
							<text style="background-color: #01B862;width: 100%;height: 100%;"></text>
						</view>

					</view>
				</view>
			</view>

		</scroll-view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';

	const props = defineProps({
		list: {
			type: Array,
			default: () => []
		}
	});

	const emit = defineEmits(['change']);

	// 响应式数据
	const position = ref(0);

	// 点击事件处理
	const onclick = (e) => {
		console.log("click", e);
		position.value = e;
		emit("change", e);
	};
</script>

<style lang="scss" scoped>
	.container {
		background-color: white;
		position: sticky;

		.scroll {
			.parent {
				display: flex;
				flex-direction: row;

				.item {
					width: auto;
					display: flex;
					flex-direction: column;

					.title {
						display: flex;
						justify-content: center;
						padding: 20rpx;
					}

					.block {
						display: flex;
						height: 5rpx;
						background-color: white;
						width: 100%;
					}
				}
			}

		}

	}
</style>