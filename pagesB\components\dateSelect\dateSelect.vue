<template>
	<view class="container">
		<view class="week">
			<view class="day" v-for="index in 7" :key="index">
				<text>{{weeks[index]}}</text>
			</view>
		</view>
		<uni-grid :column="7" :showBorder="false" :square="false">
			<uni-grid-item v-for="(item, index) in days" :index="index" :key="index">
				<view class="time">
					<!-- <text class="text" style="display: flex;justify-content: center;padding: 5rpx;">{{item}}</text> -->
					<uni-tag :text="item.day" style="display: flex;justify-content: center;padding: 5rpx;"
						:type="item.isSelect?'warning':'primary'" :disabled="!item.isWork"
						@click="change(index)"></uni-tag>
				</view>

			</uni-grid-item>
		</uni-grid>

	</view>


</template>

<script setup>
	import {
		ref,
		onMounted,
		onUnmounted
	} from 'vue';

	// 定义组件状态
	const weeks = ref(["日", "一", "二", "三", "四", "五", "六"]);
	const dates = ref([]);
	const days = ref([]);

	// 生命周期钩子 - 组件挂载后
	onMounted(() => {
		console.log("dateSelect mounted");
		const now = new Date();
		console.log("今天是：", now.getDate() + "_" + now.getDay());
		dates.value = calendarData(now.getFullYear(), now.getMonth() + 1, now.getDate());
		console.log("当前数组：", dates.value);

		dates.value.forEach((item) => {
			let isWork = false;
			let isSelect = false;
			if (item >= now.getDate()) {
				isWork = true;
			}
			if (item == now.getDate()) {
				isSelect = true;
			}
			const num = {
				day: item,
				isWork: isWork,
				isSelect: isSelect
			};
			days.value.push(num);
		});
	});

	// 生命周期钩子 - 组件卸载前
	onUnmounted(() => {
		// 组件销毁时的清理工作
	});

	// 定义方法
	const emit = defineEmits(['select']);

	const calendarData = (year, month, day) => {
		console.log("执行calendarData", "年" + year + " -月" + month);
		// 获取指定年月的第一天
		const firstDay = new Date(year, month - 1, 1);
		// 获取该月的天数
		const daysInMonth = new Date(year, month, 0).getDate();

		const calendarData = [];

		// 填充上个月的尾部日期
		const prevMonthLastDay = new Date(year, month - 1, 0).getDate();
		const prevMonthDays = firstDay.getDay(); // 0-6，0代表星期日
		for (let i = prevMonthDays; i > 0; i--) {
			calendarData.push(prevMonthLastDay - i + 1);
		}

		// 填充当月的日期
		for (let i = 1; i <= daysInMonth; i++) {
			calendarData.push(i);
		}

		// 填充下个月的起始日期
		const nextMonthDays = 42 - calendarData.length;
		for (let i = 1; i <= nextMonthDays; i++) {
			calendarData.push(i);
		}

		const result = calendarData.find((m, index) => {
			return m == day;
		});

		console.log("获取第一个相同日期的下标：", result);
		let line = parseInt(result / 7);
		let _i = result % 7;
		console.log("求余：", line + "_" + _i);
		return calendarData.splice(line * 7, 14);
	};

	const change = (index) => {
		let day = 0;
		for (let i = 0; i < days.value.length; i++) {
			if (i === index) {
				days.value[i].isSelect = true;
				day = days.value[i].day;
			} else {
				days.value[i].isSelect = false;
			}
		}
		emit("select", day);
	};
</script>

<style lang="scss">
	.container {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		display: flex;
		align-content: center;
		flex-direction: column;
		justify-content: space-around;
		width: 100%;
		box-sizing: border-box;

		.week {
			display: flex;
			width: 100%;
			justify-content: space-around;
			margin-bottom: 10rpx;

			.day {}
		}

		.content {
			display: flex;
			width: 100%;
			justify-content: space-around;
			margin-top: 20rpx;
			box-sizing: border-box;

			.time {
				display: flex;
				justify-content: center;
				box-sizing: border-box;
			}
		}
	}
</style>