<template>
	<view class="container">
		<view class="toolbar">
			<text style="width: 20rpx;height: 100%;background-color: #4B7EE0;"></text>
			<view style="display: flex; justify-items: center;">
				<button type="primary" size="mini" @click="add" style="margin-right: 10rpx;">{{addName}}</button>
				<button type="warn" size="mini" @click="del">{{delName}}</button>
			</view>
		</view>
		<view v-for="(item,index) in list" :key="index">
			<buildingList :isShow="showCheck" :info="item" @getData="goBuilding" @check="check" ref="listnum">
			</buildingList>
		</view>

	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import buildingList from '../components/buildingList/buildingList.vue';
	const db = uniCloud.database();
	const api = uniCloud.importObject("ApiFuntion");

	// 定义组件状态
	const id = ref('');
	const showCheck = ref(false);
	const list = ref([]);
	const delName = ref('删除');
	const addName = ref('添加');
	const modeShow = ref(false);
	const delIndex = ref([]);

	// 生命周期钩子 - 页面加载时
	onMounted(() => {
		id.value = useUniCloud.getCurrentUserInfo().uid;
		console.log("onLoad", id.value);
		getBuilding();
	});

	// 定义方法
	const add = () => {
		if (showCheck.value) { // 删除
			delBuilding();
		} else {
			uni.navigateTo({
				url: "/pagesB/add/addHouse"
			});
		}
	};

	const del = () => {
		showCheck.value = !showCheck.value;
		if (showCheck.value) {
			delName.value = '取消';
			addName.value = '确认';
		} else {
			delName.value = '删除';
			addName.value = '添加';
			for (let i = 0; i < delIndex.value.length; i++) {
				delIndex.value[i].values = false;
			}
			uni.$emit("stop");
		}
	};

	const check = (e) => {
		console.log("check", e);
		delIndex.value.forEach(element => {
			if (element.id === e) {
				element.values = !element.values;
			}
		});
	};

	const goBuilding = (e) => {
		console.log("goBuilding", e);
		uni.navigateTo({
			url: `/pagesB/add/addHouse?id=${e._id}`
		});
	};

	const getBuilding = () => {
		db.collection("fangke_building").where({
			user_id: id.value
		}).get().then(res => {
			console.log("getBuilding", res);
			list.value = res.result.data;
			for (let i = 0; i < res.result.data.length; i++) {
				const item = {
					values: false,
					id: res.result.data[i]._id
				};
				delIndex.value.push(item);
			}
		});
	};

	const delBuilding = () => {
		console.log("delBuilding", delIndex.value);
		const delId = [];
		for (let i = 0; i < delIndex.value.length; i++) {
			if (delIndex.value[i].values) {
				delId.push(delIndex.value[i].id);
			}
		}
		console.log("delId", delId);

		if (delId.length) {
			uni.showModal({
				title: "注意！！！确定删除该楼房吗?",
				content: "删除后，跟该楼房有关的房间，水电，账单，营收都会删除！！！请务必谨慎！！！",
				confirmText: "确认删除",
				success: (res) => {
					if (res.confirm) {
						api.deleteBuilding(delId).then(res => {
							if (res.code === 1) {
								uni.showToast({
									title: '删除成功',
									icon: 'none'
								});
							}
						}).finally(res => {
							getBuilding();
						});
					}
				}
			});
		} else {
			uni.showToast({
				title: '至少选择一个删除项',
				icon: 'none'
			});
		}
	};
</script>

<style lang="scss">
	.container {
		padding: 15rpx;

		.toolbar {
			display: flex;
			justify-content: space-between;
			align-content: center;
			height: 60rpx;
			margin-bottom: 20rpx;
		}
	}
</style>