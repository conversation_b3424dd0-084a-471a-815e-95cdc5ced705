<template>
	<view class="container">
		<uni-section title="选择房子和房间" type="line" class="top">
			<view class="select">
				<view style="width: 100%;display: flex;">
					<uni-data-select v-model="buildingId" :localdata="houses" placeholder="选择房子" @change="houseChange"
						class="house"></uni-data-select>

					<uni-data-select v-model="roomName" :localdata="rooms" placeholder="选择房间" @change="roomChange"
						class="room"></uni-data-select>
				</view>
				<button type="primary" size="mini" ref="add" @click="check" style="width: 200rpx;">查水电</button>

			</view>

		</uni-section>

		<view v-for="(item,index) in list" :key="index">
			<uni-card :title="item.month+'月'" :sub-title="'水电模板：'+item.mode_name" :extra="item.year+''">
				<uni-section title="水表数" type="circle"><template v-slot:right>
						{{item.water/100}}
					</template></uni-section>
				<uni-section title="水表损耗" type="circle"><template v-slot:right>
						{{item.loss_water/100}}
					</template></uni-section>
				<uni-section title="电表数" type="circle"><template v-slot:right>
						{{item.ele/100}}
					</template></uni-section>
				<uni-section title="电表损耗" type="circle"><template v-slot:right>
						{{item.loss_ele/100}}
					</template></uni-section>
			</uni-card>
		</view>
		<view style="display: flex;justify-content: center;">
			<text style="display: flex; font-size: 25rpx;color: gray;padding: 10rpx;">
				---------------{{tip}}----------------</text>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import {
		onReachBottom
	} from '@dcloudio/uni-app'
	const db = uniCloud.database()
	const api = uniCloud.importObject("ApiFuntion")

	// 定义响应式数据
	const buildingId = ref('') // 房子ID
	const houses = ref([]) // 房子列表
	const rooms = ref([]) // 房间列表
	const roomName = ref('') // 选择的房间名
	const roomId = ref('') // 选择的房间ID
	const roomIndex = ref(0)
	const roomList = ref([])
	const page = ref(0)
	const list = ref([])
	const hasMore = ref(false)
	const tip = ref("已经到底了")
	const name = ref("12")
	const enable = ref(false)
	const uid = ref('')

	// 生命周期钩子
	onMounted(async (e) => {
		uid.value = e.uid
		await getBuilding()

		if (houses.value.length) {
			enable.value = true
			console.log("房子非空的")
		} else {
			console.log("房子空的")
			enable.value = false
		}
	})

	// 触底加载
	onReachBottom(() => {
		if (hasMore.value) {
			getBill()
		}
	})

	// 获取房子列表
	const getBuilding = async () => {
		await api.getBuilding(uid.value).then(res => {
			for (let i = 0; i < res.data.length; i++) {
				const item = {
					value: res.data[i]._id,
					text: res.data[i].name
				}
				houses.value.push(item)
			}
		})
	}

	// 房子选择变化
	const houseChange = (e) => {
		console.log("选择房子", e)
		roomList.value = []
		rooms.value = []
		roomName.value = ''

		if (e) {
			buildingId.value = e

			db.collection("fangke_room").where(`building_id=="${buildingId.value}"`).get({
				building_id: buildingId.value
			}).then(res => {
				console.log('查找房间', res.result.data)
				roomList.value = res.result.data

				for (let i = 0; i < res.result.data.length; i++) {
					const item = {
						value: i + 1,
						text: res.result.data[i].name
					}
					console.log('item', item)
					rooms.value.push(item)
				}
				console.log('rooms', roomList.value)
			})
		} else {
			buildingId.value = ''
			console.log("rooms", rooms.value)
		}
	}

	// 房间选择变化
	const roomChange = (e) => {
		console.log("roomChange", roomList.value)

		if (e) { // e为0时代表取消
			roomIndex.value = e - 1
			roomId.value = roomList.value[roomIndex.value]._id
			console.log("roomChange", roomId.value)
		} else {
			roomId.value = ''
			roomName.value = ''
		}
	}

	// 检查并获取账单
	const check = () => {
		console.log("addRoom", "roomID :" + roomId.value + "buildingId :" + buildingId.value)

		if (roomId.value !== '') {
			getBill()
		} else {
			uni.showToast({
				title: `请先选择房子和房间`,
				icon: 'none'
			})
		}
	}

	// 获取水电账单
	const getBill = () => {
		api.getShuiDianBill(roomId.value, page.value).then(res => {
			console.log("getShuiDianBill", res)

			if (res.data.length) {
				res.data.forEach(element => {
					list.value.push(element)
				})

				if (res.data.length < 10) {
					hasMore.value = false
					tip.value = "已经到底了"
				} else {
					hasMore.value = true
					page.value += 10
					tip.value = "下拉加载"
				}
			} else {
				uni.showToast({
					title: '暂时无数据',
					icon: 'none'
				})
			}
		})
	}

	// 暴露方法给模板使用
	defineExpose({
		houseChange,
		roomChange,
		check
	})
</script>

<style lang="scss">
	.container {
		margin-left: 15rpx;
		margin-right: 15rpx;
		margin-bottom: 30rpx;
	}

	.top {
		.select {
			display: flex;
			padding: 10rpx;

			.house {
				margin-right: 10rpx;
			}

			.room {
				margin-right: 10rpx;
			}
		}
	}
</style>