<template>
	<view class="t-wrap">
		<view class="t-collapse" v-if="renderData && renderData.length>0" :style="{'left':isComputed?'0rpx':'99999px'}">
			<view class="t-collapse-item" v-for="(item,index) in renderData" :key="index">
				<view :class="['t-collapse-title',{'open':item.open}]"
					:style="{'border-bottom': item.open?'1px solid #dedede':'none','font-size':fontSize+'rpx','color':color, 'font-weight':item.bold?'bold':'400'}"
					@click="itemClick(index)">
					<view style="display:flex;width: 100%;justify-content: space-between;">
						<text>{{item.title}}</text>
						<text style="margin-right: 20rpx;">{{item.right}}</text>
					</view>
					<view v-if="!item.hideArrow===true" :class="['t-icon iconfont','icon-arrow',{'open':item.open}]"
						:style="{'color':arrowColor,'font-size':(fontSize*1.5)+'rpx'}"></view>
				</view>
				<view :class="['t-collapse-wrap',{'chide':!item.open}]" :style="{'height':item.height}">
					<view :class="['t-collapse-content','t-cn-'+index]"
						:style="{'font-size':contentFontSize+'rpx','color':contentColor}">
						<view v-if="item.table===true" class="t-table">
							<view class="t-head-con">
								<view class="t-head" :style="{'width':'calc(100% / '+item.content.head.length+')'}"
									v-for="(thd,index0) in item.content.head" :key="index0">{{thd}}</view>
							</view>
							<view class="t-tr" v-for="(trow,index1) in item.content.data" :key="index1"
								:style="{'border-bottom':item.content.bordered===true?'1rpx solid #e6e6e6':'none'}">
								<view :style="{'width':'calc(100% / '+trow.length+')'}" class="t-td"
									v-for="(tad,index2) in trow" :key="index2">{{tad}}</view>
							</view>
						</view>
						<view v-else-if="item.list==true" class="t-list">
							<view class="u_list" v-for="(th,index2) in item.content.body" :key="index2">
								<view class="list_left">
									{{item.content.body[index2].name}}
								</view>
								<view class="list_right">
									{{item.content.body[index2].value+''}}
								</view>
							</view>
							<!-- <uni-list class="u_list" >
								<uni-list-item class="u_item" v-for="(th,index2) in item.content.body" :key="index2"
									:title="item.content.body[index2].name"
									:right-text="item.content.body[index2].value+''"
									></uni-list-item>
							</uni-list> -->
						</view>
						<text v-else>{{item.content}} </text>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script setup>
	import {
		ref,
		watch,
		onMounted,
		nextTick
	} from 'vue';

	const props = defineProps({
		/**
		 * 是否开启手风琴效果
		 */
		accordion: {
			type: Boolean,
			default: true
		},
		/**
		 * 数据
		 */
		collapseData: {
			type: Array,
			default: () => []
		},
		/**
		 * 是否有边框
		 */
		bordered: {
			type: Boolean,
			default: true
		},
		/**
		 * 标题文字大小，单位rpx
		 */
		fontSize: {
			type: Number,
			default: 32
		},
		/**
		 * 标题文字颜色
		 */
		color: {
			type: String,
			default: '#333333'
		},
		/**
		 * 箭头颜色
		 */
		arrowColor: {
			type: String,
			default: '#333'
		},
		/**
		 * 内容文字大小
		 */
		contentFontSize: {
			type: Number,
			default: 32
		},
		/**
		 * 内容文字颜色
		 */
		contentColor: {
			type: String,
			default: '#666'
		}
	});

	const emit = defineEmits(['itemClick']);

	// 响应式数据
	const isComputed = ref(false);
	const renderData = ref(JSON.parse(JSON.stringify(props.collapseData)));

	// 初始化折叠面板
	const initCollapse = () => {
		nextTick(() => {
			let arr = renderData.value;
			if (arr && arr.length > 0) {
				let is = props.accordion;
				let find = false;
				for (let i = 0; i < arr.length; i++) {
					// 手风琴效果适配
					if (is && arr[i]['open']) {
						if (!find) {
							find = true;
						} else {
							renderData.value[i].open = false;
						}
					}
					// 更新高度
					updateItemHeight(i, true);
				}
				nextTick(() => {
					isComputed.value = true;
				});
			}
		});
	};

	// 点击折叠面板项
	const itemClick = (index) => {
		if (renderData.value[index]['disabled']) {
			return;
		}
		renderData.value[index].open = !renderData.value[index].open;
		updateItemHeight(index);
		emit("itemClick", {
			index,
			open: renderData.value[index]['open']
		});
	};

	// 更新项高度
	const updateItemHeight = (index, isInit) => {
		// #ifdef MP-ALIPAY
		uni.createSelectorQuery().in().select('.t-cn-' + index).fields({
			size: true
		}, d => {
			renderData.value[index].height = renderData.value[index].open ? (d.height + "px") : "0rpx";
		}).exec();
		// #endif

		// #ifndef MP-ALIPAY
		uni.createSelectorQuery().select('.t-cn-' + index).fields({
			size: true
		}, d => {
			renderData.value[index].height = renderData.value[index].open ? (d.height + "px") : "0rpx";
			// 手风琴适配
			if (renderData.value[index].open && props.accordion && !isInit) {
				closeOther(index);
			}
		}).exec();
		// #endif
	};

	// 关闭其他面板
	const closeOther = (idx) => {
		let arr = renderData.value;
		for (let i = 0; i < arr.length; i++) {
			if (i != idx && arr[i]['open']) {
				arr[i].open = false;
				updateItemHeight(i);
			}
		}
	};

	// 监听props变化
	watch(() => props.collapseData, (newVal) => {
		renderData.value = JSON.parse(JSON.stringify(newVal));
		isComputed.value = false;
		initCollapse();
	});

	// 组件挂载后初始化
	onMounted(() => {
		if (props.collapseData && props.collapseData.length > 0) {
			initCollapse();
		}
	});
</script>
<style lang="scss" scoped>
	@import '@/uni_modules/kevy-collapse/static/icon/iconfont.css';

	.t-wrap {
		width: 100%;
		box-sizing: border-box;

		.t-collapse {
			width: 100%;
			box-sizing: border-box;
			position: relative;
			right: 99999px;

			.t-collapse-item {
				margin: 10rpx 0rpx;
				border: 2rpx solid #dedede;
				border-radius: 20rpx;
				background-color: white;

				.t-collapse-title {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					color: rgba(0, 0, 0, .6);
					-webkit-transition: background-color .15s ease-out;
					transition: background-color .15s ease-out;
					padding: 25rpx 40rpx 25rpx 20rpx;
					position: relative;
					font-weight: 400;
					/*  #ifdef  H5  */
					cursor: pointer;
					/*  #endif  */
					// font-size: 28rpx;
					box-sizing: border-box;

					&.open {
						background-color: #f5f5f5;
						// color: rgba(0, 0, 0, .8);
					}

					.t-icon {
						font-size: 48rpx;
						position: absolute;
						right: 0rpx;
						top: 0rpx;
						height: 100%;
						display: flex;
						flex-direction: row;
						justify-content: center;
						align-items: center;
						font-weight: 400 !important;

						&.open {
							&::before {
								transform: rotate(90deg);
							}
						}
					}
				}

				.t-collapse-wrap {
					margin: 0 !important;
					padding: 0 !important;
					border: none !important;
					box-sizing: border-box;
					transition: height .2s linear;
					overflow: hidden;

					.t-collapse-content {
						// color: #666;
						margin-top: 0;
						padding: 16rpx 20rpx 24rpx;
						// font-size: 28rpx;
						white-space: pre-line;
					}

					&.chide {
						height: 0rpx;

						.t-collapse-content {
							overflow: hidden;
						}
					}

					.t-table {
						.t-head-con {
							display: flex;
							flex-direction: row;
							border-bottom: 1rpx solid #e6e6e6;

							.t-head {
								text-align: center;
								font-weight: bold;
								padding: 10rpx 6rpx;
							}
						}

						.t-tr {
							display: flex;
							flex-direction: row;

							&:last-child {
								border-bottom: none !important;
							}

							.t-td {
								text-align: center;
								padding: 10rpx 6rpx;

							}
						}
					}

					.t-line {
						.t-head-con {
							display: flex;
							flex-direction: row;
							border-bottom: 1rpx solid #e6e6e6;

							.t-head {
								text-align: center;
								font-weight: bold;
								padding: 10rpx 6rpx;
							}
						}

						.t-tr {
							display: flex;
							flex-direction: row;

							&:last-child {
								border-bottom: none !important;
							}

							.t-td {
								text-align: center;
								padding: 10rpx 6rpx;

							}
						}

						// .t-list{
						// 	.u_list{
						// 		display: flex;
						// 		justify-content: space-between;
						// 		.u_item{
						// 			display: flex;
						// 			justify-content: space-between;
						// 			padding: 10rpx;
						// 			.list_left{
						// 				display: flex;
						// 				color: black;
						// 			}
						// 			.list_right{
						// 				display: flex;
						// 				color: #999999;
						// 				&:last-child {
						// 					color: red;
						// 				}
						// 			}

						// 		}
						// 	}
						// }
					}

					.t-list {
						.u_list {
							display: flex;
							justify-content: space-between;
							padding: 10rpx;

							.list_left {
								display: flex;
								color: black;
							}

							.list_right {
								display: flex;
								font-weight: 550;
								color: black;
								// &:last-child {
								// 	color: red;
								// }
							}

						}
					}
				}



				.t-collapse-hide {
					height: 0rpx;
					overflow: hidden;
				}

				.t-collapse-visible {
					height: 0rpx;
					overflow: hidden;
				}

				// .t-td {
				// 	text-align: center;
				// 	padding: 2rpx 6rpx;
				// 	font-size: 20rpx;
				// 	white-space: normal;
				// 	word-break: break-all;
				// 	overflow-wrap: break-word;
				// 	background-color: yellow;
				// }
			}
		}
	}
</style>