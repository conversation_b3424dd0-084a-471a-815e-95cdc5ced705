// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "流水账单",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"name": {
			"bsonType": "string",
			"title": "账单名称",
			"defaultValue": ""
		},
		"room_id": {
			"title": "房间id",
			"bsonType": "string",
			"description": "房间id",
			"foreignKey": "fangke_room._id"
		},
		"type": {
			"bsonType": "int",
			"label": "流水类型",
			"enum": [{
					"value": 1,
					"text": "押金"
				},
				{
					"value": 2,
					"text": "账单"
				},
				{
					"value": 3,
					"text": "退租"
				},
				{
					"value": 4,
					"text": "换房"
				},
				{
					"value": 5,
					"text": "维修"
				},
				{
					"value": 6,
					"text": "保洁"
				},
				{
					"value": 7,
					"text": "其他"
				}
			]
		},
		"building_id": {
			"title": "楼房ID",
			"bsonType": "string",
			"description": "楼房ID",
			"foreignKey": "fangke_building._id"
		},
		"address": {
			"title": "地址",
			"bsonType": "string",
			"description": "房间地址"
		},
		"create_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"paymentMethod": {
			"bsonType": "string",
			"description": "收取方式",
			"label": "收取方式",
			"defaultValue": "",
			"foreignKey": "fangke_cost_type.name"
		},
		"handler": {
			"bsonType": "string",
			"label": "经办人手机号",
			"defaultValue": ""
		},
		"amount": {
			"bsonType": "int",
			"label": "实收金额",
			"defaultValue": 0
		},
		"desc": {
			"bsonType": "string",
			"label": "备注",
			"defaultValue": ""
		},
		"isPay": {
			"bsonType": "bool",
			"description": "是否是收入",
			"label": "是否是收入",
			"defaultValue": false
		},
		"images": {
			"bsonType": "array",
			"description": "凭证图片",
			"fileMediaType": "image",
			"title": "凭证图片"
		},
		"update_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"bill": {
			"bsonType": "string",
			"label": "账单ID",
			"defaultValue": "",
			"description": "对应的账单ID",
			"foreignKey": "fangke_room_bill._id"
		},
		"detail": {
			"bsonType": "array",
			"arrayType": "object",
			"label": "支付明细"
		},
		"paymentDate": {
			"bsonType": "string",
			"label": "收款时间"
		},
		"room_name": {
			"bsonType": "string",
			"label": "房间名",
			"defaultValue": ""
		},
		"tenant_name": {
			"bsonType": "string",
			"label": "承租人",
			"description": "出租人名字",
			"defaultValue": ""
		}

	}
}