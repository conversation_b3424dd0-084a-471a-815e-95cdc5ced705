<template>
	<view class="main">
		<!-- #ifdef MP-WEIXIN -->
		<custom-nav-bar logo="智小居" placeholder="搜索目的地/攻略/好物" :showLeftIcon="true" showRightIcon
			rightIconType="notification" showSearch="true"></custom-nav-bar>
		<!-- #endif -->
		<view class="content">
			<view class="ad">
				<swiper class="swiper" circular indicator-dots autoplay>
					<swiper-item v-for="(item,index) in banners" :key="index">
						<image :src="item.image" style="width: 100%;height: 100%;border-radius: 20rpx;"
							mode="scaleToFill">
						</image>
					</swiper-item>
				</swiper>
			</view>
			<view class="fun">
				<view class="leftTab">
					<countDown ref="down" :widths='300' :breadth='30' activeColor='#01B862' defaultColor='#EDF0F0'
						:times='times' @endTime='endTime' v-on:pauseNum="pauseNums">
						<view class="container">
							<text>{{month}}月营收情况</text>
							<view style="display: flex;justify-content: center;font-size: 25rpx;">
								<text>{{revenue}}/{{total}}</text>
							</view>
						</view>
					</countDown>
				</view>
				<view class="rightTab" v-for="(item,tabIndex) in topList" :key="tabIndex">
					<bage :bag="topList[tabIndex]" @onCallback="onBageEvent(tabIndex)"></bage>
				</view>
			</view>

			<view class="task">
				<view class="title">
					<view class="left">待办</view>
					<view class="right">预定{{dueCount.length+due7dayCount.length+overdueCount.length+contractDueCount.length}}笔</view>
				</view>
				<view class="manage">
					<view class="tab" v-for="(item,taskIndex) in taskList" :key="taskIndex" @click="manager(taskIndex)">
						<view class="num">
							<text style="font-size: 50rpx;">{{item.count}}</text><text
								style="font-size: 30rpx;margin-left: 10rpx;">{{item.tip}}</text>
						</view>
						<view class="name">
							<text>{{item.name}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="vip-card" @click="joinVip">
				<view class="vip-info">
					<view class="vip-title">开通线上收租 </view>
					<view class="vip-desc">一键催缴 · 招租推广 · 安全省钱</view>
				</view>
				<button class="vip-btn">立即开通</button>
			</view>
			<view class="task">
				<view class="title">
					<view class="left">运营</view>
					<view class="right">查看月报</view>
				</view>
				<view class="row">
					<view class="t_head" style="background-color: #3498DB;">入住</view>
					<view class="group">
						<view class="box">
							<view class="t1">{{rz_num1}}</view>
							<view class="t2">当前已租</view>
						</view>
						<view class="box">
							<view class="t1">{{rz_num2}}</view>
							<view class="t2">当前未租</view>
						</view>
						<view class="box">
							<view class="t1">{{rz_num3}}</view>
							<view class="t2">当前出租率</view>
						</view>
					</view>

				</view>
				<view class="row">
					<view class="t_head" style="background-color: #E74C3C;">合同</view>
					<view class="group">
						<view class="box">
							<view class="t1">{{ht_num1}}</view>
							<view class="t2">本月新签</view>
						</view>
						<view class="box">
							<view class="t1">{{ht_num2}}</view>
							<view class="t2">本月退租</view>
						</view>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed,
		reactive
	} from 'vue';
	import {
		onShow,
		onHide,
		onBackPress,
		onReachBottom,
		onLoad,
	} from "@dcloudio/uni-app"
	import countDown from '@/components/zh-countDown/countDown/countDown.vue';
	const db = uniCloud.databaseForJQL();
	// 定义响应式数据
	import {
		store2
	} from '@/utils/js/store.js'
	import {
		dayjs
	} from '../../utils/dayjs.min.js';
	const isLogin = computed(() => store2.hasLogin)
	const userInfo = computed(() => store2.userInfo)
	const searchName = ref("");
	const banners = ref([]);
	const billList = ref([]) //订单
	const contract = ref([]) //合约
	const dueCount = ref([]) //待收
	const due7dayCount = ref([]) //未来7天
	const overdueCount = ref([]) //逾期
	const contractDueCount = ref([]) //快到期
	const topList = ref([{
			name: '租约',
			count: 0,
			icon: "/static/home_add.png"
		},
		{
			name: '抄表',
			count: 0,
			icon: "/static/home_shuidian.png"

		},
		{
			name: '账单',
			count: 0,
			icon: "/static/home_task.png"
		},
		{
			name: '流水',
			count: 0,
			icon: "/static/home_bill.png"
		},
		{
			name: '会员',
			count: 0,
			icon: "/static/home_delete.png"
		},
		{
			name: '开门',
			count: 0,
			icon: "/static/home_tag.png"
		},
		{
			name: '审核',
			count: 0,
			icon: "/static/home_notice.png"
		}, {
			name: '更多',
			count: 0,
			icon: "/static/home_start.png"
		}
	]);
	const taskList = ref([]);
	const tabList = ref([]);
	const tabIndex = ref(0); //当前显示的房子
	const times = ref(3000);
	const total = ref(0); //一共应收的房租
	const revenue = ref(0); //收到的房租
	const buildingId = ref(0);
	const buildingList = ref([]);
	const building_list = ref([])
	const month = ref(1);
	const year = ref(2000)
	const rz_num1 = ref(0);
	const rz_num2 = ref(0);
	const rz_num3 = ref(0);
	const ht_num1 = ref(0);
	const ht_num2 = ref(0);
	const yearMonth = ref('')
	const down = ref(null);
	// onLoad 钩子
	onLoad(async() => {
		// uni.authorize({
		// 	scope: 'scope.userLocation',
		// 	success: () => {
		// 		console.log("获取定位权限");
		// 		getLoaction()
		// 	},
		// 	fail: (err) => {
		// 		console.log('获取定位权限失败', err);
		// 	}
		// })
		console.log("onload");
		const now = new Date();
		year.value = now.getFullYear()
		month.value = now.getMonth() + 1;
		if (month.value < 10) {
			yearMonth.value = year.value + "-0" + month.value
		} else {
			yearMonth.value = year.value + "-" + month.value
		}
		console.log("yearMonth", yearMonth.value);
		getbanner();
		checkToken()
		if (isLogin.value) {
			uni.showLoading()
			console.log("isLogin.value", isLogin.value);
			await getRevenue()
			await getTask()
			await getOperation()
			uni.hideLoading()
		}
	});

	const getLoaction = () => {
		uni.getLocation({
			type: 'gcj02',
			geocode: true,
			success: (res) => {
				console.log('获取位置信息成功', res);
			},
			fail: (err) => {
				console.log('获取位置信息失败', err);
			}
		})
	}

	const checkToken = () => {
		let token = uni.getStorageSync("uni_id_token")
		console.log("获取token", token);
		console.log("是否登录", isLogin.value)
		console.log("手机号", userInfo.value.mobile);
	}

	// onShow 钩子
	onShow(async () => {
		console.log("onShow");
		
	});


	// onBackPress 钩子
	onBackPress(() => {
		// 这里假设 $refs.fab 存在相应逻辑，需要根据实际情况修改
		// if (down.value.isShow) {
		//     down.value.close();
		//     return true;
		// }
		return false;
	});

	// 触底方法
	onReachBottom(() => {

	});

	// 定义方法
	const startCounting = () => { //开始事件
		let f = (revenue.value / total.value);
		console.log("f", f);
		let current = (times.value - (times.value * f));
		console.log("current", current);
		down.value.start(current);
	};

	const pause = () => { //暂停
		if (down.value) {
			down.value.pause();
		}
	};

	const endTime = () => { //结束
		pause();
	};

	const pauseNums = (e) => {
		console.log("pauseNums", e);
	};


	const getbanner = async () => { //
		banners.value = [];
		let data = {}
		try {
			let res = await uni.$lkj.api.getBanner(data)
			console.log("getbanner", res.data);
			banners.value = res.data
			console.log("banners", banners.value);
		} catch (error) {
			console.log("请求失败", error);
		}

	};

	const joinVip = () => {
		uni.navigateTo({
			url: '/vip/joinVip/joinVip'
		})
	}

	const getBuilding = async () => {
		let params = {
			params: {
				uid: userInfo.value._id
			}
		}
		console.log("userInfo.value.uid", userInfo.value._id);
		try {
			let res = await uni.$lkj.api.getBuilding(params)
			console.log("getBuilding success", res);
			buildingList.value = [...res.data]
			return res.data.map(item =>(item.id));
		} catch (err) {
			console.log("getBuilding err", err);
			return []
		}

		// await getRevenue();
		// await getTimeout();
		// await getTask();
		// await getOperation();
	};

	const getRevenue = async () => {
		dueCount.value = []
		due7dayCount.value = []
		overdueCount.value = []
		contractDueCount.value = []
		building_list.value = await getBuilding()
		if (building_list.value.length > 0) {
			console.log("building_list", building_list.value);
			try {
				let sum = 0
				let money = 0
				let params = {
					building_list: building_list.value,
					month: month.value,
					year: year.value
				}
				const escapedYearMonth = yearMonth.value.replace(/-/g, '\\-'); // 转义输入中的特殊字符（如 -）
				// ${new RegExp(`^${escapedYearMonth}-\\d{2}$`)}.test(day) 
				billList.value = await db.collection("fangke_room_bill").where(
					`building_id in ['${building_list.value.join("','")}']`).field(
					"_id,status,day,sum,money,name,create_time").get().then(res => {
					console.log("获取全部账单：", res);
					if (res.errCode == 0) {
						let reg = new RegExp(`^${escapedYearMonth}-\\d{2}$`) //判断本月的订单
						res.data.forEach(item => {
							if (reg.test(item.day)) {
								sum = sum + item.sum
								money = money + item.money
								console.log("获取本月待收订单",item);
								if (item.status > 0) { //获取本月待收订单
									dueCount.value.push({
										id: item._id
									})
								}

							}

						})

						return res.data
					}
					return []
				})
				
				await db.collection("fangke_room_bill").where(`room_id == "68595a3d2eea65b0f195cc06" && ${new RegExp(`^${escapedYearMonth}-\\d{2}$`)}.test(day)`).get().then(res =>{
					console.log("获取本月指定房间账单",res.data);
				})
				
				console.log("获取收益", money);
				total.value = sum / 100;
				revenue.value = money / 100;
				if (total.value) {
					startCounting();
				}

			} catch (err) {
				console.log("getRevenue err", err);
			}
		}
	};

	const trigger = (e) => {
		console.log(e);
		switch (e.index) {
			case 0:
				console.log('跳转0');
				uni.navigateTo({
					url: "/pagesB/add/addHouse"
				});
				break;
			case 1:
				console.log('跳转1');
				uni.navigateTo({
					url: "/pagesB/add/addRoom"
				});
				break;
			case 2:
				console.log('跳转2');
				uni.navigateTo({
					url: "/pagesB/addTask/addTask?uid=" + _uid.value
				});
				break;
		}
	};

	const addHouse = () => {
		uni.navigateTo({
			url: "/pagesB/add/addHouse"
		});
	};

	const getTimeout = () => {
		console.log("getTimeout", buildingList.value);
		// api.getTimeoutRoom(buildingList.value).then(res => {
		// 	console.log('getTimeoutRoom', res.data.count);
		// 	if (res.code) {
		// 		topList.value[3].count = res.data.count;
		// 	}
		// });
	};

	const onBageEvent = async (index) => {
		uni.showToast({
			title: `点击了${index}`,
			icon: 'none'
		});
		console.log("onBageEvent", index);
		switch (index) {
			case 0:
				uni.navigateTo({
					url: `/pagesB/contracts/contracts?uid=`+userInfo.value._id,
					success(res) {
						// 通过eventChannel向被打开页面传送数据
						    res.eventChannel.emit('acceptDataFromOpenerPage', buildingList.value)
					}
				});
				break;
			case 1:
				uni.navigateTo({
					url: `/pagesB/chooseHouse/chooseHouse`,
					success(res) {
						// 通过eventChannel向被打开页面传送数据
						    res.eventChannel.emit('acceptDataFromIndex', buildingList.value)
					}
				});
				break;
			case 2:
				uni.navigateTo({
					url: `/pagesB/bill/bill?uid=`+userInfo.value._id+`&type=0`,
					success(res) {
						res.eventChannel.emit('acceptDataFromBill', buildingList.value)
					}
				});
				break;
			case 3:
				uni.navigateTo({
					url: `/pagesB/revenue/revenue?uid=`+userInfo.value._id,
					success(res) {
						res.eventChannel.emit('acceptDataFromRevenue', buildingList.value)
					}
				});
				break;
			case 4:
				console.log("vipinfo",buildingList.value);
				uni.navigateTo({
					url: `/vip/vipInfo/vipInfo?uid=`+userInfo.value._id,
					success(res) {
						res.eventChannel.emit('acceptDataFromIndex', buildingList.value)
					}
				});
				break;
			case 5:
				uni.navigateTo({
					url: `/pagesB/doorLock/doorLock?uid=`+userInfo.value._id,
					success(res) {
						res.eventChannel.emit('acceptDataFromRevenue', buildingList.value)
					}
				});
				break;
			case 6:
				console.log("点击通知");
				uni.navigateTo({
					url: `/pagesB/auditCenter/auditCenter?uid=`+userInfo.value._id,
					success(res) {
						res.eventChannel.emit('acceptDataFromRevenue', buildingList.value)
					}
				});
				break;
			case 7:
				console.log("点击租客");
				// await uniCloud.callFunction({
				//     name: 'resetRoom',
				// }).then(res => {
				//     console.log("resetRoom", res);
				// })
				// getBuilding();
				// uni.navigateTo({
				//     url: `/pagesB/billInfo/createRoomBill?uid=6759268eee97ef02f0b6fd66`
				//     // url: `/pagesB/billInfo/billInfo`
				// })
				uni.navigateTo({
					url: `/pagesB/auditCenter/submitAudit?uid=`+userInfo.value._id,
					success(res) {
						res.eventChannel.emit('acceptDataFromRevenue', buildingList.value)
					}
				});
				break;
		}
	};

	const getTask = async () => {
		billList.value.forEach(item => {
			// console.log("是否7天内",dayjs('2025-06-21').isBetween(dayjs(), dayjs().add(7,'day')));
			if (dayjs(item.day).isBefore(dayjs().add(7, 'day')) && dayjs(item.day).isAfter(dayjs()) && item
				.status > 0) {
				console.log("isBetween", item.day);
				due7dayCount.value.push({
					id: item._id
				})
			}
			if (item.status == 2) {
				overdueCount.value.push({
					id: item._id
				})
			}
		})
		console.log("dueCount", dueCount.value);
		taskList.value.push({
			name: "本月待收",
			count: dueCount.value.length,
			tip: "笔"
		})
		taskList.value.push({
			name: "未来7天",
			count: due7dayCount.value.length,
			tip: "笔"
		})
		taskList.value.push({
			name: '逾期',
			count: overdueCount.value.length,
			tip: "笔"
		})
		let checkinNum = 0
		ht_num1.value = 0
		contract.value = await db.collection("fangke_contracts").where(
			`building_id in ['${building_list.value.join("','")}'] && status == 1`).field(
			"_id,end_time,building_id,room_id,create_time").get().then(res => {
			console.log("在租的合约", res);
			if (res.errCode == 0) {
				const escapedYearMonth = yearMonth.value.replace(/-/g, '\\-'); // 转义输入中的特殊字符（如 -）
				let reg = new RegExp(`^${escapedYearMonth}-\\d{2}$`) //判断本月的订单
				// res.data.forEach(item =>{

				// })
				for (const item of res.data) {
					if (dayjs().diff(item.end_time, "month") >= -1) {
						contractDueCount.value.push({
							id: item._id
						})
					}
					let createTime = dayjs(item.create_time).format('YYYY-MM-DD')
					console.log("createTime", createTime);
					if (reg.test(createTime)) {
						console.log("符合时间", createTime);
						// checkinNum ++
						ht_num1.value++
					}
				}

				return res.data
			} else {
				return []
			}
		})

		taskList.value.push({
			name: '待支付',
			count: contractDueCount.value.length,
			tip: "个"
		})
		taskList.value.push({
			name: '即将到期',
			count: contractDueCount.value.length,
			tip: "个"
		})
		taskList.value.push({
			name: '保修',
			count: 0,
			tip: "单"
		})
		taskList.value.push({
			name: '保洁',
			count: 0,
			tip: "单"
		})

		// api.getTask(task).then(res => {
		// 	console.log("获取任务", res);
		// 	if (res.code) {
		// 		topList.value[2].count = res.data.length;
		// 	}
		// });
	};

	const manager = (index) => {
		if (index === 0) {
			uni.navigateTo({
				url: `/pagesB/bill/bill?uid=`+userInfo.value._id+`&type=0`,
				success(res) {
					res.eventChannel.emit('acceptDataFromBill', buildingList.value)
				}
			});
		} else if (index === 1) {
			uni.navigateTo({
				url: `/pagesB/bill/bill?uid=`+userInfo.value._id+`&type=2`,
				success(res) {
					res.eventChannel.emit('acceptDataFromBill', buildingList.value)
				}
			});
		} else if (index === 2) {
			uni.navigateTo({
				url: `/pagesB/bill/bill?uid=`+userInfo.value._id+`&type=3`,
				success(res) {
					res.eventChannel.emit('acceptDataFromBill', buildingList.value)
				}
			});
		} else if (index === 3) {
			uni.navigateTo({
				url: "/pagesB/invite/invite"
			});
		}
		else if (index === 4) {
			uni.navigateTo({
				url: "/pagesB/invite/invite"
			});
		}
		else if (index === 5) {
			uni.navigateTo({
				url: "/pagesB/invite/invite"
			});
		}
	};

	const getOperation = async () => {
		if (building_list.value.length > 0) {
			console.log("building_list ==>", building_list.value);
			let params = {
				building_list :building_list.value
			}
			let res = await uni.$lkj.api.getAnalysis(params)
			if (res.errCode == 0) {
				console.log("getAnalysis", res.data);
				rz_num1.value = res.data.rent_room_count;
				rz_num2.value = res.data.all_room_count - res.data.rent_room_count
				rz_num3.value = res.data.percentage
				// ht_num1.value = res.data.checkin_count // 注释掉，使用getTask中的计算值
				ht_num2.value = res.data.checkout_count
			}


		}

	};
</script>

<style lang="scss" scoped>
	@import "../../common/style/base-style.scss";

	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
	}

	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}

	.main {
		flex: 1;
		// height: 100vh;
		// background-color: #eeeeee;
		background:
			linear-gradient(to bottom, transparent, #fff 500rpx),
			linear-gradient(to right, #beecd8 20%, #F4E2D8);
		min-height: 80vh;

		.popup-content {
			@include flex;
			align-items: center;
			justify-content: center;
			padding: 10px;
			height: 80px;
			margin-top: 100rpx;
			background-color: #fff;
		}

		.popup-height {
			@include height;
			width: 200px;
		}


		.content {
			display: flex;
			flex-direction: column;
			height: 100%;
			box-sizing: border-box;

			.search {
				background-color: #65A548;
				display: flex;
				align-content: center;

				.usearch {
					background-color: #65A548;
					display: flex;
					align-items: center;
					box-sizing: border-box;
					width: 100%;
				}
			}

			.vip-card {
				background-color: rgba(0, 0, 0, 0.6);
				border-radius: 16rpx;
				padding: 20rpx 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 10rpx 30rpx;
			}

			.vip-info {
				color: #fff;
			}

			.vip-title {
				font-size: 16px;
				margin-bottom: 10rpx;
			}

			.vip-desc {
				font-size: 12px;
				opacity: 0.8;
			}

			.vip-btn {
				background-color: #FFD700;
				color: #333;
				font-weight: bold;
				font-size: 14px;
				padding: 0 30rpx;
				height: 60rpx;
				line-height: 60rpx;
				border-radius: 30rpx;
			}

			.ad {
				box-sizing: border-box;
				width: 100%;
				margin: 10rpx 0;

				.swiper {
					height: 250rpx;
					margin: 0 20rpx;
				}

			}

			.fun {
				display: grid;
				// background-color: white;
				grid-template-columns: auto auto auto auto;
				grid-gap: 2rpx;
				margin: 0 20rpx;
				// border: 1rpx solid #eee;
				box-sizing: border-box;
				border-radius: 20rpx;

				// box-shadow: 1px 1px 5px gray;
				.leftTab {
					display: flex;
					grid-area: 1 / 1 / span 2 / span 2;
					justify-content: center;
					align-items: center;

					.container {
						display: flex;
						flex-direction: column;
						justify-content: center;
					}
				}

				.rightTab {
					align-items: center;
					display: flex;
					justify-content: center;
				}
			}

			.task {
				display: flex;
				flex-direction: column;

				.title {
					display: flex;
					justify-content: space-between;
					padding: 20rpx;
					margin-left: 10rpx;
					align-items: baseline;

					.left {
						font-size: 40rpx;
						font-weight: 550;
					}

					.right {
						font-size: 28rpx;
						margin-right: 20rpx;
					}
				}

				.manage {
					display: flex;
					overflow-x: auto;
					// display: grid;
					// grid-template-columns: auto auto auto auto;
					gap: 12rpx;
					padding-bottom: 10rpx;
					margin: 0 20rpx;

					.tab {
						white-space: nowrap;
						display: flex;
						flex-direction: column;
						padding: 20rpx 40rpx;
						border: #eee solid 1rpx;
						border-radius: 10rpx;
						box-shadow: 3rpx 8rpx 8rpx rgba(0, 0, 0, 0.1);

						.num {
							display: flex;
							font-weight: bold;
							align-items: baseline;
						}

						.name {
							font-size: 13px;
							// color: #4B4E54;
							margin-top: 5rpx;
							display: flex;
							justify-content: center;
						}
					}

				}

				.row {
					display: flex;
					padding: 0 20rpx;
					align-items: center;
					margin: 0 0 10rpx 0;
					color: white;

					.t_head {
						width: min-content;
						padding: 20rpx;
						font-size: 30rpx;
						font-weight: 550;
					}

					.group {
						width: 100%;
						display: flex;
						justify-content: space-around;
						color: black;

						.box {
							display: flex;
							flex-direction: column;
							justify-content: center;
							margin: 5rpx;

							.t1 {
								font-size: 40rpx;
								font-weight: 550;
								display: flex;
								justify-content: center;
							}

							.t2 {
								font-size: 25rpx;
							}
						}
					}

				}
			}

			// .button_box {
			// 	background-color: white;
			// 	margin-top: 5rpx;
			// 	height: 100vh;

			// 	.empty {
			// 		text-align: center;
			// 		justify-content: center;
			// 		display: flex;
			// 		margin-top: 30rpx;
			// 	}

			// 	.building {
			// 		height: 100%;
			// 	}
			// }

		}
	}
</style>