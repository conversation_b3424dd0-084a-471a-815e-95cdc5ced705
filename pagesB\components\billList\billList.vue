<template>
	<view class="bill">
		<view class="_head">
			<view class="month">
				<text style="font-size: 50rpx;">{{building_list[0].month}}月</text>
				<text style="font-size: 30rpx;margin-right: 30rpx;">{{building_list[0].year}}</text>
			</view>
			<view class="revenue" style="font-size: 30rpx;">收入：{{sum/100}}</view>
		</view>
		<view class="_body">
			<uni-list class="list">
				<view v-for="(item,index) in building_list" :key="index"  @click="goto(item)" >
					<uni-list-item :title="isRoom?item.room_name:item.name" :note="isRoom?(new Date(item.update_time).getFullYear()+'-'+new Date(item.update_time).getMonth()+'-'+new Date(item.update_time).getDate()):item.address" thumbSize="base" :thumb="imgUrl" :right-text="'+'+(item.money/100)" :showArrow="!isRoom"  ></uni-list-item>
				</view>
			</uni-list>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue';

// 定义props
const props = defineProps({
  building_list: {
    type: Array,
    default: () => [],
    required: false
  },
  sum: {
    type: Number,
    default: 0,
    required: false
  },
  isRoom: {
    type: Boolean,
    default: false,
    required: false
  }
});

// 定义data属性
const imgUrl = computed(() => props.isRoom ? "" : "/static/logo.png");

// 定义方法
const goto = (item) => {
  console.log("item", item);
  if (props.isRoom) {
    return;
  }
  uni.navigateTo({
    url: `/pagesB/roomBill/roomBill?building_id=${item.building_id}&month=${item.month}&year=${item.year}&sum=${item.money}&name=${item.name}`
  });
};
</script>

<style lang="scss">
	.bill{
		border-radius: 20rpx;
		box-sizing: border-box;
		box-shadow: 0px 0px 3px 2px rgba(0, 0, 0, 0.08);
		padding: 20rpx;
		background-color: white;
		margin: 20rpx;
		._head{
			margin-bottom: 10rpx;
			margin-left: 30rpx;
			margin-top: 10rpx;
			box-sizing: border-box;
			.month{
				display: flex;
				justify-content: space-between;
				align-items: baseline;
			}
		}
		._body{
			box-sizing: border-box;
		}
	}
</style>
