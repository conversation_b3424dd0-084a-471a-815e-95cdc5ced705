'use strict';
const {
	result
} = require('result');
const db = uniCloud.databaseForJQL();
const collection = db.collection('fangke_addbooking');
exports.main = async (event, context) => {
    const {
        action,
        data,
    } = event;
	
    // 获取用户信息
    const {
        uid
    } = context;

    try {
        switch (action) {
            case 'add':
                return await addBooking(data, uid);
            case 'get':
                return await getBooking(data, uid);
            case 'update':
                return await updateBooking(data, uid);
            case 'delete':
                return await deleteBooking(data, uid);
            default:
                return {
                    errCode: 400,
                        errMsg: '无效的操作类型'
                };
        }
    } catch (error) {
        console.error('云函数执行错误:', error);
        return result(500,"云函数执行错误"+error,'');
    }
};

// 添加预定
async function addBooking(data, uid) {
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(data.phone)) {
        return result(400,'手机号格式不正确','')
    }

    // 验证日期
    const targetDate = new Date(data.target_date);
    const cancelingDate = new Date(data.canceling_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (targetDate < today) {
        return result(400,'预定日期不能早于今天','')

    if (cancelingDate < targetDate) {
        return result(400,'最晚签约日不能早于预定日期','')
    }

    // 检查是否已有该房间的有效预定
    const existingBooking = await collection
        .where(`room_id == "${data.room_id} && ( status ==1 || status == 3)"`)
        .get();
		//{
        //     room_id: data.room_id,
        //     status: db.command.in([1, 3]), // 已预定或已过期
        // }
		console.log("获得已预约的数据",existingBooking);
    if (existingBooking.data && existingBooking.data.length > 0) {
        let arr = []
		arr = existingBooking.data.map(item => item._id)
		await collection.where(`_id in ['${arr.join("','")}']`).update({
			status:0,
			update_time:new Date().getTime()
		}).then(res =>{
			console.log("修改之前预定状态",res);
		})
    }

    // 插入数据
    const result = await collection.add(data);

    if (result.id) {
        return result(0,"预定成功","");
    } else {
        return {
            errCode: 500,
            errMsg: '预定失败'
        };
    }
}

// 获取预定列表
async function getBooking(data, uid) {
    const where = {};

    // 如果指定了用户ID，只查询该用户的预定
    if (data.uid) {
        where.uid = data.uid;
    } else if (uid) {
        where.uid = uid;
    }

    // 如果指定了房间ID，只查询该房间的预定
    if (data.room_id) {
        where.room_id = data.room_id;
    }

    // 如果指定了状态，只查询该状态的预定
    if (data.status !== undefined) {
        where.status = parseInt(data.status);
    }

    const result = await collection
        .where(where)
        .orderBy('create_time', 'desc')
        .limit(data.limit || 50)
        .skip(data.skip || 0)
        .get();

    return {
        errCode: 0,
        errMsg: 'success',
        data: result.data
    };
}

// 更新预定
async function updateBooking(data, uid) {
    if (!data.id) {
        return {
            errCode: 400,
            errMsg: '缺少预定ID'
        };
    }

    // 检查预定是否存在且属于当前用户
    const existingResult = await collection.doc(data.id).get();

    if (!existingResult.data || existingResult.data.length === 0) {
        return {
            errCode: 404,
            errMsg: '预定不存在'
        };
    }

    const existingBooking = existingResult.data[0];
    if (existingBooking.uid !== uid && !data.admin) {
        return {
            errCode: 403,
            errMsg: '无权限修改此预定'
        };
    }

    // 准备更新数据
    const updateData = {
        update_time: new Date()
    };

    // 只更新允许修改的字段
    const allowedFields = ['status', 'comment', 'canceling_date'];
    allowedFields.forEach(field => {
        if (data[field] !== undefined) {
            updateData[field] = data[field];
        }
    });

    // 更新数据
    const result = await collection.doc(data.id).update(updateData);

    if (result.updated) {
        return {
            errCode: 0,
            errMsg: 'success',
            data: updateData
        };
    } else {
        return {
            errCode: 500,
            errMsg: '更新失败'
        };
    }
}

// 删除预定
async function deleteBooking(data, uid) {
    if (!data.id) {
        return {
            errCode: 400,
            errMsg: '缺少预定ID'
        };
    }

    // 检查预定是否存在且属于当前用户
    const existingResult = await collection.doc(data.id).get();

    if (!existingResult.data || existingResult.data.length === 0) {
        return {
            errCode: 404,
            errMsg: '预定不存在'
        };
    }

    const existingBooking = existingResult.data[0];
    if (existingBooking.uid !== uid && !data.admin) {
        return {
            errCode: 403,
            errMsg: '无权限删除此预定'
        };
    }

    // 删除数据
    const result = await collection.doc(data.id).remove();

    if (result.deleted) {
        return {
            errCode: 0,
            errMsg: 'success'
        };
    } else {
        return {
            errCode: 500,
            errMsg: '删除失败'
        };
    }
}