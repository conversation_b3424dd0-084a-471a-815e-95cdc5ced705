const {
	result
} = require('result');
const db = uniCloud.database()
const dbCmd = db.command
let dbJQL;
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function() { // 通用预处理器
		// this.params = this.getParams()[0]
		this.startTime = Date.now();
		this.params = this.getHttpInfo()
		console.log("this.getClientInfo", this.getClientInfo);
		console.log("httpMethod", this.params.httpMethod);
		console.log("httpInfo", this.params);
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		if (this.params.httpMethod == "POST") {
			//post请求
			// queryStringParameters: { uid: 'ddaadw' },	//参数在form-data时
			let body = this.getHttpInfo().body;
			if (!body) throw result(400, "required");
			this.params = JSON.parse(this.getHttpInfo().body)
		} else {
			//get请求
			this.params = this.getParams()[0]
		}
	},

	async getData() {
		let now = new Date()
		let year = now.getFullYear()
		let month = now.getMonth() + 1;

		console.log("get", this.params);
		let {
			building_list
		} = this.params
		let arr = []
		let all_room = []
		let all_room_count = 0
		let rent_room_count = 0
		let checkin_count = 0
		let checkout_count = 0
		if (building_list.length > 0) {
			let arr = []
			for (let i = 0; i < building_list.length; i++) {
				arr.push(dbCmd.eq(building_list[i]))
			}
				let res = await db.collection("fangke_room").where({
					building_id: dbCmd.or(arr)
				}).get()
				all_room = res.data
				console.log("获取的所有房间列表", all_room);
				all_room.forEach((item, index) => {
					console.log("房间的状态==>",item.rent_status);
					if(item.rent_status != 4 ){ //非占用
						all_room_count ++
						if(item.rent_status != 0 && item.rent_status != 1 ){
							rent_room_count ++
						}
					} 
				}, this)
				let checkin = await db.collection("fangke_room_checkin").where({
					year: year,
					month:month,
					building_id: dbCmd.or(arr)
				}).count()
				console.log("入住数量：",checkin);
				checkin_count = checkin.total
				let checkout = await db.collection("fangke_room_checkin").where({
					year: year,
					month:month,
					building_id: dbCmd.or(arr)
				}).count()
				console.log("退租数量：",checkout);
				checkout_count = checkout.total
		}
		let percentage = 0
		if(all_room_count){
			percentage = (rent_room_count/all_room_count)*100
			// 保留两位小数（四舍五入）
			percentage = percentage.toFixed(2);
		}
		let data = {
			all_room_count,
			rent_room_count,
			percentage: percentage+'%',
			checkin_count,
			checkout_count
		}
		return result(0, "success", data)

	},



	_after: function(error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}

}