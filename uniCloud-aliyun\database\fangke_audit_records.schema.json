// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "审核记录表",
	"bsonType": "object",
	"required": ["type", "title", "applicant_id", "landlord_id", "room_id"],
	"permission": {
		"read": "auth.uid != null && (doc.applicant_id == auth.uid || doc.landlord_id == auth.uid)",
		"create": "auth.uid != null",
		"update": "auth.uid != null && doc.landlord_id == auth.uid",
		"delete": "auth.uid != null && doc.landlord_id == auth.uid"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"type": {
			"bsonType": "string",
			"title": "审核类型",
			"description": "审核类型：utility-水电表，maintenance-维修，cleaning-保洁，checkin-入住，checkout-退租，renewal-续租，transfer-换房",
			"enum": ["utility", "maintenance", "cleaning", "checkin", "checkout", "renewal", "transfer"]
		},
		"title": {
			"bsonType": "string",
			"title": "申请标题",
			"description": "申请标题",
			"maxLength": 100
		},
		"description": {
			"bsonType": "string",
			"title": "申请描述",
			"description": "申请描述",
			"maxLength": 500
		},
		"applicant_id": {
			"bsonType": "string",
			"title": "申请人ID",
			"description": "申请人用户ID",
			"foreignKey": "uni-id-users._id"
		},
		"applicant_name": {
			"bsonType": "string",
			"title": "申请人姓名",
			"description": "申请人姓名",
			"maxLength": 50
		},
		"applicant_phone": {
			"bsonType": "string",
			"title": "申请人电话",
			"description": "申请人联系电话",
			"maxLength": 20
		},
		"landlord_id": {
			"bsonType": "string",
			"title": "房东ID",
			"description": "房东用户ID",
			"foreignKey": "uni-id-users._id"
		},
		"building_id": {
			"bsonType": "string",
			"title": "楼房ID",
			"description": "所属楼房ID",
			"foreignKey": "fangke_building._id"
		},
		"room_id": {
			"bsonType": "string",
			"title": "房间ID",
			"description": "所属房间ID",
			"foreignKey": "fangke_room._id"
		},
		"room_info": {
			"bsonType": "string",
			"title": "房间信息",
			"description": "房间信息描述",
			"maxLength": 100
		},
		"status": {
			"bsonType": "int",
			"title": "审核状态",
			"description": "审核状态：0-待审核，1-已通过，2-已拒绝",
			"enum": [0, 1, 2],
			"defaultValue": 0
		},
		"priority": {
			"bsonType": "int",
			"title": "优先级",
			"description": "优先级：1-低，2-中，3-高",
			"enum": [1, 2, 3],
			"defaultValue": 2
		},
		"attachments": {
			"bsonType": "array",
			"title": "附件列表",
			"description": "相关附件",
			"arrayType": "object",
			"items": {
				"bsonType": "object",
				"properties": {
					"name": {
						"bsonType": "string",
						"title": "文件名",
						"description": "附件文件名"
					},
					"url": {
						"bsonType": "string",
						"title": "文件URL",
						"description": "附件访问地址"
					},
					"type": {
						"bsonType": "string",
						"title": "文件类型",
						"description": "文件类型：image, document, video等"
					},
					"size": {
						"bsonType": "int",
						"title": "文件大小",
						"description": "文件大小（字节）"
					}
				}
			}
		},
		"related_data": {
			"bsonType": "object",
			"title": "关联数据",
			"description": "根据不同审核类型存储的相关数据",
			"properties": {
				"utility_records": {
					"bsonType": "array",
					"title": "水电记录",
					"description": "水电表审核相关的记录ID",
					"arrayType": "string"
				},
				"maintenance_info": {
					"bsonType": "object",
					"title": "维修信息",
					"description": "维修申请的详细信息"
				},
				"cleaning_info": {
					"bsonType": "object",
					"title": "保洁信息",
					"description": "保洁申请的详细信息"
				},
				"checkin_info": {
					"bsonType": "object",
					"title": "入住信息",
					"description": "入住申请的详细信息"
				},
				"checkout_info": {
					"bsonType": "object",
					"title": "退租信息",
					"description": "退租申请的详细信息"
				},
				"renewal_info": {
					"bsonType": "object",
					"title": "续租信息",
					"description": "续租申请的详细信息"
				},
				"transfer_info": {
					"bsonType": "object",
					"title": "换房信息",
					"description": "换房申请的详细信息"
				}
			}
		},
		"audit_user_id": {
			"bsonType": "string",
			"title": "审核人ID",
			"description": "审核人用户ID",
			"foreignKey": "uni-id-users._id"
		},
		"audit_time": {
			"bsonType": "timestamp",
			"title": "审核时间",
			"description": "审核处理时间"
		},
		"audit_remark": {
			"bsonType": "string",
			"title": "审核备注",
			"description": "审核时的备注说明",
			"maxLength": 500
		},
		"create_time": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"description": "申请创建时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_time": {
			"bsonType": "timestamp",
			"title": "更新时间",
			"description": "最后更新时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		}
	}
}
