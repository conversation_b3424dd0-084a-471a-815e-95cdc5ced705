<template>
	<view class="container">
		<!-- #ifdef MP-WEIXIN -->
		<custom-base-bar title="房源管理" showRight="true" rightIconType="plus" rightText="新建房间" @onRight="topRight" @onLeft="topLeft"
			hasBack="true" ></custom-base-bar>
		<!-- #endif -->
		<view class="segmented-control">
			<uni-segmented-control :current="current" :values="tabList" @clickItem="onClickItem" styleType="button">
			</uni-segmented-control>
		</view>
		<view v-show="current === 0" class="content">
			<uni-section title="房子信息" type="line">
				<view class="example">
					<!-- 展示不同的排列方式 -->
					<uni-forms ref="houseForm" :modelValue="houseFormData" :rules="rules">
						<uni-forms-item label="小区/村名" name="name">
							<uni-easyinput v-model="houseFormData.name" placeholder="请输入小区/村名,用于给租客看" />
						</uni-forms-item>
						<uni-forms-item label="楼栋名" required name="number">
							<uni-easyinput v-model="houseFormData.number" placeholder="请输入楼栋名,用于方便自己查看" />
						</uni-forms-item>
						<uni-forms-item label="房子地址">
							<view style="display: flex; align-items: center ; height: 100%;color: #333;">{{ houseFormData.address }}</view>
						</uni-forms-item>
						<uni-forms-item label="定位">
							<map class="map" :latitude="latitude" :longitude="longitude" :markers="covers"></map>
						</uni-forms-item>
						<uni-forms-item label="描述" name="desc">
							<uni-easyinput type="textarea" v-model="houseFormData.desc" placeholder="请输入房子的介绍" />
						</uni-forms-item>
					</uni-forms>
				</view>
			</uni-section>

			<view style="display: flex;justify-content: space-between;margin-right: 25rpx;">
				<uni-section title="房东信息" type="line" style="width: max-content;"></uni-section>
				<switch :checked="isManager" color="#1AAD19" style="transform:scale(0.7)" @change="switchManager">
				</switch>
			</view>

			<view class="example" v-if="isManager">
				<uni-forms ref="userForm" :modelValue="userFormData">
					<uni-forms-item label="名字" required name="u_name">
						<uni-easyinput v-model="userFormData.u_name" placeholder="请输入管理者名字" />
					</uni-forms-item>
					<uni-forms-item label="电话" required name="tel">
						<uni-easyinput v-model="userFormData.tel" type="number" placeholder="请输入管理者电话" />
					</uni-forms-item>
					<uni-forms-item label="验证码" required v-if="false">
						<view class="verify">
							<uni-easyinput v-model="userFormData.code" class="inputCode" type="number"
								placeholder="请输入验证码" />
							<button type="primary" size="mini">获取验证码</button>
						</view>
					</uni-forms-item>
				</uni-forms>

			</view>


			<button type="primary" @click="submit" class="btn">保存</button>
			<button type="warn" @click="delect"  class="btn">删除</button>

		</view>
		<view v-show="current === 1">
			<!-- 房型配置按钮 -->
			<view class="config-btn-wrapper">
				<button type="primary" class="config-btn" @click="config">房型配置</button>
			</view>
			
		<view class="layout-list">
			<view class="layout-item" v-for="(layout, index) in layoutList" :key="index">
				<view class="delete-btn" @click="deleteLayout(layout)">
					<uni-icons type="trash" size="16" color="#FFFFFF" />
				</view>
				<view class="layout-content" @click="editLayout(layout._id)">
					<text class="layout-name">{{ layout.name }}</text>
					<text class="layout-count">编辑</text>
					<uni-icons type="right" size="16" color="#CCCCCC" />
				</view>
				
			</view>
		</view>
			<!-- 房型列表 -->

			<!-- 新建房型 -->
			<view class="add-type" @click="addLayout">
				<uni-icons type="plus" size="16" color="#333333" />
				<text>新建房型</text>
			</view>

		</view>

		<uni-popup ref="popup" type="dialog" border-radius="20rpx 20rpx 20rpx 20rpx" is-mask-click="true">
			<uni-popup-dialog type="error" cancelText="关闭" confirmText="删除" title="注意" :content="tip == 0?'是否删除该楼房':'是否删除该模板'" @confirm="dialogConfirm"
								@close="dialogClose"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>


<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue';
	import {
		onLoad,
		onReady
	} from '@dcloudio/uni-app'
	import {
		userPhone
	} from '../utils/validate.js';
	const longitude = ref("113.255879") //经度
	const latitude = ref("23.172938") //纬度
	const covers = ref([{
		latitude: 23.172938,
		longitude: 113.255879,
		iconPath: '../../static/location.png'
	}]) //标记点
	const qqMapSDK = ref(null)
	const tabList = ref(['基本信息', '房型选择'])
	const db = uniCloud.databaseForJQL()
	const buildingObj = uniCloud.importObject("fangke_building")
	const houseForm = ref(null)
	const popup = ref(null)
	// 响应式数据
	const houseFormData = ref({
		name: '',
		number: '',
		address: '',
		desc: ''
	});
	
	const userFormData = ref({
		u_name: '',
		code: '',
		tel: ''
	});
	const userForm = ref(null)
	const tel = ref('');
	const current = ref(0);
	// 移除地区选择相关数据
	// const selectedRegion = ref([]);
	// const selectedRegionText = ref('');
	// const isRegionSelected = ref(false);

	// rules.value.address 只保留必填校验
	const rules = ref({
		title: {
			rules: [{
				required: true,
				errorMessage: '标题不能为空'
			}]
		},
		name: {
			rules: [{
				required: true,
				errorMessage: '姓名不能为空'
			}]
		},
		u_name: {
			rules: [{
				required: true,
				errorMessage: '姓名不能为空'
			}]
		},
		address: {
			rules: [{
				required: true,
				errorMessage: '地址不能为空'
			}]
		},
		tel: {
			rules: [{
				required: true,
				errorMessage: '手机号码不能为空'
			}, {
				format: 'number',
			}, {
				validateFunction: function(rule, value, data, callback) {
					let phone = (
						/^(13[0-9]|14[1579]|15[0-3,5-9]|16[6]|17[0123456789]|18[0-9]|19[89])\d{8}$/
					);
					if (userPhone("" + value)) {
						return true
					}
					callback('请输入有效电话号码')
					return false
				}
			}]
		},
		code: {
			rules: [{
				required: true,
				errorMessage: '验证码不能为空',
			}]
		}
	});
	
    const config = () =>{
		uni.navigateTo({
			url:"/pagesB/roomConfig/roomConfig?building_id="+building_id.value
		})
	}
	
	onReady(()=>{
		
	})
	const layoutList = ref([]);

	const roomFormData = ref({
		room: [],
	});

	const roomLists = ref([]);
	const building_id = ref("");
	const info = ref({});
	const uid = ref('');
	const floor = ref(0);
	const num = ref(0);
	const floors = ref([]);
	const nums = ref([]);
	const isManager = ref(true);
	const city = ref("");
	const desc = ref("")
	const type = ref(1)
	const tip = ref(0)
	const delModle = ref('')
	// 初始化楼层和房间数选项
	for (let i = 1; i <= 99; i++) {
		floors.value.push(i);
		nums.value.push(i);
	}

	// 页面加载后初始化
	onMounted(() => {
		// 设置表单验证规则（需要在模板中引用表单组件）
		userForm.value.setRules(rules.value)
	});
	
	const deleteLayout = (item) =>{
		if(item.count > 0){
			uni.showToast({
				title:'有'+item.count+'间房有使用改房型的价格模板，请替换模板或者删除对应房间',
				icon:'error'
			})
			return
		}else{
			tip.value = 1
			delModle.value = item._id
			popup.value.open()
		}
	}
	
	const topLeft = () => {
		uni.navigateBack()
	}

	const topRight = () => {
		console.log("topRight");
		uni.navigateTo({
			url: "/pagesB/editRoom/editRoom?building_id=" + building_id.value
		});
	}

	// 页面加载时处理参数
	onLoad((e) => {
		console.log("addHouse", e);
		uid.value = uniCloud.getCurrentUserInfo().uid;
		building_id.value = e.id;
		type.value = e.type //集中型还是分散型
		getBuilding();

		roomLists.value = [];

		// 监听房间列表更新
		uni.$on("roomList", (res) => {
			console.log("监听的列表是：", res);
			let list = [];
			floor.value = res.length;

			res.forEach(function(element) {
				element.forEach((item) => {
					item.rent_status = 0;
					list.push(item);
				});
			});

			roomLists.value = list;
		});
	});

	// 获取楼房信息
	const getBuilding = async () => {
		try {
			const res = await db.collection("fangke_building").doc(building_id.value).get();
			console.log("getBuilding", res);
			if (res.data && res.data.length > 0) {
				await updateInfo(res.data[0]);
			}
		} catch (error) {
			console.error("获取楼房信息失败:", error);
			uni.showToast({
				title: "获取楼房信息失败",
				icon: 'error'
			});
		}
	};

	// 更新表单数据
	const updateInfo = async (info) => {
		houseFormData.value.name = info.name || '';
		houseFormData.value.number = info.number || '';
		houseFormData.value.desc = info.desc || '';
		houseFormData.value.address = info.address || '';
		if (info.location && info.location.length >= 2) {
			longitude.value = info.location[0].toString();
			latitude.value = info.location[1].toString();
			covers.value = [{
				latitude: parseFloat(latitude.value),
				longitude: parseFloat(longitude.value),
				iconPath: '../../static/location.png'
			}];
		}
	};

	// 提交表单
	const submit = async () => {
		let sum = 0;
		let data = {};
		data.name = houseFormData.value.name
		// 验证房子信息表单
		await houseForm.value.validate().then(res => {
			console.log('房子信息', res);
			sum = sum + 1;
			setBuildingData(data, res);
		}).catch(err => {
			console.log('err', err);
		});

		// 验证管理员信息表单
		if (isManager.value) {
			await userForm.value.validate().then(res => {
				console.log('管理员信息：', res);
				sum = sum + 1;
				setManagerInfo(data, res);
			}).catch(err => {
				console.log('err', err);
			});
		}

		data.city = city.value;
		if (data.city == "") {
			uni.showToast({
				title: "城市信息不能为空",
				icon: 'none'
			});
			return;
		}

		// 验证地区选择
		if (!isRegionSelected.value || !city.value) {
			uni.showToast({
				title: "请选择省市区",
				icon: 'none'
			});
			return;
		}

		// 提交数据
		if ((sum == 2 && isManager.value) || (sum == 1 && !isManager.value)) {
			data.update_time = Date.now();
			
			// 保存经纬度信息
			if (longitude.value && latitude.value) {
				data.location = [longitude.value, latitude.value];
			}

			await db.collection("fangke_building").doc(building_id.value).update(data).then(res => {
				console.log("updateBuilding", res);
				uni.showToast({
					title: "修改成功"
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 800);
			});
		} else {
			uni.showToast({
				title: "请完成爆红的选项",
				icon: 'none'
			});
		}
	};
	
	const delect = ()=>{
		tip.value = 0
		popup.value.open()
	}
	
	const dialogConfirm = async()=>{
		console.log("dialogConfirm",tip.value);
		popup.value.close()
		if(tip.value == 1){		//删除模板
			let params = {
				uid:uid.value,
				mode_id:delModle.value
			}
			await uni.$lkj.api.delRoomModel(params).then(res =>{
				console.log("删除回调",res);
				if(res.errCode == 0){
					uni.showToast({
						title:'删除成功',
						icon:'none'
					})
					layoutList.value = layoutList.value.filter( item => item._id !== delModle.value )
				}
			})
		}else{		//删除
			let params = {
				uid:uid.value,
				building_id:building_id.value
			}
			await uni.$lkj.api.delBuilding(params).then(res =>{
				console.log("删除回调",res);
				if(res.errCode == 0){
					uni.reLaunch({
						url:'/pages/house/house'
					})
				}
			}).catch(err =>{
				uni.showToast({
					title:"删除失败。"+err.message
				})
			})
		}
		
	}
	
	const dialogClose = ()=>{
		popup.value.close()
	}

	// 设置楼房数据
	const setBuildingData = (data, res) => {
		data.number = res.number;
		data.address = houseFormData.value.address;
		data.desc = res.desc;
		return data;
	};

	// 设置管理员信息
	const setManagerInfo = (data, res) => {
		data.manager = res.u_name;
		data.tel = res.tel;
		return data;
	};

	// 添加楼房
	const addBuilding = async (data, rooms) => {
		data.sum = 0; // 初始化总数
		data.uid = uid.value;
		data.room_num = roomLists.value.length;
		data.floor = floor.value;

		if (isSwitch.value) {
			data.type = 1;
		} else {
			data.type = 2;
		}
		console.log("rooms", rooms);
		let params = {
			data,
			room_list: rooms
		}
		console.log('上传数据data', params);
		await uni.$lkj.api.addBuilding(params).then(res => {
			console.log("请求成功", res);
			building_id.value = res.data;
			if (res.errCode == 0) {
				uni.showToast({
					title: "添加成功",
					icon: 'none'
				});
				setTimeout(() => {
					uni.reLaunch({
						url: "/pages/index/index"
					});
				}, 800);
			} else {
				uni.showToast({
					title: "添加失败：" + res.errMsg,
					icon: 'none'
				});
			}

		}).catch(err => {
			uni.showToast({
				title: err,
				icon: 'none'
			});
		})
	};
	
	const editLayout = (id) =>{
		uni.navigateTo({
			url:"/pagesB/layout/layout?id="+id,
			events:{
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			updateLayout:function(data){
				console.log("updateLayout")
				getRoomModel()
			}
		}
		})
	}
	 
	const addLayout = () =>{
		uni.navigateTo({
			url:"/pagesB/layout/layout",
			events:{
				// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
				updateLayout:function(data){
					console.log("updateLayout")
					getRoomModel()
				}
			}
		})
	}

	// 编辑房间
	const editRoom = (floor, num) => {
		uni.navigateTo({
			url: "/pagesB/editRoom/editRoom?building_id=" + building_id.value
		});
	};


	// 切换是否有管理员
	const switchManager = () => {
		isManager.value = !isManager.value;
	};


	const onClickItem = (e) => {
		if (current.value != e.currentIndex) {
			current.value = e.currentIndex;
			if(e.currentIndex == 1){
				getRoomModel()
			}
		}
		
	}
	
	const getRoomModel = () =>{
		let params = {
			params : {
				uid:uid.value
			}
		}
		uni.$lkj.api.getRoomModel(params).then(res =>{
			console.log("获取模板",res);
			layoutList.value = res.data
		}).catch(err =>{
			
		})
	}
</script>

<style lang="scss" scoped>
	.container {
		margin-left: 15rpx;
		margin-right: 15rpx;
		margin-bottom: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;

		.segmented-control {
			margin-bottom: 15px;
		}

		.content {
			padding-bottom: 50rpx;
			box-sizing: border-box;

			.example {
				padding-left: 15px;
				padding-right: 15px;
				background-color: #fff;

				.verify {
					display: flex;
					justify-content: space-around;
					align-items: center;

					.inputCode {
						margin-right: 10rpx;
					}
				}

				.map {
					width: 100%;
					height: 300rpx;
				}

				.line_raw {
					margin-top: 20rpx;
					margin-bottom: 20rpx;
					display: flex;
					padding-left: 15px;
					padding-right: 15px;
					background-color: #fff;
					justify-content: space-between;
					align-content: center;
					align-items: center;

					.left {
						display: flex;
						font-size: 14px;
						color: #606266;
						width: 200rpx;
					}

					.pick {
						width: 100%;

						.right {
							display: flex;

							.item {
								display: flex-end;
								width: 100%;
							}

							.arrow {
								width: 30rpx;
							}

						}
					}

					.right_text {
						display: flex;

						.arrow {
							width: 30rpx;
						}

					}


				}



			}
			
			.btn {
				margin: 10rpx 20rpx;
			}
			
		}
		
		
		.config-btn-wrapper {
			padding:  10rpx 30rpx;
			.config-btn {
				background-color: #07C160;
				border: none;
			}
		}
		
		
		
		.layout-list {
			padding: 0 32rpx;
		
			.layout-item {
				display: flex;
				align-items: center;
				padding: 32rpx;
				background-color: #FFFFFF;
				border-radius: 8rpx;
				margin-bottom: 20rpx;
				border-bottom: #999999 solid 1rpx;
			}
		
			.delete-btn {
				width: 40rpx;
				height: 40rpx;
				border-radius: 20rpx;
				background-color: #FF4D4F;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
			}
			
			.layout-content{
				flex: 1;
				display: flex;
				justify-content: space-between;
				.layout-name {
					flex: 1;
					font-size: 14px;
					color: #333333;
				}
						
				.layout-count {
					font-size: 14px;
					color: #999999;
					margin-right: 16rpx;
				}
			}
			
		}
		
		.add-type {
			display: flex;
			align-items: center;
			padding: 32rpx;
			background-color: #FFFFFF;
			margin: 20rpx 32rpx;
			border-radius: 8rpx;
			border: #606266 solid 1rpx;
		}
		
		.add-type text {
			margin-left: 16rpx;
			font-size: 14px;
			color: #333333;
		}

	}

</style>