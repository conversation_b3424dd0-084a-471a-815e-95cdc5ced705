<template>
	<view class="page">
		<view class="header">
			<view class="room-info">
				<text class="room-name">{{room_name}}</text>
			</view>
			<view class="contract-info" :class="{ active: selectedContract === 'electronic' }"
				@click="selectContract('electronic')">
				<view class="contract-title">
					<text>新签CA电子合同</text>
					<uni-icons :type="selectedContract === 'electronic' ?'checkbox-filled':'checkbox'" size="16"
						:color="selectedContract === 'electronic' ? '#07c160' : '#ccc'" />
				</view>
				<view class="contract-desc">请用于您新租赁签署的线签电子合同，使用CA电子合同签署后，系统将自动生成电子合同，并直接生效。</view>
				<view class="contract-number">
					<text>电子签名号码</text>
					<text class="number">13632244771-13632244771</text>
				</view>
			</view>
			
			<view class="contract-info" :class="{ active: selectedContract === 'paper' }"
				@click="selectContract('paper')">
				<view class="contract-title">
					<text>已签纸质合同</text>
					<uni-icons :type="selectedContract === 'paper' ?'checkbox-filled':'checkbox'" size="16"
						:color="selectedContract === 'paper' ? '#07c160' : '#ccc'" />
				</view>
				<view class="contract-desc">使用于与租客签约过纸质合同，将合同信息补录到系统中，方便统一管理</view>
				<view class="contract-number">
					<text>合同编码</text>
					<input class="number" v-model="formData.contractCode" placeholder="请输入(没有可以不填)" />
				</view>
			</view>
		</view>
		<view class="content">
			<view class="form-section">
				<uni-section title="租客信息" type="line"></uni-section>
				<view class="form-item">
					<text class="label required">姓名</text>
					<input type="text" placeholder="请输入" v-model="formData.tenantName" />
				</view>
				<view class="form-item">
					<text class="label required">手机号码</text>
					<input type="number" placeholder="请输入" v-model="formData.tenantPhone" />
				</view>
				<view class="form-item">
					<text class="label required">性别</text>
					<view class="gender-group">
						<view class="gender-item" :class="{ active: formData.tenantGender === '男' }"
							@click="formData.tenantGender = '男'">
							<text>男</text>
						</view>
						<view class="gender-item" :class="{ active: formData.tenantGender === '女' }"
							@click="formData.tenantGender = '女'">
							<text>女</text>
						</view>
					</view>
				</view>

				<!-- <view class="form-item">
					<text class="label">证件类型</text>
					<text class="value">身份证</text>
				</view> -->
				<view class="form-item">
					<text class="label required">证件号码</text>
					<input type="text" placeholder="请输入" v-model="formData.idNumber" />
				</view>
				<view class="form-item">
					<uni-collapse ref="collapse" :open="false" style="width: 100%;">
						<uni-collapse-item title="证件照片">
							<view class="cardId">
								<view class="upload-area">
									<uni-icons type="camera" size="24" color="#999" />
									<text>上传照片</text>
								</view>
								<text>身份证正面</text>
							</view>
							
						</uni-collapse-item>
					</uni-collapse>
				</view>

				<!-- <view class="form-item">
					<text class="label">租约起止日期</text>
				</view> -->
				<uni-section title="租约起止日期" type="line">
				</uni-section>
				<view class="form-item" @click="showDateRangeSelector">
					<text class="label required">选择租约时间</text>
					<view class="item-right">
						<text class="value">{{ dateRangeText }}</text>
						<uni-icons type="right" size="16" color="#999" />
			</view>
				</view>

				<!-- DateRangeSelector 组件 -->
				<DateRangeSelector 
					ref="dateRangeSelector" 
					:startDate="formData.startDate" 
					:endDate="formData.endDate" 
					:customTimeTags="customTimeTags"
					@confirm="onDateRangeConfirm" 
					@cancel="onDateRangeCancel" 
				/>

				<view class="form-item">
					<text class="label">紧急联系人</text>
				</view>
				<view class="list-item">
					<view class="item-row" style="display: flex;">
						<input placeholder="姓名" style="text-align: left;" v-model="formData.emergency_contact" />
						<input type="number" placeholder="手机号码" v-model="formData.emergency_phone"
							class="item-price-input" />
			</view>
				</view>
				</view>


			<view class="form-section">
				<uni-section title="同住人" type="line">
					<template v-slot:right>
						<view class="add-btn" @click="addRoomate">添加同住人</view>
					</template>
				</uni-section>
				<view class="form-item" v-for="(item,index) in formData.roommates" :key="index"
					v-if="formData.roommates.length> 0" @click="editRoommate(item,index)">
					<text class="label">同住人{{index+1}}</text>
					<view class="item-right">
						<text class="value">{{item.name}}</text>
					<uni-icons type="right" size="16" color="#999" />
				</view>

				</view>
			</view>
			<view class="form-section">
				<uni-section title="租金合付租方式" type="line"></uni-section>

				<view class="form-item">
					<text class="label required">月租金（元）</text>
					<input type="number" placeholder="0.00" v-model="formData.monthlyRent" />
				</view>
				<view class="form-item" @click="showPaymentPopup">
					<text class="label required">付款方式</text>
					<view class="item-right">
						<text class="value">{{formData.paymentType === ""?'请选择':formData.paymentType}}</text>
					<uni-icons type="right" size="16" color="#999" />
					</view>

				</view>
				<view class="form-item">
					<text class="label">押金（元）</text>
					<input type="number" placeholder="0.00" v-model="formData.deposit" />
				</view>
				<view class="form-item">
					<text class="label required">水费（元/吨）</text>
					<input type="number" placeholder="0.00" v-model="formData.water" />
				</view>
				<view class="form-item">
					<text class="label required">电费（元/度）</text>
					<input type="number" placeholder="0.00" v-model="formData.ele" />
				</view>
				<view class="form-item">
					<text class="label">其他押金</text>
					<text class="add-btn" @click="showDepositPopup">添加押金</text>
				</view>
				<view class="list-item" v-for="(item,index) in formData.depositList" :key="index"
					v-if="formData.depositList.length > 0">
					<view class="item">
						<text class="label">{{item.text}} (元)</text>
						<input type="number" placeholder="请输入" v-model="item.value"></input>
					</view>
				</view>
				<view class="form-item">
					<text class="label">其他月费用</text>
					<text class="add-btn" @click="showFeePopup">添加费用</text>
				</view>
				<view class="list-item" v-for="(item,index) in formData.feeList" :key="index"
					v-if="formData.feeList.length > 0">
					<view class="item">
						<text class="label">{{item.text}} (元)</text>
						<input type="number" placeholder="请输入" v-model="item.value" />
				</view>
				</view>
				<view class="form-item" @click="showRentDatePopup">
					<text class="label required">收租日期</text>
					<view class="item-right">
						<text class="value">每期固定{{formData.rentDate || 1}}号</text>
					<uni-icons type="right" size="16" color="#999" />
					</view>
				</view>
				<view class="form-item">
					<text class="label">滞纳金</text>
					<input type="number" placeholder="0.00" v-model="formData.lateFee" @input="lateFeeChange" />
				</view>

			</view>
			<view class="late_info">
				<uni-segmented-control :current="current" :values="tabList" @clickItem="onClickItem" styleType="button"
					activeColor="#1AAD19">
					</uni-segmented-control>
				<view class="late_btn">{{tip}}</view>
			</view>
			<view class="form-section">
				<uni-section title="补充信息" type="line"></uni-section>

				<view class="form-item" @click="showSource">
					<text class="label">租客来源</text>
					<view class="item-right">
						<text class="value">{{ formData.tenantSource == ""?"请选择":formData.tenantSource}} </text>
						<uni-icons type="right" size="16" color="#999" />
					</view>

				</view>
				<view class="form-item">
					<text class="label">物品清单</text>
					<text class="add-btn" @click="addItem">添加物品</text>
				</view>
				<view class="list-item" v-for="(item, index) in formData.itemList" :key="index"
					v-if="formData.itemList.length > 0">
					<view class="item-row">
						<text class="item-label">物品{{index + 1}}</text>
						<input type="text" style="text-align: left;margin-left: 10rpx;" placeholder="物品名称"
							v-model="item.text" class="item-name-input" />
						<input type="number" placeholder="费用" v-model="item.value" class="item-price-input" />
						<uni-icons type="trash" size="20" color="#FF6B6B" @click="removeItem(index)"
							class="delete-btn" />
					</view>
				</view>
				<view class="form-item remark-item">
					<text class="label">备注信息</text>
					<textarea class="remark-input" placeholder="请输入备注信息" maxlength="200" v-model="formData.desc" />
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="submit-btn" type="primary" @click="handleSubmit">提交签约</button>
		</view>

		<!-- 费用选择弹窗 -->
		<uni-popup ref="feePopup" type="bottom" background-color="#fff">
			<view class="fee-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeFeePopup">取消</text>
					<text class="popup-title">选择费用类型</text>
					<text class="confirm-btn" @click="confirmFeeSelection">确定</text>
				</view>
				<view class="fee-picker-container">
					<picker-view class="fee-picker" :value="feePickerValue" @change="onFeePickerChange"
						:indicator-style="indicatorStyle">
						<picker-view-column>
							<view class="picker-text" v-for="(category, index) in feeCategories" :key="index">
								{{ category.name }}
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="picker-text" v-for="(item, index) in currentFeeChildren" :key="index">
								{{ item }}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</uni-popup>

		<!-- 收租日期选择弹窗 -->
		<uni-popup ref="rentDatePopup" type="bottom" background-color="#fff">
			<view class="fee-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeRentDatePopup">取消</text>
					<text class="popup-title">选择收租日期</text>
					<text class="confirm-btn" @click="confirmRentDate">确定</text>
				</view>
				<view class="fee-picker-container">
					<picker-view class="fee-picker" :value="[rentDatePickerValue]" @change="onRentDatePickerChange"
						:indicator-style="indicatorStyle">
						<picker-view-column>
							<view class="picker-text" v-for="(date, index) in rentDateOptions" :key="index">
								{{ date }}号
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</uni-popup>

	</view>
</template>
<script setup>
	import {
		nextTick,
		ref,
		computed,
		onMounted
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'

	import {
		dayjs
	} from '@/utils/dayjs.min.js'
	// 定义响应式数据
	import {
		store2
	} from '@/utils/js/store.js'
	import DateRangeSelector from '../components/DateRangeSelector/DateRangeSelector.vue'

	const userInfo = computed(() => store2.userInfo)

	const current = ref(0);
	const tabList = ref(['每天固定', '每天百分比', '单次固定', '单次百分比'])
	const onClickItem = (e) => {
		if (current.value != e.currentIndex) {
			current.value = e.currentIndex;
			switch (e.currentIndex) {
				case 0:
					formData.value.lateType = 1
					tip.value = "每逾期一天，按" + formData.value.lateFee + "元加收滞纳金"
				break
				case 1:
					formData.value.lateType = 2
					tip.value = "每逾期一天，按当期租赁账单总额的 " + formData.value.lateFee + " %加收滞纳金"
				break
				case 2:
					formData.value.lateType = 3
					tip.value = "无视账单逾期天数，只加收" + formData.value.lateFee + "元滞纳金"
				break
				case 3:
					formData.value.lateType = 4
					tip.value = "无视账单逾期天数，只加收当期租金总额的 " + formData.value.lateFee + "%滞纳金"
				break
			}
		}
	}
	const tip = ref('')
	
	// 费用选择器相关数据
	const feePopup = ref(null);
	const feePickerValue = ref([0, 0]); // [分类索引, 子项索引]
	const indicatorStyle =
		'height: 76rpx; background-color: rgba(7, 193, 96, 0.1); border-top: 2px solid #07c160; border-bottom: 2px solid #07c160;';

	// 收租日期选择器相关数据
	const rentDatePopup = ref(null);
	const rentDatePickerValue = ref(0); // 默认选择4号（索引3）
	const rentDateOptions = ref(Array.from({
		length: 31
	}, (_, i) => i + 1)); // 1-31号
	const room_id = ref('')
	const building_id = ref('')
	const uid = ref('')
	const room_name = ref('')
	// 费用分类数据
	const feeCategories = ref([{
			"name": "租金",
			"children": [
				"租金补差",
				"租金涨价",
				"临时租金"
			]
		},
		{
			"name": "水电燃",
			"children": [
				"水费",
				"电费",
				"电损耗费",
				"水损耗费"
			]
		},
		{
			"name": "维修费",
			"children": [
				"家电维修",
				"房屋维修",
				"公共区域维修"
			]
		},
		{
			"name": "服务费",
			"children": [
				"管理费",
				"物业费",
				"宽带费",
				"垃圾费",
				"搬家费",
				"保洁费"
			]
		}
	]);

	// 当前选中分类的子项
	const currentFeeChildren = computed(() => {
		const categoryIndex = feePickerValue.value[0];
		return feeCategories.value[categoryIndex]?.children || [];
	});

	onMounted(() => {

	})

	onLoad((e) => {
		tip.value = "每逾期一天，按" + formData.value.lateFee + "元加收滞纳金"
		if (e.room_id && e.building_id && e.uid) {
			room_id.value = e.room_id
			building_id.value = e.building_id
			uid.value = e.uid
			room_name.value = e.room_name
		}
	})

	const selectedContract = ref('electronic')
	const formData = ref({
		contractCode: '',
		// 租客信息
		tenantName: '',
		tenantPhone: '',
		tenantGender: '男',
		idNumber: '',
		emergency_contact: '',
		emergency_phone: '',
		// 同住人
		roommates: [],
		// 租约日期
		startDate: '',
		endDate: '',
		// 租金信息  
		paymentType: '',
		monthlyRent: '',
		deposit: '',
		lateFee: 0,
		lateType: 1,
		rentDate: 1, // 收租日期，默认1号
		depositList: [],
		feeList: [],
		itemList: [], // 物品清单
		// 补充信息
		tenantSource: '',
		desc: '',
		water: '',
		ele: ''
	})

	const addRoomate = () => {
		uni.navigateTo({
			url: "/pagesB/addroomMate/addroomMate",
			events: {
				add: function(data) {
					console.log("add", data);
					data.room_id = room_id.value
					formData.value.roommates.push(data)
				}
			}
		})
	}

	const editRoommate = (item, index) => {
		uni.navigateTo({
			url: "/pagesB/addroomMate/addroomMate?item=" + encodeURIComponent(JSON.stringify(item)) +
				"&index=" + index,
			events: {
				editEvent: function(data, index) {
					console.log("edit", data);
					formData.value.roommates[index] = data
				}
			}
		})
	}


	const showDepositPopup = () => {
		uni.showActionSheet({
			itemList: ['门卡押金', '钥匙押金', '家具押金', '家电押金'],
			success: (res) => {
				const types = ['门卡押金', '钥匙押金', '家具押金', '家电押金'];
				let result = formData.value.depositList.find(item => {
					return item.text === types[res.tapIndex]
				})
				console.log("showDepositPopup", result);
				if (result == undefined) {
					let item = {
						text: types[res.tapIndex],
						value: 0
					}
					formData.value.depositList.push(item);
				} else {
					console.log("已经存在了");
				}

			}
		});
	};

	const showPaymentPopup = () => {
		uni.showActionSheet({
			itemList: ['押一付一', '押一付三', '押二付一', '押三付一', '半年付', '年付'],
			success: (res) => {
				const types = ['押一付一', '押一付三', '押二付一', '押三付一', '半年付', '年付'];
				formData.value.paymentType = types[res.tapIndex];
				if (formData.value.deposit == '' && formData.value.monthlyRent !== '') {
					switch (res.tapIndex) {
						case 0:
							formData.value.deposit = parseFloat(formData.value.monthlyRent)
							break
						case 1:
							formData.value.deposit = parseFloat(formData.value.monthlyRent)
							break
						case 2:
							formData.value.deposit = parseFloat(formData.value.monthlyRent) * 2
							break
						case 3:
							formData.value.deposit = parseFloat(formData.value.monthlyRent) * 3
							break
					}
				}
			}
		});
	};

	const showSource = () => {
		uni.showActionSheet({
			itemList: ['租房平台', '社交媒体', '自然来访', '线下推广', '合作伙伴', '朋友介绍'],
			success: (res) => {
				const types = ['租房平台', '社交媒体', '自然来访', '线下推广', '合作伙伴', '朋友介绍'];
				formData.value.tenantSource = types[res.tapIndex];
			}
		});
	};

	const lateFeeChange = (e) => {
		console.log("lateFeeChange", e);
	}

	const selectContract = (contractType) => {
		selectedContract.value = contractType
	}

	// 表单校验
	const validateForm = () => {
		const requiredFields = [{
				field: 'tenantName',
				name: '姓名'
			},
			{
				field: 'tenantPhone',
				name: '手机号码'
			},
			{
				field: 'idNumber',
				name: '证件号码'
			},
			{
				field: 'monthlyRent',
				name: '月租金'
			}
		]

		for (const item of requiredFields) {
			if (!formData.value[item.field] || formData.value[item.field].toString().trim() === '') {
				uni.showToast({
					title: `请填写${item.name}`,
					icon: 'none'
				})
				return false
			}
		}

		// 验证手机号格式
		const phoneReg = /^1[3-9]\d{9}$/
		if (!phoneReg.test(formData.value.tenantPhone)) {
			uni.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			})
			return false
		}

		// 验证身份证格式
		const idReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
		if (!idReg.test(formData.value.idNumber)) {
			uni.showToast({
				title: '请输入正确的身份证号码',
				icon: 'none'
			})
			return false
		}

		if (formData.value.startDate === '') {
			uni.showToast({
				title: '请设置租约开始时间',
				icon: 'none'
			})
			return false
		}

		if (formData.value.endDate === '') {
			uni.showToast({
				title: '请设置租约结束时间',
				icon: 'none'
			})
			return false
		}

		// // 验证合同编码（纸质合同必填）
		// if (selectedContract.value === 'paper' && !formData.value.contractCode.trim()) {
		// 	uni.showToast({
		// 		title: '请填写合同编码',
		// 		icon: 'none'
		// 	})
		// 	return false
		// }

		return true
	}

	// 提交处理
	const handleSubmit = async () => {
		// 水电费默认值处理
		if (!formData.value.water || formData.value.water.toString().trim() === '') {
			formData.value.water = 0;
		}
		if (!formData.value.ele || formData.value.ele.toString().trim() === '') {
			formData.value.ele = 0;
		}

		// 清理空值的押金和费用项
		formData.value.depositList = formData.value.depositList.filter(item => {
			return item.value > 0;
		});

		formData.value.feeList = formData.value.feeList.filter(item => {
			return item.value > 0 && item.value !== "";
		});

		// 清理空值的物品清单
		formData.value.itemList = formData.value.itemList.filter(item => {
			return item.text && item.text.toString().trim() !== ''
		});

		let contract = {}
		if (selectedContract.value === 'electronic') {
			contract.isPaper = false
		} else {
			contract.isPaper = true
		}


		//校验
		if (validateForm()) {
			contract.room_id = room_id.value
			contract.room_name = room_name.value
			contract.building_id = building_id.value
			contract.start_time = formData.value.startDate
			contract.end_time = formData.value.endDate
			if (formData.value.deposit !== '' && parseFloat(formData.value.deposit) > 0) {
				formData.value.depositList.push({
					text: "房屋押金",
					value: formData.value.deposit
				})
			}
			formData.value.depositList.forEach(item => {
				item.value = parseFloat(item.value) * 100
			})
			formData.value.feeList.forEach(item => {
				item.value = parseFloat(item.value) * 100
			})
			contract.ext_fee = formData.value.feeList
			contract.deposit = formData.value.depositList
			contract.payment = formData.value.paymentType
			contract.rent = parseFloat(formData.value.monthlyRent) * 100
			contract.day = formData.value.rentDate
			contract.ele_price = parseFloat(formData.value.ele) * 100
			contract.water_price = parseFloat(formData.value.water) * 100
			contract.desc = formData.value.desc
			contract.uid = uid.value
			contract.sign_time = new Date().getTime()
			contract.status = 1
			contract.goods_list = formData.value.itemList
			contract.number = formData.value.contractCode
			contract.handler = userInfo.value.mobile
			if (formData.value.lateFee > 0) {
				contract.late_fee_type = formData.value.lateType
				contract.late_fee_num = parseFloat(formData.value.lateFee) * 100
			} else {
				contract.late_fee = 0
			}
			let user = {
				name: formData.value.tenantName,
				phone: formData.value.tenantPhone,
				gender: formData.value.tenantGender === "男" ? 1 : 2,
				id_card: formData.value.idNumber,
				emergency_contact: formData.value.emergency_contact,
				emergency_phone: formData.value.emergency_phone,
				source: formData.value.tenantSource,
				room_id: room_id.value
			}
			let params = {
				user: user,
				contract: contract,
				uid: uniCloud.getCurrentUserInfo().uid,
				room_mate: formData.value.roommates
			}
			uni.showLoading({
				title: '提交中...'
			})
			console.log("参数params",params);
			await uni.$lkj.api.checkin(params).then(res => {
				console.log("提交成功", res);
				uni.hideLoading()
				setTimeout(() => {
					uni.reLaunch({
						url: "/pages/house/house"
					})
				}, 2000)
			})

		}
	}

	// 费用选择器相关方法
	const showFeePopup = () => {
		console.log("显示费用选择弹窗");
		feePopup.value?.open();
	};

	const closeFeePopup = () => {
		feePopup.value?.close();
	};

	const onFeePickerChange = (e) => {
		console.log('费用选择变化:', e.detail.value);
		feePickerValue.value = e.detail.value;
	};

	const confirmFeeSelection = () => {
		const categoryIndex = feePickerValue.value[0];
		const childIndex = feePickerValue.value[1];

		const categoryName = feeCategories.value[categoryIndex]?.name;
		const childName = feeCategories.value[categoryIndex]?.children[childIndex];

		if (categoryName && childName) {
			const selectedFeeName = childName;

			// 检查是否已存在
			const existingFee = formData.value.feeList.find(item => item.text === selectedFeeName);

			if (existingFee) {
				uni.showToast({
					title: '该费用类型已存在',
					icon: 'none',
					duration: 2000
				});
			} else {
				// 添加新费用项
				const newFeeItem = {
					text: selectedFeeName,
					value: 0
				};
				formData.value.feeList.push(newFeeItem);
			}
		}

		closeFeePopup();
	};

	const showRentDatePopup = () => {
		// 实现弹窗逻辑
		console.log("showRentDatePopup");
		rentDatePopup.value.open();
	};

	const closeRentDatePopup = () => {
		rentDatePopup.value.close();
	};

	const onRentDatePickerChange = (e) => {
		rentDatePickerValue.value = e.detail.value[0];
	};

	const confirmRentDate = () => {
		formData.value.rentDate = rentDateOptions.value[rentDatePickerValue.value];
		closeRentDatePopup();
		uni.showToast({
			title: `已选择${formData.value.rentDate}号`,
			icon: 'success'
		});
	};

	const addItem = () => {
		// 实现添加物品逻辑
		console.log("addItem");
		formData.value.itemList.push({
			text: '',
			value: ''
		});
	};

	const removeItem = (index) => {
		// 实现移除物品逻辑
		console.log("removeItem", index);
		formData.value.itemList.splice(index, 1);
	};

	// 自定义时间标签 - 租约期限选择
	const customTimeTags = ref([
		{ name: '1个月', days: 30, type: 'future' },
		{ name: '3个月', days: 90, type: 'future' },
		{ name: '6个月', days: 180, type: 'future' },
		{ name: '1年', days: 365, type: 'future' }
	])

	// DateRangeSelector 相关
	const dateRangeSelector = ref(null)

	// 日期范围显示文本
	const dateRangeText = computed(() => {
		if (formData.value.startDate && formData.value.endDate) {
			return `${formData.value.startDate} ~ ${formData.value.endDate}`
		}
		return '请选择租约起止日期'
	})

	// 显示日期范围选择器
	const showDateRangeSelector = () => {
		dateRangeSelector.value?.open()
	}

	// 日期范围选择确认回调
	const onDateRangeConfirm = (result) => {
		formData.value.startDate = result.startDate
		formData.value.endDate = result.endDate
		
		console.log('选择的租约时间:', result)
	}

	// 日期范围选择取消回调
	const onDateRangeCancel = () => {
		console.log('取消选择租约时间')
	}
</script>
<style scoped>
	page {
		height: 100%;
		background-color: #f5f5f5;
	}

	.page {
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.header {
		background: #fff;
		padding: 0 30rpx;
		flex-shrink: 0;
	}

	.room-info {
		padding: 20rpx 0;
	}

	.room-name {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	.list-item {
		background: #f8f8f8;
		padding: 5rpx 20rpx;
	}

	.item-row {
		display: flex;
		font-size: 28rpx;
	}

	.item-label {}

	.item-name-input {
		flex: 1;

	}

	.item-price-input {
		flex: 1;
		margin-right: 10rpx;
	}

	.item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-height: 88rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.late_info {
		background: #f8f8f8;
		border-radius: 8px;
		padding: 20rpx;
		margin: 20rpx 0;
		cursor: pointer;
		transition: all 0.3s ease;
		border: 2px solid transparent;
	}

	.late_btn {
		font-size: 25rpx;
		padding: 10rpx;
		color: gray;
	}

	.contract-info {
		background: #f8f8f8;
		border-radius: 8px;
		padding: 20rpx;
		margin: 20rpx 0;
		cursor: pointer;
		transition: all 0.3s ease;
		border: 2px solid transparent;
	}

	.contract-info.active {
		background: #e8f5e8;
		border-color: #07c160;
		box-shadow: 0 2px 8px rgba(7, 193, 96, 0.2);
	}

	.contract-info.active .contract-title text {
		color: #07c160;
		font-weight: 500;
	}

	.contract-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.contract-title text {
		font-size: 14px;
		color: #333;
	}

	.help {
		color: #07c160 !important;
	}

	.contract-desc {
		font-size: 12px;
		color: #999;
		line-height: 1.5;
		margin-bottom: 16rpx;
	}

	.contract-number {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
		justify-content: space-between;
	}

	.contract-number text {
		font-size: 12px;
		color: #666;

	}

	.number {
		margin-left: 16rpx;
	}

	.contract-status {
		display: flex;
		align-items: center;
		background: #e8f7ed;
		padding: 16rpx;
		border-radius: 4px;
	}

	.contract-status text {
		margin-left: 8rpx;
		font-size: 14px;
		color: #07c160;
	}

	.content {
		flex: 1;
	}

	.form-section {
		background: #fff;
		margin-bottom: 20rpx;
		padding: 0 30rpx;
	}

	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		font-size: 14px;
		color: #333;
		font-weight: 500;
	}

	.required::before {
		content: '*';
		color: #ff4d4f;
		margin-right: 4rpx;
	}

	.add-btn {
		color: #07c160;
		font-size: 14px;
		font-weight: normal;
	}

	.form-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-height: 88rpx;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-right {
		display: flex;
	}

	.remark-item {
		display: flex;
		justify-content: space-between;
		align-content: center;
		padding: 20rpx 0;
	}

	.remark-input {
		flex: 1;
		height: 160rpx;
		font-size: 14px;
		color: #333;
		margin-left: 20rpx;
		border: 1px solid #eee;
		border-radius: 4px;
		padding: 10rpx;
		box-sizing: border-box;
	}

	.label {
		width: 250rpx;
		font-size: 14px;
		color: #333;
		flex-shrink: 0;
	}

	.value {
		font-size: 14px;
		color: #999;
		margin-right: 20rpx;
	}

	input {
		font-size: 14px;
		text-align: right;
		border: none;
		outline: none;
		width: 300rpx;
	}

	.gender-group {
		display: flex;
		gap: 20rpx;
	}

	.gender-item {
		padding: 10rpx 30rpx;
		border: 1px solid #ddd;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.gender-item.active {
		border-color: #07c160;
		color: #07c160;
		background-color: #e8f5e8;
	}

	.upload-area {
		flex: 1;
		width: calc(100%/3);
		height: 160rpx;
		border: 1px dashed #ddd;
		border-radius: 4px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.upload-area text {
		margin-top: 8rpx;
		font-size: 12px;
		color: #999;
	}

	.roommate-item {
		display: flex;
		align-items: center;
		height: 88rpx;
	}

	.roommate-item .name {
		width: 200rpx;
		font-size: 14px;
		color: #333;
	}

	.footer {
		padding: 20rpx 30rpx;
		background: #fff;
		display: flex;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}

	.submit-btn {
		width: 100%;
		background: #07c160;
		color: #fff;
		font-size: 16px;
	}

	/* 费用选择弹窗样式 */
	.fee-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #EBEDF0;
	}

	.cancel-btn {
		color: #666;
		font-size: 28rpx;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.confirm-btn {
		color: #07c160 !important;
		font-weight: 500 !important;
		font-size: 28rpx !important;
	}

	.fee-picker-container {
		padding: 24rpx;
	}

	.fee-picker {
		width: 100%;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.picker-text {
		display: block;
		font-size: 30rpx;
		color: #333;
		text-align: center;
		line-height: 76rpx;
	}
</style>