<template>
	<view class="room" @click="goRoom" :style='isOver?"background-color: orange;":"background-color: white;"'>
		<image class="door" src="../../static/door.png"></image>  
		<view class="info">
			<view class="title">
				<text class="num">{{info.name}}</text>
				<view>
					<uni-tag :inverted="true" text="合约快到期了" type="error" size="mini" style="margin-right: 20rpx;" v-if="isOver"/>
					<uni-tag :inverted="true" :text="getTip(info.status)" :type="getColor(info.status)" size="mini"/>
				</view>
			</view>
			<text class="time">合同时间：{{info.startTime || ''}} - {{info.endTime || ''}}</text>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
  info: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      num: '',
      src: '',
      startTime: '',
      endTime: '',
      state: ''
    })
  },
  type: {
    type: Number,
    default: 0
  },
  address: {
    type: String,
    default: ''
  },
  floor: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['callback']);

// 响应式数据
const state = ref(0);
const isOver = ref(false);

// 计算是否已过期
const calculateIsOver = () => {
  if (props.info.endTime) {
    const split = props.info.endTime.split("-");
    const year = Number(split[0]);
    const month = Number(split[1]) - 1;
    const day = Number(split[2]);
    isOver.value = getOverDate(year, month, day);
  }
};

// 页面加载后初始化状态
onMounted(() => {
  state.value = props.info.status;
  console.log("room load", state.value);
});

// 导航到房间详情或执行退房操作
const goRoom = () => {
  console.log('goRoom', props.info._id);
  
  if (props.type === 1) {
    uni.navigateTo({
      url: `/pagesB/add/addRoom?room_id=${props.info._id}&building_id=${props.info.building_id}&type=${props.type}&name=${props.info.name}&building_address=${props.address}&floor=${props.floor}`
    });
  } else if (props.type === 2) {
    uni.showModal({
      title: "确定退房吗？",
      content: "退房后，该房的当月水电会以当前记录的水电为准，租客信息会被删除",
      success: (res) => {
        console.log("退房", res);
        if (res.confirm) {
          emit("callback", props.info._id);
        }
      }
    });
  } else if (props.type === 3) {
    uni.navigateTo({
      url: `/pagesB/roomTimeout/roomTimeout?id=${props.info._id}`
    });
  } else {
    uni.navigateTo({
      url: `/pagesB/add/roomInfo?id=${props.info._id}&address=${props.address}`
    });
  }
};

// 获取状态提示文本
const getTip = (status) => {
  let tip = '';
  switch (status) {
    case 0:
      tip = "未出租";
      break;
    case 1:
      tip = "已出租";
      break;
    case 2:
      tip = "已到期";
      break;
    case 3:
      tip = "已超时";
      break;
    case 4:
      tip = "快到期";
      break;
    default:
      tip = '';
      break;
  }
  return tip;
};

// 获取状态对应的颜色类
const getColor = (status) => {
  let tip = '';
  switch (status) {
    case 0:
      tip = 'default';
      break;
    case 1:
      tip = 'primary';
      break;
    case 2:
      tip = 'warning';
      break;
    case 3:
      tip = 'error';
      break;
    case 4:
      tip = 'success';
      break;
  }
  return tip;
};

// 计算日期是否已过期
const getOverDate = (year, month, day) => {
  // 步骤 1: 创建一个当前日期的对象
  let currentDate = new Date(year, month, day); // 获取合同日期和时间
  let oneMonthAgo = new Date(currentDate);
  
  // 获取当前月份（注意：月份从0开始，0表示1月）
  oneMonthAgo.setMonth(month - 1); 
  
  // 步骤 3: 将月份减去一
  if (month === 0) {
    // 如果当前是1月
    year--; // 年份减去1
    month = 11; // 设置为12月
  } else {
    month--; // 否则，月份减去1
  }
  
  // 确保日期是有效的
  if (oneMonthAgo.getDate() !== currentDate.getDate()) {
    // 如果这个月没有同样的日期，则设置为上一个月最后一天
    oneMonthAgo.setDate(0); 
  }
  
  // 步骤 4: 创建一个新的日期对象
  let lastMonthDate = new Date(
    year,
    month,
    oneMonthAgo.getDate()
  ); 
  
  console.log("到期时间：", lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-' + lastMonthDate.getDate());
  return lastMonthDate.getTime() <= new Date().getTime();
};

// 监听props变化，更新过期状态
const watchProps = () => {
  calculateIsOver();
};

// 初始计算过期状态
calculateIsOver();
</script>

<style lang="scss" scoped>
	.room{
		padding: 10rpx;
		display: flex;
		background-color: white;
		margin-top: 5rpx;
		.door{
			width: 70rpx;
			height: 70rpx;
			justify-items: center;
		}
		.info{
			width: 100%;
			margin-left: 10rpx;
			.title{
				display: flex;
				font-size: 25rpx;
				justify-content: space-between;
				margin-right: 25rpx;
				.num{
					
				}
				.state{
					
				}
			}
			.time{
				font-size: 20rpx;
			}
		}
		
		
	}
	

</style>