<template>
  <view class="container">
    <view class="title">
      <text>总层数/总房间数</text>
      <text>{{ floor }}层/{{ num }}间</text>
      <button size="mini" @click="refreshData" style="margin-left: 20rpx">刷新数据</button>
    </view>
    <view class="tip">注意！！删除楼层将无法添加楼层，删除房间将会对房间的配置好的数据清空，请谨慎操作</view>
    <unicloud-db ref="udb" v-slot:default="{ data, loading, error, options }" collection="fangke_room" :where="sWhere" @load="onqueryload" @error="onqueryerror" :manual="true">
      <view v-if="error">{{ error.message }}</view>
      <view v-else-if="loading">正在加载...</view>
      <view v-else>
        <view v-if="floors.length === 0" class="empty-state">
          <text>暂无房间数据</text>
          <text>building_id: {{ building_id }}</text>
          <text>sWhere: {{ sWhere }}</text>
        </view>
        <view class="content" v-for="(item, index) in floors" :key="index">
          <uni-section :title="'第' + item.floor + '层'" type="line" class="heard" style="background-color: #edf2f4">
            <template v-slot:right>
              <view @click="delFloor(index)">删除楼层</view>
            </template>
          </uni-section>
          <view class="body">
            <view class="room" v-for="(item2, roomIndex) in item.rooms" :key="item2._id">
              <input class="num" v-model="item2.name" type="number" maxlength="5" @blur="change(item.rooms, item2)" @confirm="change(item.rooms, item2)" @focus="focus" />
              <view class="image">
                <image src="../../static/close.png" style="width: 100%; height: 100%" mode="aspectFit" @click="del(item.rooms, roomIndex)" />
              </view>
            </view>
          </view>
          <view class="foot">
            <button size="mini" style="margin: 20rpx" @click="add(item.rooms, index)">+添加房间</button>
          </view>
        </view>
      </view>
    </unicloud-db>

    <view class="bottom">
      <button class="left" @click="close">取消</button>
      <button class="right" type="primary" @click="save">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
const db = uniCloud.databaseForJQL();
// 响应式数据
const floor = ref(1);
const num = ref(1);
const floors = ref([]);
const roomsList = ref([]);
const name = ref('');
const origen = ref('');
const building_id = ref('');
const udb = ref(null);
const sWhere = ref('');
const building_name = ref('');
const removeList = ref([]);
// 移除未使用的colList定义，因为我们现在使用unicloud-db组件
// let colList = [
// 	db.collection('fangke_room').where(`building_id =="${building_id.value}"`).getTemp(),
// ]
// 页面加载时处理参数
onLoad((e) => {
  console.log('buidlingRoom', e);
  if (e.building_id) {
    building_id.value = e.building_id;
    sWhere.value = `building_id =="${building_id.value}"`;
    console.log('设置building_id:', building_id.value);
    console.log('设置sWhere:', sWhere.value);
    // 如果组件已经准备好，立即加载数据
    if (udb.value) {
      udb.value.loadData();
    }
  } else {
    console.error('未接收到building_id参数');
    uni.showToast({
      title: '缺少building_id参数',
      icon: 'none'
    });
  }
});

onReady(() => {
  console.log('onReady - building_id:', building_id.value);
  console.log('onReady - sWhere:', sWhere.value);
  // 如果building_id已经设置，加载数据
  if (building_id.value && sWhere.value) {
    console.log('onReady - 开始加载数据');
    udb.value.loadData(); //手动加载数据
  } else {
    console.log('onReady - 条件不满足，不加载数据');
  }
});

const onqueryload = (data, ended) => {
  // `data` 当前查询结果
  // `ended` 是否有更多数据
  // 可在此处预处理数据，然后再渲染界面
  console.log('onqueryload', data);
  console.log('building_id:', building_id.value);
  console.log('sWhere:', sWhere.value);

  roomsList.value = data;
  if (data.length > 0) {
    building_name.value = data[0].building_name;
  }
  let res = groupRoomsByFloor(data);
  console.log('排列后的房间', res);
  floor.value = res.length;
  num.value = data.length;
  floors.value = res;
};
const onqueryerror = (e) => {
  console.log('onqueryerror', e);
  console.log('building_id:', building_id.value);
  console.log('sWhere:', sWhere.value);
  uni.showToast({
    title: '数据加载失败: ' + e.message,
    icon: 'none'
  });
};

const groupRoomsByFloor = (data) => {
  // 创建一个Map来按楼层分组房间
  const floorMap = new Map();

  // 遍历每个房间，将它们添加到对应的楼层组
  data.forEach((room) => {
    const floor = room.floor;
    if (!floorMap.has(floor)) {
      floorMap.set(floor, {
        floor,
        rooms: []
      });
    }
    floorMap.get(floor).rooms.push(room);
  });

  // 将Map转换为数组并按楼层排序
  const result = Array.from(floorMap.values()).sort((a, b) => a.floor - b.floor);
  console.log('groupRoomsByFloor', result);
  return result;
};

// 删除楼层
const delFloor = (index) => {
  console.log('delFloor', floors.value[index]);
  floors.value[index].rooms.forEach((item) => {
    if (item._id) {
      removeList.value.push(item._id);
    }
  });
  floors.value.splice(index, 1);
  floor.value--;
};

// 删除房间
const del = (rooms, roomIndex) => {
  if (rooms[roomIndex]._id) {
    removeList.value.push(rooms[roomIndex]._id);
  }
  rooms.splice(roomIndex, 1);
  num.value--;
};

// 添加房间
const add = (rooms, floorIndex) => {
  let l = rooms.length + 1;
  let f = floorIndex + 1;
  let room = {
    floor: f,
    name: l < 10 ? f + '0' + l : f + '' + l,
    building_id: building_id.value,
    building_name: building_name.value
  };
  rooms.push(room);
  num.value++;
};

// 更改房间名称
const change = (rooms, room) => {
  let map = rooms.map(function (item) {
    return item.name === room.name;
  });
  console.log('map', map);

  let sum = 0;
  map.forEach(function (element) {
    if (element) {
      sum = sum + 1;
    }
  });

  console.log('sum', sum);
  if (sum > 1) {
    uni.showToast({
      title: '修改的房名已重复，请重新修改',
      icon: 'none'
    });
    console.log(rooms.value[floorIndex][roomIndex].name);
    room.name = origen.value;
  }
};

// 刷新数据
const refreshData = () => {
  console.log('手动刷新数据');
  console.log('building_id:', building_id.value);
  console.log('sWhere:', sWhere.value);
  if (udb.value && building_id.value) {
    udb.value.loadData();
  } else {
    uni.showToast({
      title: 'building_id未设置',
      icon: 'none'
    });
  }
};

// 关闭页面
const close = () => {
  uni.navigateBack();
};

// 保存房间数据
const save = async () => {
  console.log('原房间', roomsList.value);
  console.log('修改后房间', floors.value);
  console.log('删除的房间', removeList.value);
  let updateList = [];
  let addList = [];
  floors.value.forEach((item, index) => {
    item.rooms.forEach((item2) => {
      if (item2._id) {
        roomsList.value.forEach((item3) => {
          if (item2._id == item3._id) {
            if (item2.name !== item3.name) {
              updateList.push(item2);
            }
          }
        });
      } else {
        addList.push(item2);
      }
    });
  });
  updateList.forEach((item) => {
    db.collection('fangke_room')
      .where(`_id == "${item._id}"`)
      .update({
        name: item.name
      })
      .then((res) => {
        console.log('更新成功', item._id);
      });
  });
  if (addList.length > 0) {
    await db
      .collection('fangke_room')
      .add(addList)
      .then((res) => {
        console.log('添加成功', res);
      });
  }
  if (removeList.value.length > 0) {
    await db
      .collection('fangke_room')
      .where(`_id in ['${removeList.value.join("','")}'] `)
      .remove()
      .then((res) => {
        console.log('删除成功', res);
      });
  }
  console.log('操作完成');
  uni.reLaunch({
    url: '/pages/house/house'
  });
};

// 输入框聚焦事件
const focus = (e) => {
  console.log('focus', e);
  origen.value = e.detail.value;
};
</script>
<style lang="scss">
.container {
  padding: 10rpx 20rpx;
  box-sizing: border-box;

  .title {
    margin: 20rpx;
    display: flex;
    justify-content: space-between;
  }

  .tip {
    font-size: 28rpx;
    background-color: orange;
    color: white;
    padding: 20rpx;
  }
  .content {
    display: flex;
    flex-direction: column;

    .head {
      background-color: darkgray;
    }

    .body {
      display: grid;
      grid-template-columns: auto auto auto auto;

      .room {
        width: max-content;
        position: relative;
        margin: 20rpx;

        .num {
          display: flex;
          padding: 10rpx;
          border: 1rpx solid gray;
          align-content: center;
          align-items: center;
          width: 100rpx;
          border-radius: 10rpx;
        }

        .image {
          position: absolute;
          top: -15rpx;
          right: -15rpx;
          width: 40rpx;
          height: 40rpx;
          z-index: 10;
        //   background: rgba(255,0,0,0.2); // 调试用，后续可去掉
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .foot {
      box-sizing: border-box;

      .slide {
        width: 100%;
        height: 2rpx;
        background-color: #eee;
        margin: 0 20rpx;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40rpx;
    color: #999;

    text {
      display: block;
      margin-bottom: 10rpx;
    }
  }

  .bottom {
    display: flex;
    margin: 20rpx 0rpx;

    button {
      padding: 0rpx 40rpx;
    }
  }
}
</style>
