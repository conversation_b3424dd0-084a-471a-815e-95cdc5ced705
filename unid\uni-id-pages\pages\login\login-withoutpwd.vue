<!-- 免密登录页 -->
<template>
	<view class="uni-content">
		<view class="login-logo">
			<image :src="logo"></image>
		</view>
		<!-- 顶部文字 -->
		<text class="title">请选择登录方式</text>
		<!-- 快捷登录框 当url带参数时有效 -->
		<template v-if="['apple','weixin', 'weixinMobile', 'huawei', 'huaweiMobile'].includes(type)">
			<text class="tip">将根据第三方账号服务平台的授权范围获取你的信息</text>
			<view class="quickLogin">
				<image v-if="type !== 'weixinMobile' && type !== 'huaweiMobile'" @click="quickLogin" :src="imgSrc"
					mode="widthFix" class="quickLoginBtn"></image>
				<view v-else style="position: relative">
					<button v-if="type ==='weixinMobile'" type="primary" open-type="getPhoneNumber"
						@getphonenumber="quickLogin" class="uni-btn">微信授权手机号登录</button>
					<button v-if="type === 'huaweiMobile'" open-type="getPhoneNumber" @getphonenumber="quickLogin"
						class="quickLoginBtn" style="padding: 0; display: flex">
						<image :src="imgSrc" mode="widthFix"></image>
					</button>
					<view  v-if="needAgreements && !agree" class="mobile-login-agreement-layer"
						@click="showAgreementModal"></view>
				</view>
				<uniIdPagesAgreements scope="register" ref="agreements"></uniIdPagesAgreements>
			</view>
		</template>
		<template v-else>
			<text class="tip">未注册的账号验证通过后将自动注册</text>
			<view class="phone-box">
				<view @click="chooseArea" class="area">+86</view>
				<uni-easyinput trim="both" :focus="focusPhone" @blur="focusPhone = false" class="input-box"
					type="number" :inputBorder="false" v-model="phone" maxlength="11" placeholder="请输入手机号" />
			</view>
			<uniIdPagesAgreements scope="register" ref="agreements"></uniIdPagesAgreements>
			<button class="uni-btn" type="primary" @click="toSmsPage">获取验证码</button>
		</template>
		<!-- 固定定位的快捷登录按钮 -->
		<uniIdPagesFabLogin ref="uniFabLogin" v-if="false"></uniIdPagesFabLogin>
	</view>
</template>


<script setup>
	import {
		ref,
		computed,
		getCurrentInstance,
		nextTick,
		onMounted
	} from 'vue'
	import {
		onLoad,
		onUnload,
		onShow,
		onReady
	} from '@dcloudio/uni-app'
	import config from '@/unid/uni-id-pages/config.js'
	import mixin from '@/unid/uni-id-pages/common/login-page.mixin.js';
	import uniIdPagesFabLogin from '../../components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue'
	import uniIdPagesAgreements from '../../components/uni-id-pages-agreements/uni-id-pages-agreements.vue'
	import { wxlogin,loginSuccess } from '@/utils/js/common.js'
	const uniFabLogin = ref(null)
	const agreements = ref(null)
	// 导入 mixin 功能
	const {
		needAgreements,
		agree
	} = mixin.data()

	// 响应式数据
	const type = ref("") // 快捷登录方式
	const phone = ref("") // 手机号码
	const focusPhone = ref(false)
	const logo = ref("/static/logo.png")
	let currentWebview = null // 当前窗口对象
	const uniIdCo = uniCloud.importObject("uni-id-co")
	// 计算属性
	const loginTypes = computed(async () => {
		return config.loginTypes
	})

	const isPhone = computed(() => {
		return /^1\d{10}$/.test(phone.value)
	})

	const imgSrc = computed(() => {
		const images = {
			weixin: '/unid/uni-id-pages/static/login/weixin.png',
			apple: '/unid/uni-id-pages/static/app/apple.png',
			huawei: '/unid/uni-id-pages/static/login/huawei.png',
			huaweiMobile: '/unid/uni-id-pages/static/login/huawei-mobile.png',
		}
		return images[type.value]
	})

	// 生命周期钩子
	onLoad(async (e) => {
		// 获取通过url传递的参数type设置当前登录方式，如果没传递直接默认以配置的登录
		let typeVal = e.type || config.loginTypes[0]
		type.value = typeVal
		console.log("this.type: -----------",type.value);
		if (typeVal != 'univerify') {
			focusPhone.value = true
		}

		nextTick(() => {
			// 关闭重复显示的登录快捷方式
			if (['weixin', 'apple', 'huawei', 'huaweiMobile'].includes(typeVal) && uniFabLogin.value) {
				uniFabLogin.value.servicesList = uniFabLogin.value.servicesList.filter(
					item =>
					item.id != typeVal
				)
			}
		})

		uni.$on('uni-id-pages-setLoginType', typeVal => {
			type.value = typeVal
		})
	})

	onShow(() => {
		// #ifdef H5
		document.onkeydown = event => {
			var e = event || window.event;
			if (e && e.keyCode == 13) { // 回车键的键值为13
				toSmsPage()
			}
		};
		// #endif
	})

	onUnload(() => {
		uni.$off('uni-id-pages-setLoginType')
	})

	onReady(() => {
		// 是否优先启动一键登录。即：页面一加载就启动一键登录
		//#ifdef APP-PLUS
		if (config.loginTypes.includes('univerify') && type.value == "univerify") {
			uni.preLogin({
				provider: 'univerify',
				success: () => {
					const pages = getCurrentPages();
					currentWebview = pages[pages.length - 1].$getAppWebview();
					currentWebview.setStyle({
						"top": "2000px" // 隐藏当前页面窗体
					})
					if (uniFabLogin.value) {
						uniFabLogin.value.login_before('univerify')
					}
				},
				fail: (err) => {
					console.log(err);
					if (config.loginTypes.length > 1 && uniFabLogin.value) {
						uniFabLogin.value.login_before(config.loginTypes[1])
					} else {
						uni.showModal({
							content: err.message,
							showCancel: false
						});
					}
				}
			})
		}
		//#endif
	})

	// 方法
	const showCurrentWebview = () => {
		// 恢复当前页面窗体的显示 一键登录，默认不显示当前窗口
		if (currentWebview) {
			currentWebview.setStyle({
				"top": 0
			})
		}
	}

	const showAgreementModal = () => {
		agreements.value.popup()
	}
	

	const quickLogin = async(e) => {
		if(!agreements.value.isAgree){
			showAgreementModal()
			return
		}
		let options = {}
		console.log(e)
		if (e.detail?.code) {
			options.phoneNumberCode = e.detail.code
		}

		if ((type.value === 'weixinMobile' || type.value === 'huaweiMobile') && !e.detail?.code) return
		console.log("uniFabLogin ===",  options);
		let result = await wxlogin(options.phoneNumberCode).then(res => {
				console.log("登录成功",res);
				return true
			}).catch(err => {
				console.log("登录失败", err);
				return false
			})
		if(result){
			let isRegister = uni.getStorageSync("isRegister")
			console.log("是否注册流程",isRegister);
			if(isRegister){
				await uniIdCo.bindMobileByMpWeixin({
					code: options.phoneNumberCode
				}).then((res) => {
					let {
						errCode
					} = res
					if (errCode == 0) {
						console.log("bindMobileByMpWeixin 绑定成功",res);
					} else {
						console.log("bindMobileByMpWeixin 绑定失败", res);
					}
				}).catch((err) => {
					console.log("bindMobileByMpWeixin 绑定失败", err);
				})
			}
			loginSuccess()
			// uni.navigateBack()
		}
		
		// uniFabLogin.value.login_before(type.value, true, options)
	}

	const toSmsPage = () => {
		if (!isPhone.value) {
			focusPhone.value = true
			return uni.showToast({
				title: "手机号码格式不正确",
				icon: 'none',
				duration: 3000
			});
		}

		if (needAgreements.value &&!agree.value) {
			return agreements.value.popup(toSmsPage)
		}

		// 发送验证吗
		uni.navigateTo({
			url: '/unid/uni-id-pages/pages/login/login-smscode?phoneNumber=' + phone.value
		});
	}

	// 去密码登录页
	const toPwdLogin = () => {
		uni.navigateTo({
			url: '../login/password'
		})
	}

	const chooseArea = () => {
		uni.showToast({
			title: '暂不支持其他国家',
			icon: 'none',
			duration: 3000
		});
	}
	
</script>

<style lang="scss" scoped>
	@import "@/unid/uni-id-pages/common/login-page.scss";

	@media screen and (min-width: 690px) {
		.uni-content {
			height: 350px;
		}
	}

	.mobile-login-agreement-layer {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}

	.uni-content,
	.quickLogin {
		/* #ifndef APP-NVUE */
		display: flex;
		flex-direction: column;
		/* #endif */
	}

	.phone-box {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
	}

	.area {
		position: absolute;
		left: 10px;
		z-index: 9;
		top: 12px;
		font-size: 14px;
	}

	.area::after {
		content: "";
		border: 3px solid transparent;
		border-top-color: #000;
		top: 12px;
		left: 3px;
		position: relative;
	}

	/* #ifdef MP */
	// 解决小程序端开启虚拟节点virtualHost引起的 class = input-box丢失的问题 [详情参考](https://uniapp.dcloud.net.cn/matter.html#%E5%90%84%E5%AE%B6%E5%B0%8F%E7%A8%8B%E5%BA%8F%E5%AE%9E%E7%8E%B0%E6%9C%BA%E5%88%B6%E4%B8%8D%E5%90%8C-%E5%8F%AF%E8%83%BD%E5%AD%98%E5%9C%A8%E7%9A%84%E5%B9%B3%E5%8F%B0%E5%85%BC%E5%AE%B9%E9%97%AE%E9%A2%98)
	.phone-box ::v-deep .uni-easyinput__content,
	/* #endif */
	.input-box {
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		/* #endif */
		flex: 1;
		padding-left: 45px;
		margin-bottom: 10px;
		border-radius: 0;
	}

	.quickLogin {
		height: 350px;
		align-items: center;
		justify-content: center;
	}

	.quickLoginBtn {
		margin: 20px 0;
		width: 450rpx;
		background-color: transparent;
		border: none;
		box-shadow: none;
		/* #ifndef APP-NVUE */
		max-width: 230px;
		/* #endif */
		height: 82rpx;
	}

	.tip {
		margin-top: -15px;
		margin-bottom: 20px;
	}

	@media screen and (min-width: 690px) {
		.quickLogin {
			height: auto;
		}
	}
</style>