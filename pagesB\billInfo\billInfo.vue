<template>
	<view class="container">
		<view class="title">
			<text>{{bill.room_name+"房"}}</text>
		</view>
		<view class="head">
			<text>地址:{{bill.address}}</text>
			<text>{{time}}</text>
		</view>
		<lkj-collapse :collapse-data="contentData" :fontSize="45" :contentFontSize="40"></lkj-collapse>
		<view class="desc">
			<text style="color: red;font-size: 30rpx;">备注：{{desc}}</text>
		</view>
		<view class="bottom">
			<view class="sum">
				实收金额：<text
					style="color: red;font-size: 50rpx;font-weight: bold;">{{bill.money?bill.money/100:0}}元</text>
			</view>
		</view>
		<view class="foot">
			<button type="default">编辑账单</button>
			<!-- #ifdef MP-WEIXIN -->
			<button type="primary" open-type="share">分享账单</button>
			<!-- #endif -->
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import {
		onShareAppMessage
	} from '@dcloudio/uni-app'
	import uniCloud from '@dcloudio/uni-cloud';

	const db = uniCloud.databaseForJQL();

	// 响应式数据
	const contentData = ref([]);
	const time = ref("2024年12月12日");
	const billId = ref('');
	const bill = ref({});
	const year = ref(0);
	const month = ref(0);
	const day = ref(0);
	const info = ref({});
	const desc = ref("");

	// 获取 URL 参数
	const route = useRoute();
	const {
		bill_id
	} = route.query;

	// 页面加载时处理参数
	onMounted(() => {
		console.log("BillInfo", bill_id);
		if (bill_id) {
			billId.value = bill_id;
		}
		getBill();
	});

	// 分享功能
	onShareAppMessage(() => {
		return {
			title: bill.value.room_name + "房账单",
			path: '/pagesB/billInfo/billInfo?bill_id=' + billId.value,
			content: time.value + "的房租账单出来了",
			success: (res) => {
				console.log("分享完成", res);
			},
			fail: (res) => {
				console.log("分享失败", res);
			}
		};
	});

	// 获取账单信息
	const getBill = () => {
		db.collection("fangke_bill").doc(billId.value).get().then(res => {
			console.log("getBill", res);
			if (res.data.length) {
				bill.value = res.data[0];
				let now = new Date(bill.value.update_time);
				year.value = now.getFullYear();
				month.value = now.getMonth() + 1;
				day.value = now.getDate();
				time.value = year.value + "年" + month.value + "月" + day.value + "日";
			}
		}).finally(res => {
			let shuidian = db.collection("fangke_shuidian").where(
				`room_id=="${bill.value.room_id}"&&month==${month.value}&&year==${year.value}`
			).field("mode_id,water,ele,loss_ele,loss_water,last_ele,last_water,rent,cash").getTemp();
			let mode = db.collection("fangke_price_model").getTemp();
			db.collection(shuidian, mode).get().then(res => {
				console.log("联表查询", res);
				if (res.data.length) {
					info.value = res.data[0];
					updateBill(info.value);
				}
			}).catch(err => {
				console.error(err);
			});
		});
	};

	// 更新账单信息
	const updateBill = (info) => {
		console.log("updateBill", info);
		let mode = info.mode_id.fangke_price_model[0];
		let ele_p = (info.ele - info.last_ele) / 100 * (mode.ele_p / 100);
		let ele_loss = info.loss_ele / 100 * mode.loss_ele / 100;
		let water_p = (info.water - info.last_water) / 100 * (mode.water_p / 100);
		let water_loss = info.loss_water / 100 * mode.loss_water / 100;
		let rent = info.rent / 100;
		let other_p = (mode.manager_p + mode.net_p + mode.rubbish_p + mode.tv_p + mode.other_p) / 100;

		contentData.value = [{
				title: "电费",
				list: true,
				bold: true,
				right: "￥" + (ele_p + ele_loss),
				content: {
					body: [{
							name: '上月电表',
							value: (info.last_ele / 100) + '',
						},
						{
							name: '本月电表',
							value: (info.ele / 100) + '',
						},
						{
							name: '实用(单价/度:' + mode.ele_p / 100 + ')',
							value: (info.ele - info.last_ele) / 100 + '度',
						},
						{
							name: '电力设备损耗(单价/度:' + mode.loss_ele / 100 + ')',
							value: info.loss_ele / 100 + '度',
						},
						{
							name: '合计',
							value: '￥' + (ele_p + ele_loss),
						},
					]
				}
			},
			{
				title: "水费",
				list: true,
				bold: true,
				right: "￥" + (water_p + water_loss),
				content: {
					body: [{
							name: '上月水表',
							value: (info.last_water / 100) + '',
						},
						{
							name: '本月水表',
							value: (info.water / 100) + '',
						},
						{
							name: '实用(单价/吨:' + mode.water_p / 100 + ')',
							value: (info.water - info.last_water) / 100 + '吨',
						},
						{
							name: '水表设备损耗(单价/吨:' + mode.loss_water / 100 + ')',
							value: info.loss_water / 100 + '吨',
						},
						{
							name: '合计',
							value: '￥' + (water_p + water_loss),
						},
					]
				}
			},
			{
				title: "房租",
				list: true,
				bold: true,
				right: "￥" + rent,
				content: {
					body: [{
							name: '租房时间(' + year.value + '/' + month.value + '/' + day.value + '-' + year
								.value + '/' + (month.value + 1) + '/' + day.value + ')',
							value: '1个月',
						},
						{
							name: '房租',
							value: '' + rent,
						},
						{
							name: '合计',
							value: '￥' + rent,
						},
					]
				}
			},
			{
				title: "其他费用",
				list: true,
				bold: true,
				right: "￥" + other_p,
				content: {
					body: [{
							name: '管理费',
							value: '' + mode.manager_p,
						},
						{
							name: '卫生费',
							value: '' + mode.rubbish_p,
						},
						{
							name: '电视费',
							value: '' + mode.tv_p,
						},
						{
							name: '网费',
							value: '' + mode.net_p,
						},
						{
							name: '合计',
							value: '￥' + other_p,
						},
					]
				}
			}
		];

		uni.showShareMenu({
			title: bill.value.room_name + "房" + month.value + "月账单",
			content: time.value + "的房租账单出来了",
			success: (res) => {
				console.log("分享完成", res);
			},
			fail: (res) => {
				console.log("分享失败", res);
			}
		});
	};
</script>

<style lang="scss">
	.container {
		box-sizing: border-box;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		background:
			linear-gradient(to bottom, transparent, #fff 500rpx),
			linear-gradient(to right, #beecd8 20%, #F4E2D8);
		min-height: 80vh;

		.title {
			display: flex;
			justify-content: center;
			margin: 10rpx 20rpx;
			font-size: 40rpx;
			font-weight: 550;
		}

		.head {
			display: flex;
			justify-content: space-between;
			margin: 0 20rpx;
		}

		.bottom {
			display: flex;
			padding: 20rpx;
			flex-direction: column;

			.sum {
				font-size: 30rpx;
				display: flex;
				align-items: baseline;
				justify-content: end;
			}
		}

		.foot {
			margin: 20rpx;
			display: flex;
			justify-content: space-around;
		}

		.desc {
			margin: 10rpx 20rpx;
		}
	}
</style>