<template>
	<view class="floor">
		<view v-if="floor" class="ceng">
			第{{floor.floor}}层
		</view>
		<view class="rooms" v-for="(item, num) in floor.rooms" :key="num">
			<room :info="item" :type="type" @callback="getResult" :address="address" :floor="floor.floor">
			</room>
		</view>
	</view>
</template>

<script setup>
	import {
		defineProps,
		defineEmits
	} from 'vue';

	const props = defineProps({
		floor: {
			type: Object,
			default: () => ({})
		},
		type: {
			type: Number,
			default: null
		},
		address: {
			type: String,
			default: ''
		}
	});

	const emit = defineEmits(['callback']);

	const getResult = (e) => {
		emit('callback', e);
	};
</script>

<style lang="scss" scoped>
	.floor {
		display: flex;
		/* 使用flex布局 */
		flex-direction: column;
		/* 列方向排列 */
		background-color: #eee;

		.ceng {
			margin: 10rpx 20rpx;
			font-size: 30rpx;
			font-weight: 550;
		}
	}
</style>