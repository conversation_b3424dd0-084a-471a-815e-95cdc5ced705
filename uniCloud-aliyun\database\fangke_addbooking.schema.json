// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
    "description": "添加预定表",
    "bsonType": "object",
    "required": ["uid", "name", "phone", "sex", "money", "target_date", "canceling_date", "room_id"],
    "permission": {
        "read": "auth.uid != null",
        "create": "auth.uid != null",
        "update": "auth.uid != null",
        "delete": "auth.uid != null"
    },
    "properties": {
        "_id": {
            "description": "ID，系统自动生成"
        },
        "uid": {
            "bsonType": "string",
            "description": "用户ID",
            "foreignKey": "uni-id-users._id",
            "forceDefaultValue": {
                "$env": "uid"
            }
        },
        "create_time": {
            "bsonType": "timestamp",
            "description": "创建时间",
            "forceDefaultValue": {
                "$env": "now"
            }
        },
        "update_time": {
            "bsonType": "timestamp",
            "description": "更新时间",
            "forceDefaultValue": {
                "$env": "now"
            }
        },
        "name": {
            "bsonType": "string",
            "label": "预定人姓名",
            "description": "预定人姓名"
        },
        "phone": {
            "bsonType": "string",
            "label": "预定人电话",
            "description": "预定人电话"
        },
        "sex": {
            "bsonType": "int",
            "label": "性别",
            "description": "性别：0-男 1-女",
            "defaultValue": 0
        },
        "money": {
            "bsonType": "int",
            "label": "预定金额",
            "description": "预定金额"
        },
        "target_date": {
            "bsonType": "string",
            "label": "预定日期",
            "description": "预定日期"
        },
        "canceling_date": {
            "bsonType": "string",
            "label": "最晚签约日",
            "description": "最晚签约日"
        },
        "comment": {
            "bsonType": "string",
            "label": "备注",
            "description": "备注"
        },
        "room_id": {
            "bsonType": "string",
            "description": "房间ID",
            "foreignKey": "fangke_room._id"
        },
        "status": {
            "bsonType": "int",
            "label": "预定状态",
            "description": "预定状态：0-待预定 1-已预定 2-预约取消 3-预约过期",
            "defaultValue": 1
        },
        "building_name": {
            "bsonType": "string",
            "label": "楼栋名称",
            "description": "楼栋名称"
        },
        "room_name": {
            "bsonType": "string",
            "label": "房间名称",
            "description": "房间名称"
        },
		"payment_status": {
		    "bsonType": "int",
		    "label": "收款状态",
		    "description": "预定状态：0-待收款 1-已收款 2-已作废,3-已退款",
		    "defaultValue": 0
		},
		"handler": {
			"bsonType": "string",
			"label": "经办人",
			"description": "经办人"
		}
    }
    
}