let reg = /^(\d{4})\d+(\d{4})$/ // 银行卡 只显示前四位后四位 中间用*代替
let inputNumber = /[^\d]/g      // 只能输入数字
let inputLetter = /[^a-zA-Z]/g      // 只能输入字母
let inputNumberLetter = /[\W]/g    // 只能输入数字和字母
let inputIdCard = /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
let inputBankCard = /^([1-9]{1})(\d{15}|\d{18})$/
let inputVin = /^[\da-zA-Z]{7,8}$/
let phone = /^1[3-9]\d{9}$/
let inputAccount = /^[0-9]{12,21}$/
let inputMoney = /^[0-9]{12,21}$/
let name = /^[\u4e00-\u9fa5]{2,}$/ //公司名称
let globalNo = /^[A-Z0-9]{8,}$/ //公司证件号码
let address = /^[\u4e00-\u9fa5]{2,}$/ //公司注册地址

let username =  /\S/
let password = /^[0-9a-zA-Z](6-16)$/


export const account = str => {
  return username.test(str)
}

export const userPassword = str => {
  return password.test(str)
}

export const userPhone = str =>{
	return phone.test(str)
}