<template>
	<view>
		<view class="container">
			<view class="img" v-if="isShow" @click="select">
				<image :src="isSelect?'../../../static/check_1.png':'../../../static/check_0.png'"
					style="width: 100%;height: 100%;">
				</image>
			</view>

			<view class="content" @click="onClick">
				<view class="title">
					<text style="font-size: 25rpx;">{{info.title}}</text>
					<text style="font-size: 20rpx;">{{'地址：'+info.address}}</text>
				</view>
				<view class="right">
					<image src="../../../static/arrow_right.png" mode="aspectFit"></image>
				</view>
			</view>

		</view>
		<view style="width: 100%;height: 1px;background-color: #eee;"></view>
	</view>


</template>

<script setup>
	import {
		ref,
		onMounted,
		onUnmounted,
		watch
	} from 'vue';

	// 定义props
	const props = defineProps({
		isShow: {
			type: Boolean,
			default: false,
			required: false
		},
		info: {
			type: Object,
			default: () => ({}),
			required: true
		}
	});

	// 定义组件状态
	const isSelect = ref(false);

	// 生命周期钩子 - 组件挂载后
	onMounted(() => {
		console.log("modeList mounted");
		uni.$on("stop", reset);
	});

	// 生命周期钩子 - 组件卸载前
	onUnmounted(() => {
		console.log("销毁监听");
		uni.$off("stop", reset);
	});

	// 定义方法
	const emit = defineEmits(['check', 'getData']);

	const select = () => {
		if (props.isShow) {
			isSelect.value = !isSelect.value;
			emit('check', props.info._id);
		}
	};

	const onClick = () => {
		if (props.isShow) {
			return;
		}
		const infoWithSelect = {
			...props.info,
			select: isSelect.value
		};
		emit('getData', infoWithSelect);
	};

	const reset = () => {
		console.log("重置reset");
		isSelect.value = false;
	};

	// 监听props变化
	watch(() => props.isShow, (newVal) => {
		if (!newVal) {
			reset();
		}
	});
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		display: flex;
		align-content: center;

		.img {
			width: 50rpx;
			height: 50rpx;

		}

		.content {
			margin-left: 10rpx;
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-content: center;

			.title {
				width: 100%;
				display: flex;
				flex-direction: column;
			}

			.right {
				width: 30rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
</style>