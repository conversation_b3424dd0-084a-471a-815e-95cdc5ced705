<template>
	<view class="page">
		<!-- 头部信息 -->
		<!-- <view class="header-section">
			<view class="header-title">水电审核</view>
			<view class="header-subtitle">审核已抄录但未提交的水电记录</view>
		</view> -->

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="stat-number">{{ auditRecords.length }}</text>
				<text class="stat-label">待审核记录</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ roomCount }}</text>
				<text class="stat-label">涉及房间</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ buildingCount }}</text>
				<text class="stat-label">涉及楼房</text>
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-section">
			<view class="filter-item" @click="showBuildingFilter">
				<text class="filter-text">{{ selectedBuilding }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showDeviceFilter">
				<text class="filter-text">{{ selectedDevice }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showDateFilter">
				<text class="filter-text">{{ selectedDate }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
		</view>

		<!-- 多选控制栏 -->
		<view class="selection-bar" v-if="filteredRecords.length > 0">
			<view class="selection-left">
				<view class="select-all" @click="toggleSelectAll">
					<uni-icons :type="isAllSelected ? 'checkbox-filled' : 'checkbox'"
						:color="isAllSelected ? '#07c160' : '#c8c9cc'" size="18" />
					<text class="select-text">全选</text>
				</view>
				<text class="selected-count">已选择 {{ selectedRecords.length }} 项</text>
			</view>
			<view class="selection-right">
				<text class="clear-selection" @click="clearSelection" v-if="selectedRecords.length > 0">清空</text>
			</view>
		</view>

		<!-- 表头 -->
		<view class="table-header">
			<text class="header-cell ">选择</text>
			<text class="header-cell building-name">楼房</text>
			<text class="header-cell room-name">房间</text>
			<text class="header-cell device-type">设备</text>
			<text class="header-cell usage-amount">用量</text>
			<text class="header-cell record-time">抄录时间</text>
		</view>

		<!-- 记录列表 -->
		<view class="record-list">
			<view v-for="(record, index) in filteredRecords" :key="record._id" class="record-item"
				:class="{ selected: isRecordSelected(record._id) }">
				<view class="checkbox-cell" @click="toggleRecordSelection(record)">
					<uni-icons :type="isRecordSelected(record._id) ? 'checkbox-filled' : 'checkbox'"
						:color="isRecordSelected(record._id) ? '#07c160' : '#c8c9cc'" size="18" />
				</view>
				<view class="record-index">
					<view class="record-info">
						<text class="building-name">{{ record.building_name }}</text>
						<text class="room-name">{{ record.room_name }}</text>
						<text class="device-type">{{ getDeviceName(record.type) }}</text>
						<text class="usage-amount">{{ formatUsage(record.usage) }}</text>
						<text class="record-time">{{ formatDate(record.record_date) }}</text>
					</view>
					<view class="record-details">
						<text class="detail-item">上次读数：{{ formatNumber(record.last_num) }}</text>
						<text class="detail-item">当前读数：{{ formatNumber(record.num) }}</text>
						<text class="detail-item">抄录月份：{{ record.date }}</text>
					</view>
				</view>

			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="filteredRecords.length === 0" class="empty-state">
			<image class="empty-icon" src="/static/empty.png" mode="aspectFit" />
			<text class="empty-text">暂无待审核记录</text>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-actions" v-if="filteredRecords.length > 0">
			<button class="audit-btn" @click="submitAudit" :disabled="selectedRecords.length === 0"
				:class="{ disabled: selectedRecords.length === 0 }">
				处理记录 ({{ selectedRecords.length }}条)
			</button>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom" background-color="#fff">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeFilterPopup">取消</text>
					<text class="popup-title">{{ filterTitle }}</text>
					<text class="confirm-btn" @click="confirmFilter">确定</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" v-for="(option, index) in currentFilterOptions" :key="index"
						:class="{ active: selectedFilterIndex === index }" @click="selectFilterOption(index)">
						<text class="option-text">{{ option.text }}</text>
						<uni-icons v-if="selectedFilterIndex === index" type="checkmarkempty" size="16"
							color="#07c160" />
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { dayjs } from '../../utils/dayjs.min'

const db = uniCloud.databaseForJQL()

// 响应式数据
const auditRecords = ref([])
const buildingList = ref([])
const userId = ref('')
const selectedBuilding = ref('全部楼房')
const selectedDevice = ref('全部设备')
const selectedDate = ref('全部月份')

// 多选相关数据
const selectedRecords = ref([])  // 已选择的记录ID数组

// 筛选弹窗相关
const filterPopup = ref(null)
const filterTitle = ref('')
const currentFilterOptions = ref([])
const selectedFilterIndex = ref(0)
const currentFilterType = ref('')

// 筛选选项
const deviceOptions = [
	{ text: '全部设备', value: 'all' },
	{ text: '冷水表', value: 1 },
	{ text: '电表', value: 2 },
	{ text: '燃气表', value: 3 }
]

// 楼房选项 - 动态生成
const buildingOptions = computed(() => {
	const options = [{ text: '全部楼房', value: 'all' }]
	buildingList.value.forEach(building => {
		options.push({ text: building.name || building.number, value: building._id })
	})
	return options
})

// 日期选项 - 动态生成
const dateOptions = computed(() => {
	const options = [{ text: '全部月份', value: 'all' }]
	const dates = [...new Set(auditRecords.value.map(record => record.date))]
	dates.sort((a, b) => b.localeCompare(a))
	dates.forEach(date => {
		options.push({ text: date, value: date })
	})
	return options
})

// 筛选后的记录列表
const filteredRecords = computed(() => {
	let filtered = auditRecords.value

	// 根据楼房筛选
	if (selectedBuilding.value !== '全部楼房') {
		const selectedBuildingId = buildingOptions.value.find(option => option.text === selectedBuilding.value)?.value
		if (selectedBuildingId !== 'all') {
			filtered = filtered.filter(record => record.building_id === selectedBuildingId)
		}
	}

	// 根据设备筛选
	if (selectedDevice.value !== '全部设备') {
		const deviceType = deviceOptions.find(option => option.text === selectedDevice.value)?.value
		if (deviceType !== 'all') {
			filtered = filtered.filter(record => record.type === deviceType)
		}
	}

	// 根据日期筛选
	if (selectedDate.value !== '全部月份') {
		const dateValue = dateOptions.value.find(option => option.text === selectedDate.value)?.value
		if (dateValue !== 'all') {
			filtered = filtered.filter(record => record.date === dateValue)
		}
	}

	return filtered
})

// 统计信息
const roomCount = computed(() => {
	return [...new Set(filteredRecords.value.map(record => record.room_id))].length
})

const buildingCount = computed(() => {
	return [...new Set(filteredRecords.value.map(record => record.building_id))].length
})

// 多选相关计算属性
const isAllSelected = computed(() => {
	return filteredRecords.value.length > 0 && selectedRecords.value.length === filteredRecords.value.length
})

// 判断记录是否被选中
const isRecordSelected = (recordId) => {
	return selectedRecords.value.includes(recordId)
}

// 切换全选状态
const toggleSelectAll = () => {
	if (isAllSelected.value) {
		selectedRecords.value = []
	} else {
		selectedRecords.value = filteredRecords.value.map(record => record._id)
	}
}

// 切换单个记录选择状态
const toggleRecordSelection = (record) => {
	const index = selectedRecords.value.indexOf(record._id)
	if (index > -1) {
		selectedRecords.value.splice(index, 1)
	} else {
		selectedRecords.value.push(record._id)
	}
}

// 清空选择
const clearSelection = () => {
	selectedRecords.value = []
}

// 页面加载
onLoad((e) => {
	console.log("onLoad", e)
	if (e.uid) {
		userId.value = e.uid
		initData()
	} else {
		uni.showToast({
			title: '缺少用户ID参数',
			icon: 'none'
		})
	}
})

// 初始化数据
const initData = async () => {
	uni.showLoading({
		title: '加载中...'
	})
	try {
		await getBuildingList()
		await getAuditRecords()
	} catch (error) {
		console.error('初始化数据失败:', error)
		uni.showToast({
			title: '加载数据失败',
			icon: 'none'
		})
	} finally {
		uni.hideLoading()
	}
}

// 获取用户的楼房列表
const getBuildingList = async () => {
	try {
		const res = await db.collection("fangke_building")
			.where(`uid=="${userId.value}"`)
			.field("_id,name,number")
			.get()

		console.log('获取楼房列表', res)
		buildingList.value = res.data
	} catch (error) {
		console.error('获取楼房列表失败:', error)
	}
}

// 获取待审核的水电记录
const getAuditRecords = async () => {
	try {
		if (buildingList.value.length === 0) {
			auditRecords.value = []
			return
		}

		const buildingIds = buildingList.value.map(building => building._id)

		const res = await db.collection("fangke_utility_records")
			.where(`building_id in ["${buildingIds.join('","')}"] && is_submit == false && is_update == true`)
			.orderBy('record_date', 'desc')
			.get()

		console.log('获取待审核水电记录', res)

		// 补充楼房名称
		auditRecords.value = res.data.map(record => {
			const building = buildingList.value.find(b => b._id === record.building_id)
			return {
				...record,
				building_name: building?.number || building?.name || '未知楼房'
			}
		})
	} catch (error) {
		console.error('获取待审核记录失败:', error)
	}
}

// 获取设备名称
const getDeviceName = (type) => {
	const deviceMap = {
		1: '冷水表',
		2: '电表',
		3: '燃气表'
	}
	return deviceMap[type] || '未知设备'
}

// 格式化数字（分转换为元）
const formatNumber = (num) => {
	if (!num) return '0.00'
	return (num / 100).toFixed(2)
}

// 格式化用量
const formatUsage = (usage) => {
	if (!usage) return '0.00'
	return (usage / 100).toFixed(2)
}

// 格式化日期
const formatDate = (timestamp) => {
	if (!timestamp) return ''
	return dayjs(timestamp).format('MM-DD HH:mm')
}

// 显示楼房筛选
const showBuildingFilter = () => {
	filterTitle.value = '楼房筛选'
	currentFilterOptions.value = buildingOptions.value
	currentFilterType.value = 'building'
	selectedFilterIndex.value = buildingOptions.value.findIndex(option => option.text === selectedBuilding.value)
	filterPopup.value?.open()
}

// 显示设备筛选
const showDeviceFilter = () => {
	filterTitle.value = '设备筛选'
	currentFilterOptions.value = deviceOptions
	currentFilterType.value = 'device'
	selectedFilterIndex.value = deviceOptions.findIndex(option => option.text === selectedDevice.value)
	filterPopup.value?.open()
}

// 显示日期筛选
const showDateFilter = () => {
	filterTitle.value = '日期筛选'
	currentFilterOptions.value = dateOptions.value
	currentFilterType.value = 'date'
	selectedFilterIndex.value = dateOptions.value.findIndex(option => option.text === selectedDate.value)
	filterPopup.value?.open()
}

// 选择筛选选项
const selectFilterOption = (index) => {
	selectedFilterIndex.value = index
}

// 确认筛选
const confirmFilter = () => {
	const selectedOption = currentFilterOptions.value[selectedFilterIndex.value]

	switch (currentFilterType.value) {
		case 'building':
			selectedBuilding.value = selectedOption.text
			break
		case 'device':
			selectedDevice.value = selectedOption.text
			break
		case 'date':
			selectedDate.value = selectedOption.text
			break
	}

	// 筛选条件改变时清空选择
	selectedRecords.value = []

	closeFilterPopup()
}

// 关闭筛选弹窗
const closeFilterPopup = () => {
	filterPopup.value?.close()
}

// 生成账单
const submitAudit = async () => {
	if (selectedRecords.value.length === 0) {
		uni.showToast({
			title: '请先选择要生成账单的记录',
			icon: 'none'
		})
		return
	}

	uni.showModal({
		title: '处理水电记录',
		content: `确定要处理这 ${selectedRecords.value.length} 条水电记录吗？`,
		confirmText: '确定处理',
		cancelText: '取消',
		success: async (res) => {
			if (res.confirm) {
				await performSubmit()
			}
		}
	})
}

// 执行生成操作
const performSubmit = async () => {
	uni.showLoading({
		title: '生成中...'
	})

	try {
		// 获取选中的水电记录详细信息
		const selectedUtilityRecords = filteredRecords.value.filter(record =>
			selectedRecords.value.includes(record._id)
		)
		console.log("selectedUtilityRecords", selectedUtilityRecords);
		
		// 调用云函数处理水电账单生成
		const billObj = uniCloud.importObject('bill')
		const result = await billObj.processUtilityBills({
			uid: userId.value,
			utility_records: selectedUtilityRecords
		})
		console.log("result", result);
		uni.hideLoading()

		if (result.errCode === 0) {
			uni.showToast({
				title: '处理成功',
				icon: 'success'
			})

			// 计算处理统计
			const totalAddedFees = result.data.details.reduce((sum, detail) => sum + detail.added_fees, 0)
			const totalAddedAmount = result.data.details.reduce((sum, detail) => sum + detail.added_amount, 0)

			// 构建结果消息
			let contentMessage = `总计处理：${result.data.total_records} 条水电记录\n`

			// 显示处理结果详情
			setTimeout(() => {
				uni.showModal({
					title: '处理完成',
					content: contentMessage,
					showCancel: false,
					confirmText: '确定',
					success(res) {
						uni.reLaunch({
							url:'/pages/index/index'
						})
					}
				})
			}, 1500)

			// 清空选择并刷新数据
			selectedRecords.value = []
			setTimeout(() => {
				initData()
			}, 2000)
		} else {
			throw new Error(result.errMsg || '处理失败')
		}
	} catch (error) {
		uni.hideLoading()
		console.error('生成失败:', error)
		uni.showToast({
			title: error.message || '生成失败',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: white;

	.header-title {
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
	}

	.header-subtitle {
		font-size: 24rpx;
		opacity: 0.9;
	}
}

.stats-section {
	display: flex;
	background: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	margin: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

	.stat-item {
		flex: 1;
		text-align: center;

		.stat-number {
			display: block;
			font-size: 36rpx;
			font-weight: 600;
			color: #07c160;
			margin-bottom: 8rpx;
		}

		.stat-label {
			font-size: 24rpx;
			color: #666;
		}
	}
}

.filter-section {
	display: flex;
	background: #fff;
	padding: 20rpx 30rpx;
	gap: 40rpx;
	border-bottom: 1px solid #f0f0f0;

	.filter-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
		flex: 1;
	}

	.filter-text {
		font-size: 28rpx;
		color: #333;
	}
}

.selection-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;

	.selection-left {
		display: flex;
		align-items: center;
		gap: 30rpx;

		.select-all {
			display: flex;
			align-items: center;
			gap: 12rpx;

			.select-text {
				font-size: 28rpx;
				color: #333;
			}
		}

		.selected-count {
			font-size: 24rpx;
			color: #666;
		}
	}

	.selection-right {
		.clear-selection {
			font-size: 26rpx;
			color: #07c160;
			padding: 8rpx 16rpx;
		}
	}
}

.table-header {
	display: flex;
	background: #f8f8f8;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #e0e0e0;

	.header-cell {
		font-size: 26rpx;
		color: #666;
		font-weight: 500;
	}



	.building-name {
		width: 120rpx;
	}

	.room-name {
		width: 120rpx;
	}

	.device-type {
		width: 100rpx;
	}

	.usage-amount {
		width: 100rpx;
	}

	.record-time {
		flex: 1;
		text-align: center;
	}
}

.record-list {
	.record-item {
		display: flex;
		background: #fff;
		margin-bottom: 2rpx;
		padding: 20rpx 20rpx 20rpx 0rpx;
		border-left: 4px solid #07c160;
		transition: all 0.3s ease;

		&.selected {
			background: #f6ffed;
			border-left-color: #52c41a;
		}

		.checkbox-cell {
			display: flex;
			align-items: center;
			padding: 20rpx;
		}

		.record-index {
			display: flex;
			flex-direction: column;
		}

		.record-info {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			.checkbox-cell {
				width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0;
			}

			.building-name {
				width: 120rpx;
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}

			.room-name {
				width: 120rpx;
				font-size: 28rpx;
				color: #333;
			}

			.device-type {
				width: 100rpx;
				font-size: 28rpx;
				color: #666;
			}

			.usage-amount {
				width: 100rpx;
				font-size: 28rpx;
				color: #07c160;
				font-weight: 500;
			}

			.record-time {
				flex: 1;
				font-size: 24rpx;
				color: #999;
				text-align: center;
			}
		}

		.record-details {
			display: flex;
			flex-wrap: wrap;
			gap: 10rpx;

			.detail-item {
				font-size: 24rpx;
				color: #666;
				background: #f8f8f8;
				padding: 6rpx 12rpx;
				border-radius: 4rpx;
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;

	.empty-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
		opacity: 0.4;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

.bottom-actions {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	padding: 20rpx 30rpx;
	background: #fff;
	border-top: 1px solid #f0f0f0;
	box-sizing: border-box;

	.audit-btn {
		width: 100%;
		height: 80rpx;
		background: #07c160;
		color: #fff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 500;
		transition: all 0.3s ease;

		&.disabled {
			background: #c8c9cc;
			color: #fff;
			cursor: not-allowed;
		}
	}
}

/* 筛选弹窗样式 */
.filter-popup {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 60vh;
	overflow: hidden;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EBEDF0;
}

.cancel-btn {
	color: #666;
	font-size: 28rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.confirm-btn {
	color: #07c160;
	font-weight: 500;
	font-size: 28rpx;
}

.filter-options {
	padding: 20rpx 0;
	max-height: 400rpx;
	overflow-y: auto;
}

.filter-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #f0f0f0;
}

.filter-option:last-child {
	border-bottom: none;
}

.filter-option.active {
	background: #f0f9ff;
}

.option-text {
	font-size: 28rpx;
	color: #333;
}

.filter-option.active .option-text {
	color: #07c160;
}
</style>