<template>
	<view>
		<view class="fab-login-box">
			<view class="item" v-for="(item,index) in servicesList" :key="index"
				@click="item.path?toPage(item.path):login_before(item.id,false)">
				<image class="logo" :src="item.logo" mode="scaleToFill"></image>
				<text class="login-title">{{item.text}}</text>
			</view>
		</view>
	</view>
</template>
<script setup>
	import {
		ref,
		computed,
		watch,
		onMounted,
		nextTick,
		getCurrentInstance,
		defineEmits
	} from 'vue'
	import config from '@/unid/uni-id-pages/config.js'
	import {
		mutations
	} from '@/utils/js/store.js'
	import { wxlogin } from '@/utils/js/common.js'
	const emit = defineEmits(['onagree'])
	// 响应式数据
	const servicesList = ref([{
			id: 'username',
			text: '账号登录',
			logo: '@/unid/uni-id-pages/static/login/uni-fab-login/user.png',
			path: '/unid/uni-id-pages/pages/login/login-withpwd'
		},
		{
			id: 'smsCode',
			text: '短信验证码',
			logo: '/unid/uni-id-pages/static/login/uni-fab-login/sms.png',
			path: '/unid/uni-id-pages/pages/login/login-withoutpwd?type=smsCode'
		},
		{
			id: 'weixin',
			text: '微信登录',
			logo: '/unid/uni-id-pages/static/login/uni-fab-login/weixin.png'
		},
		// #ifndef MP-WEIXIN
		{
			id: 'apple',
			text: '苹果登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/apple.png'
		},
		{
			id: 'univerify',
			text: '一键登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/univerify.png'
		},
		{
			id: 'taobao',
			text: '淘宝登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/taobao.png'
		},
		{
			id: 'facebook',
			text: '脸书登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/facebook.png'
		},
		{
			id: 'alipay',
			text: '支付宝登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/alipay.png'
		},
		{
			id: 'qq',
			text: 'QQ登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/qq.png'
		},
		{
			id: 'google',
			text: '谷歌登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/google.png'
		},
		{
			id: 'douyin',
			text: '抖音登录',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/douyin.png'
		},
		{
			id: 'sinaweibo',
			text: '新浪微博',
			logo: '/unid/uni-id-pages/static/app-plus/uni-fab-login/sinaweibo.png'
		},
		// #endif
	])

	const univerifyStyle = ref({
		fullScreen: true,
		backgroundColor: '#ffffff',
		buttons: {
			iconWidth: '45px',
			list: []
		},
		privacyTerms: {
			defaultCheckBoxState: false,
			textColor: '#BBBBBB',
			termsColor: '#5496E3',
			prefix: '我已阅读并同意',
			suffix: '并使用本机号码登录',
			privacyItems: []
		}
	})

	const agree = computed({
		get() {
			const parent = getParentComponent()
			return parent ? parent.agree : false
		},
		set(value) {
			const parent = getParentComponent()
			if (parent) parent.agree = value
		}
	})

	// 计算属性
	const agreements = computed(() => {
		if (!config.agreements) return []
		const {
			serviceUrl,
			privacyUrl
		} = config.agreements
		return [{
				url: serviceUrl,
				title: '用户服务协议'
			},
			{
				url: privacyUrl,
				title: '隐私政策条款'
			}
		]
	})

	// 生命周期
	onMounted(async () => {
		let list = servicesList.value
		const loginTypes = config.loginTypes

		// 过滤登录方式
		list = list.filter(item => {
			// #ifndef APP
			if (item.id === 'apple') return false
			// #endif

			// #ifdef APP
			if (item.id === 'apple' && uni.getSystemInfoSync().osName !== 'ios') return false
			// #endif

			return loginTypes.includes(item.id)
		})

		// 处理一键登录配置
		if (loginTypes.includes('univerify')) {
			univerifyStyle.value.privacyTerms.privacyItems = agreements.value
			list.forEach(({
				id,
				logo,
				path
			}) => {
				if (id !== 'univerify') {
					univerifyStyle.value.buttons.list.push({
						iconPath: logo,
						provider: id,
						path
					})
				}
			})
		}

		// 过滤当前页面登录选项
		servicesList.value = list.filter(item => {
			const path = item.path ? item.path.split('?')[0] : ''
			return path !== getRoute(1)
		})
	})

	// 依赖注入获取父组件
	function getParentComponent() {
		const {
			proxy
		} = getCurrentInstance()
		// #ifndef H5
		return proxy.$parent
		// #endif

		// #ifdef H5
		return proxy.$parent.$parent
		// #endif
	}

	// 方法定义
	function getRoute(n = 0) {
		const pages = getCurrentPages()
		if (n >= pages.length) return ''
		return `/${pages[pages.length - n - 1].route}` // Vue3 中 pages 结构可能不同，需确认索引逻辑
	}

	function toPage(path, index = 0) {
		const currentPath = getRoute(1)?.split('?')[0]
		const targetPath = path.split('?')[0]

		if (currentPath === targetPath && currentPath === '/unid/uni-id-pages/pages/login/login-withoutpwd') {
			const loginType = path.split('?')[1].split('=')[1]
			uni.$emit('uni-id-pages-setLoginType', loginType)
		} else if (getRoute(2) === path) {
			uni.navigateBack()
		} else if (currentPath !== targetPath) {
			const navigate = index === 0 ? uni.navigateTo : uni.redirectTo
			navigate({
				url: path,
				animationType: 'slide-in-left',
				complete(e) {
					/* 处理完成回调 */ }
			})
		}
	}

	async function login_before(type, navigateBack = true, options = {}) {
		console.log("login_before",type);
		// 提示空实现逻辑
		if (['qq', 'xiaomi', 'sinaweibo', 'taobao', 'facebook', 'google', 'alipay', 'douyin'].includes(type)) {
			return uni.showToast({
				title: '该登录方式暂未实现，欢迎提交pr',
				icon: 'none',
				duration: 3000
			})
		}

		// #ifdef APP
		let isAppExist = true
		if (['apple', 'univerify','username'].includes(type)) {
			await new Promise((resolve, reject) => {
				plus.oauth.getServices(services => {
					const service = services.find(s => s.id === type)
					isAppExist = service?.nativeClient || false
					resolve()
				}, reject)
			})
		}
		// #endif

		// 环境支持检查
		// if (
		// 	// #ifdef APP
		// 	!isAppExist ||
		// 	// #endif

		// 	// #ifndef APP
		// 	['univerify', 'apple'].includes(type)
		// 	// #endif
		// ) {
		// 	return uni.showToast({
		// 		title: '当前设备不支持此登录，请选择其他登录方式'+type,
		// 		icon: 'none',
		// 		duration: 3000
		// 	})
		// }

		// 隐私协议检查
		// const needAgreements = (config?.agreements?.scope || []).includes('register')
		// if (type !== 'univerify' && needAgreement && !agree) {
		// 	// console.log('needAgreements',needAgreements);
		// 	// console.log("agree",agree);
		// 	// return emit('onagree',needAgreements)
		// 	// const agreementsRef = getParentComponent()?.agreements.value
		// 	// if (agreementsRef) {
		// 	// 	return agreementsRef.popup(() => login_before(type, navigateBack, options))
		// 	// }
		// }

		// #ifdef H5
		if (type === 'weixin') {
			const baseUrl = import.meta.env.BASE_URL.replace(/\/$/, '')
			const redirectUrl =
				`${location.protocol}//${location.host}${baseUrl}${window.location.href.includes('#') ? '/#' : ''}/unid/uni-id-pages/pages/login/login-withoutpwd?is_weixin_redirect=true&type=weixin`
			const ua = window.navigator.userAgent.toLowerCase()

			if (ua.includes('micromessenger')) {
				window.open(
					`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.appid.weixin.h5}&redirect_uri=${encodeURIComponent(redirectUrl)}&response_type=code&scope=snsapi_userinfo&state=STATE&connect_redirect=1#wechat_redirect`
					)
			} else {
				location.href =
					`https://open.weixin.qq.com/connect/qrconnect?appid=${config.appid.weixin.web}&redirect_uri=${encodeURIComponent(redirectUrl)}&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect`
			}
			return
		}
		// #endif

		uni.showLoading({
			mask: true
		})

		if (type === 'univerify') {
			const univerifyManager = uni.getUniverifyManager()
			let clickAnotherButtons = false
			const onButtonsClickFn = async (res) => {
				clickAnotherButtons = true
				// #ifdef VUE3
				const checkBoxState = await uni.getCheckBoxState()
				agree.value = checkBoxState.state
				// #endif

				const {
					path
				} = univerifyStyle.value.buttons.list[res.index]
				if (path) {
					if (getRoute(1).includes('login-withoutpwd') && path.includes('login-withoutpwd')) {
						getParentComponent()?.showCurrentWebview()
					}
					toPage(path, 1)
					closeUniverify()
				} else {
					if (agree.value) {
						closeUniverify()
						setTimeout(() => login_before(res.provider), 500)
					} else {
						uni.showToast({
							title: '你未同意隐私政策协议',
							icon: 'none',
							duration: 3000
						})
					}
				}
			}

			function closeUniverify() {
				uni.hideLoading()
				univerifyManager.close()
				univerifyManager.offButtonsClick(onButtonsClickFn)
			}

			univerifyManager.onButtonsClick(onButtonsClickFn)
			return univerifyManager.login({
				univerifyStyle: univerifyStyle.value,
				success: (res) => login(res.authResult, 'univerify'),
				fail: (err) => {
					console.log(err)
					if (!clickAnotherButtons) uni.navigateBack()
				},
				complete: async () => {
					uni.hideLoading()
					// #ifdef VUE3
					// agree.value = (await uni.getCheckBoxState()).state
					// #endif
					univerifyManager.offButtonsClick(onButtonsClickFn)
				}
			})
		}

		if (type === 'weixinMobile') {
			return wxlogin(options.phoneNumberCode).then(res => {
				console.log("登录成功");
			}).catch(err => {
				console.log("登录失败", err);
			})
			// return login({
			// 	phoneCode: options.phoneNumberCode
			// }, type)
		}

		return new Promise((resolve) => {
			uni.login({
				provider: type,
				onlyAuthorize: true,
				// #ifdef APP
				univerifyStyle: univerifyStyle.value,
				// #endif
				success: async (e) => {
					if (type === 'apple') {
						const userInfo = await getUserInfo({
							provider: 'apple'
						})
						Object.assign(e.authResult, userInfo.userInfo)
					}
					login(type === 'weixin' ? {
						code: e.code
					} : e.authResult, type)
					resolve()
				},
				fail: (err) => {
					console.log(err)
					uni.hideLoading()
					resolve()
				}
			})
		})
	}

	function login(params, type) {
		const action = `loginBy${type.charAt(0).toUpperCase() + type.slice(1)}`
		const uniIdCo = uniCloud.importObject('uni-id-co', {
			customUI: true
		})

		uniIdCo[action](params)
			.then((result) => {
				uni.showToast({
					title: '登录成功',
					icon: 'none',
					duration: 2000
				})
				// #ifdef H5
				result.loginType = type
				// #endif
				mutations.loginSuccess(result)
			})
			.catch((e) => {
				uni.showModal({
					content: e.message,
					confirmText: '知道了',
					showCancel: false
				})
			})
			.finally(() => {
				if (type === 'univerify') uni.closeAuthView()
				uni.hideLoading()
			})
	}

	function getUserInfo(e) {
		return new Promise((resolve, reject) => {
			uni.getUserInfo({
				...e,
				success: (res) => resolve(res),
				fail: (err) => {
					uni.showModal({
						content: JSON.stringify(err),
						showCancel: false
					})
					reject(err)
				}
			})
		})
	}

	// 监视 agree 变化
	watch(agree, (newVal) => {
		univerifyStyle.value.privacyTerms.defaultCheckBoxState = newVal
	})

	// 暴露方法供外部调用
	defineExpose({
		login_before
	})
</script>

<style lang="scss">
	/* #ifndef APP-NVUE */
	.fab-login-box,
	.item {
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}

	/* #endif */

	.fab-login-box {
		flex-direction: row;
		flex-wrap: wrap;
		width: 750rpx;
		justify-content: space-around;
		position: fixed;
		left: 0;
	}

	.item {
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		cursor: pointer;
	}

	/* #ifndef APP-NVUE */
	@media screen and (min-width: 690px) {
		.fab-login-box {
			max-width: 500px;
			margin-left: calc(50% - 250px);
		}

		.item {
			height: 160rpx;
		}
	}

	@media screen and (max-width: 690px) {
		.fab-login-box {
			bottom: 10rpx;
		}
	}

	/* #endif */

	.logo {
		width: 60rpx;
		height: 60rpx;
		max-width: 40px;
		max-height: 40px;
		border-radius: 100%;
		border: solid 1px #F6F6F6;
	}

	.login-title {
		text-align: center;
		margin-top: 6px;
		color: #999;
		font-size: 10px;
		width: 70px;
	}
</style>