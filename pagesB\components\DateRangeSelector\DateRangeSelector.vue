<template>
	<uni-popup ref="timePopup" type="bottom">
		<view class="popup-content">
			<view class="popup-header">
				<text class="popup-title">选择时间范围</text>
				<view class="popup-close" @click="closePopup">
					<uni-icons type="close" size="20" color="#666"></uni-icons>
				</view>
			</view>

			<!-- 快捷时间标签 -->
			<view class="time-tags">
				<scroll-view scroll-x="true" class="time-tags-scroll">
					<view class="time-tags-list">
						<view v-for="(tag, index) in timeTags" :key="index" class="time-tag"
							:class="{ active: selectedTimeTag === index }" @click="selectTimeTag(index)">
							{{ tag.name }}
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 时间选择器 -->
			<view class="time-picker-section">
				<!-- 日期显示区域 -->
				<view class="date-display-area">
					<view class="date-display-item" :class="{ active: isSelectingStartDate }"
						@click="selectStartDateMode">
						<text class="date-display-text">{{ selectedStartDate || '开始日期' }}</text>
					</view>
					<view class="date-separator">
						<text class="date-label">至</text>
					</view>
					<view class="date-display-item" :class="{ active: !isSelectingStartDate }"
						@click="selectEndDateMode">
						<text class="date-display-text">{{ selectedEndDate || '结束日期' }}</text>
					</view>
				</view>

				<!-- 单个 Picker View -->
				<view class="picker-view-wrapper">
					<view class="picker-section">
						<picker-view class="date-picker-view" :value="currentPickerValue" @change="onPickerChange"
							:indicator-style="indicatorStyle">
							<picker-view-column>
								<view v-for="(year, index) in years" :key="index" class="picker-item">
									{{ year }}年
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="(month, index) in months" :key="index" class="picker-item">
									{{ String(month).padStart(2, '0') }}月
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="(day, index) in currentDays" :key="index" class="picker-item">
									{{ String(day).padStart(2, '0') }}日
								</view>
							</picker-view-column>
						</picker-view>
					</view>
				</view>
			</view>

			<view class="popup-buttons">
				<button class="btn-cancel" @click="closePopup">取消</button>
				<button class="btn-confirm" @click="confirmSelection">确定</button>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		watch,
		nextTick
	} from 'vue'
	import {
		dayjs
	} from '../../../utils/dayjs.min.js'

	// 定义 props
	const props = defineProps({
		// 初始开始日期
		startDate: {
			type: String,
			default: ''
		},
		// 初始结束日期
		endDate: {
			type: String,
			default: ''
		},
		// 自定义时间标签
		customTimeTags: {
			type: Array,
			default: () => []
		}
	})

	// 定义 emits
	const emit = defineEmits(['confirm', 'cancel'])

	// 内部状态
	const startDate = ref(props.startDate)
	const endDate = ref(props.endDate)
	const selectedTimeTag = ref(-1)

	// 默认时间标签
	const defaultTimeTags = [{
			name: '未来7天',
			days: 7,
			type: 'future'
		},
		{
			name: '最近1周',
			days: 7,
			type: 'past'
		},
		{
			name: '最近1月',
			days: 30,
			type: 'past'
		},
		{
			name: '最近3个月',
			days: 90,
			type: 'past'
		}
	]

	// 时间标签（支持自定义）
	const timeTags = computed(() => {
		return props.customTimeTags.length > 0 ? props.customTimeTags : defaultTimeTags
	})

	// picker-view 相关数据
	const currentYear = new Date().getFullYear()
	const years = ref([])
	const months = ref([])
	const startDays = ref([])
	const endDays = ref([])

	// picker-view 选中值
	const startPickerValue = ref([0, 0, 0])
	const endPickerValue = ref([0, 0, 0])

	// 选中的日期显示
	const selectedStartDate = ref('')
	const selectedEndDate = ref('')

	// 当前选择模式 (true: 选择开始时间, false: 选择结束时间)
	const isSelectingStartDate = ref(true)

	// 当前picker-view的值和天数数组
	const currentPickerValue = computed(() => {
		return isSelectingStartDate.value ? startPickerValue.value : endPickerValue.value
	})

	const currentDays = computed(() => {
		return isSelectingStartDate.value ? startDays.value : endDays.value
	})

	// 弹窗引用
	const timePopup = ref(null)

	// picker-view 指示器样式
	const indicatorStyle = ref('height: 60rpx; border-top: 1rpx solid #e5e5e5; border-bottom: 1rpx solid #e5e5e5;')

	// 方法
	const open = () => {
		timePopup.value?.open()
	}

	const closePopup = () => {
		timePopup.value?.close()
		emit('cancel')
	}

	// 选择开始日期模式
	const selectStartDateMode = () => {
		isSelectingStartDate.value = true
	}

	// 选择结束日期模式
	const selectEndDateMode = () => {
		isSelectingStartDate.value = false

		// 如果结束时间还没有设置，将结束时间的picker值设置为开始时间的值
		if (!endDate.value && startDate.value) {
			endPickerValue.value = [...startPickerValue.value]
			updateDays('end')
		}
	}

	// 处理picker-view变化
	const onPickerChange = (e) => {
		if (isSelectingStartDate.value) {
			startPickerValue.value = e.detail.value
			updateDays('start')
			updateSelectedDates()
		} else {
			endPickerValue.value = e.detail.value
			updateDays('end')
			updateSelectedDates()
		}
	}

	// 选择时间标签
	const selectTimeTag = (index) => {
		selectedTimeTag.value = index
		const tag = timeTags.value[index]
		const now = dayjs()

		let startDateObj, endDateObj

		if (tag.type === 'future') {
			startDateObj = now
			endDateObj = now.add(tag.days, 'day')
		} else {
			startDateObj = now.subtract(tag.days, 'day')
			endDateObj = now
		}

		// 更新日期字符串
		startDate.value = startDateObj.format('YYYY-MM-DD')
		endDate.value = endDateObj.format('YYYY-MM-DD')

		// 更新picker-view的值
		const startYear = startDateObj.year()
		const startMonth = startDateObj.month() + 1
		const startDay = startDateObj.date()

		const endYear = endDateObj.year()
		const endMonth = endDateObj.month() + 1
		const endDay = endDateObj.date()

		// 找到对应的索引
		const startYearIndex = years.value.findIndex(year => year === startYear)
		const startMonthIndex = months.value.findIndex(month => month === startMonth)
		const endYearIndex = years.value.findIndex(year => year === endYear)
		const endMonthIndex = months.value.findIndex(month => month === endMonth)

		if (startYearIndex >= 0 && startMonthIndex >= 0) {
			startPickerValue.value = [startYearIndex, startMonthIndex, startDay - 1]
		}

		if (endYearIndex >= 0 && endMonthIndex >= 0) {
			endPickerValue.value = [endYearIndex, endMonthIndex, endDay - 1]
		}

		// 如果当前正在选择开始时间，确保picker显示开始时间
		// 如果当前正在选择结束时间，确保picker显示结束时间
		// 这样可以保证picker-view与当前选择模式同步

		// 更新天数数组和显示文本
		updateDays('both')
		updateSelectedDates()
	}

	// 确认选择
	const confirmSelection = () => {
		// 校验开始时间和结束时间是否为空
		if (!startDate.value) {
			uni.showToast({
				title: '请选择开始时间',
				icon: 'none'
			})
			return
		}

		if (!endDate.value) {
			uni.showToast({
				title: '请选择结束时间',
				icon: 'none'
			})
			return
		}

		// 校验开始时间是否小于结束时间
		const start = dayjs(startDate.value)
		const end = dayjs(endDate.value)

		if (start.isAfter(end) || start.isSame(end)) {
			uni.showToast({
				title: '开始时间必须小于结束时间',
				icon: 'none'
			})
			return
		}

		// 触发确认事件
		emit('confirm', {
			startDate: startDate.value,
			endDate: endDate.value,
			selectedTimeTag: selectedTimeTag.value
		})

		closePopup()
	}

	// 初始化年月日数据
	const initPickerData = () => {
		// 生成年份数组 (当前年份前后5年)
		for (let i = currentYear - 10; i <= currentYear + 5; i++) {
			years.value.push(i)
		}

		// 生成月份数组
		for (let i = 1; i <= 12; i++) {
			months.value.push(i)
		}

		// 确定初始日期：优先使用开始时间，其次使用当前时间
		let initialDate
		if (props.startDate) {
			initialDate = dayjs(props.startDate)
		} else {
			initialDate = dayjs()
		}

		const initialYearIndex = years.value.findIndex(year => year === initialDate.year())
		const initialMonthIndex = initialDate.month()
		const initialDay = initialDate.date()
		console.log("初始化选择的日期",initialYearIndex,initialMonthIndex,initialDay);
		// 设置默认选中值 - picker-view 初始化时显示开始时间
		startPickerValue.value = [initialYearIndex, initialMonthIndex, initialDay - 1]
		endPickerValue.value = [initialYearIndex, initialMonthIndex, initialDay - 1]
		currentPickerValue.value = startPickerValue.value
		// 如果有初始值，设置对应的picker值
		if (props.startDate) {
			const startDateObj = dayjs(props.startDate)
			const startYearIndex = years.value.findIndex(year => year === startDateObj.year())
			const startMonthIndex = startDateObj.month()
			if (startYearIndex >= 0) {
				startPickerValue.value = [startYearIndex, startMonthIndex, startDateObj.date() - 1]
			}
		}

		if (props.endDate) {
			const endDateObj = dayjs(props.endDate)
			const endYearIndex = years.value.findIndex(year => year === endDateObj.year())
			const endMonthIndex = endDateObj.month()
			if (endYearIndex >= 0) {
				endPickerValue.value = [endYearIndex, endMonthIndex, endDateObj.date() - 1]
			}
		} else {
			// 如果没有结束时间，结束时间的picker也初始化为开始时间
			endPickerValue.value = [...startPickerValue.value]
		}

		// 初始化天数数组
		updateDays('both')
		updateSelectedDates()
	}

	// 更新天数数组
	const updateDays = (type = 'both') => {
		if (type === 'start' || type === 'both') {
			const startYear = years.value[startPickerValue.value[0]]
			const startMonth = months.value[startPickerValue.value[1]]

			const startDaysInMonth = new Date(startYear, startMonth, 0).getDate()

			startDays.value = []
			for (let i = 1; i <= startDaysInMonth; i++) {
				startDays.value.push(i)
			}
		}

		if (type === 'end' || type === 'both') {
			const endYear = years.value[endPickerValue.value[0]]
			const endMonth = months.value[endPickerValue.value[1]]

			const endDaysInMonth = new Date(endYear, endMonth, 0).getDate()

			endDays.value = []
			for (let i = 1; i <= endDaysInMonth; i++) {
				endDays.value.push(i)
			}
		}
	}

	// 更新选中的日期字符串
	const updateSelectedDates = () => {
		const startYear = years.value[startPickerValue.value[0]]
		const startMonth = months.value[startPickerValue.value[1]]
		const startDay = startDays.value[startPickerValue.value[2]]

		const endYear = years.value[endPickerValue.value[0]]
		const endMonth = months.value[endPickerValue.value[1]]
		const endDay = endDays.value[endPickerValue.value[2]]

		if (startYear && startMonth && startDay) {
			selectedStartDate.value =
				`${startYear}年-${String(startMonth).padStart(2, '0')}月-${String(startDay).padStart(2, '0')}日`
			startDate.value =
				`${startYear}-${String(startMonth).padStart(2, '0')}-${String(startDay).padStart(2, '0')}`
		}

		if (endYear && endMonth && endDay) {
			selectedEndDate.value =
				`${endYear}年-${String(endMonth).padStart(2, '0')}月-${String(endDay).padStart(2, '0')}日`
			endDate.value = `${endYear}-${String(endMonth).padStart(2, '0')}-${String(endDay).padStart(2, '0')}`
		}
	}

	// 监听 props 变化
	watch(() => props.startDate, (newVal) => {
		startDate.value = newVal
	})

	watch(() => props.endDate, (newVal) => {
		endDate.value = newVal
	})

	// 暴露方法给父组件
	defineExpose({
		open,
		close: closePopup
	})

	onMounted(() => {
		initPickerData()
	})
</script>

<style lang="scss" scoped>
	.popup-content {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding: 40rpx;
		max-height: 80vh;

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}
		}

		.time-tags {
			margin-bottom: 40rpx;

			.time-tags-scroll {
				white-space: nowrap;
			}

			.time-tags-list {
				display: flex;
				padding: 0 30rpx;

				.time-tag {
					flex-shrink: 0;
					padding: 16rpx 32rpx;
					margin-right: 20rpx;
					background-color: #f5f6f7;
					border-radius: 40rpx;
					font-size: 28rpx;
					color: #666;

					&.active {
						background-color: #01B862;
						color: #fff;
					}

					&:last-child {
						margin-right: 30rpx;
					}
				}
			}
		}

		.time-picker-section {
			margin-bottom: 40rpx;

			.date-display-area {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 40rpx;
				padding: 0 20rpx;

				.date-display-item {
					display: flex;
					align-items: center;
					padding: 20rpx;
					border-radius: 12rpx;
					cursor: pointer;
					transition: all 0.3s ease;

					&.active {
						background-color: #f0f9ff;

						.date-display-text {
							color: #01B862;
							border-bottom-color: #01B862;
						}
					}

					.date-display-text {
						font-size: 32rpx;
						font-weight: 500;
						color: #333;
						padding-bottom: 10rpx;
						border-bottom: 2rpx solid transparent;
						transition: all 0.3s ease;
					}
				}

				.date-separator {
					.date-label {
						font-size: 28rpx;
						color: #999;
					}
				}
			}

			.picker-view-wrapper {
				display: flex;
				height: 300rpx;
				justify-content: center;

				.picker-section {
					width: 100%;
					position: relative;

					.date-picker-view {
						height: 100%;
						background-color: transparent;

						.picker-item {
							display: flex;
							align-items: center;
							justify-content: center;
							height: 60rpx;
							font-size: 28rpx;
							color: #999;
							transition: all 0.3s ease;

							&.selected {
								color: #333;
								font-weight: 600;
								font-size: 32rpx;
							}
						}
					}

					&::before,
					&::after {
						content: '';
						position: absolute;
						left: 0;
						right: 0;
						height: 1rpx;
						background-color: #e5e5e5;
						z-index: 10;
						pointer-events: none;
					}

					&::before {
						top: 120rpx;
					}

					&::after {
						bottom: 120rpx;
					}
				}
			}
		}

		.popup-buttons {
			display: flex;
			gap: 20rpx;

			button {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 40rpx;
				font-size: 28rpx;
				border: none;

				&.btn-cancel {
					background-color: #f5f6f7;
					color: #666;
				}

				&.btn-confirm {
					background-color: #01B862;
					color: #fff;
				}
			}
		}
	}
</style> 