{"description": "房型模板", "bsonType": "object", "required": [], "permission": {"read": "auth.uid != null", "create": "auth.uid != null", "update": "auth.uid != null", "delete": "auth.uid != null"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "uid": {"bsonType": "string", "description": "创建模板的用户id", "foreignKey": "uni-id-users._id", "forceDefaultValue": {"$env": "uid"}}, "create_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}, "name": {"bsonType": "string", "description": "房型名字", "label": "房型名字"}, "area": {"bsonType": "int", "label": "面积"}, "payment": {"bsonType": "string", "label": "付租方式"}, "rent": {"bsonType": "int", "label": "租金"}, "layout": {"bsonType": "string", "label": "实际房型"}, "desc": {"bsonType": "string", "label": "备注"}, "devices": {"bsonType": "array", "description": "房间配置", "label": "房间配置", "arrayType": "int"}, "images": {"bsonType": "array", "description": "房间照片URL数组", "label": "房间照片", "arrayType": "string"}}}