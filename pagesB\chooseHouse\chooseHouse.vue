<template>
	<view class="page">
		<!-- 加载状态 -->
		

		<!-- 房源列表 -->
		<view v-if="houseList.length > 0" class="house-list">
			<view 
				class="house-item" 
				v-for="(house, index) in houseList" 
				:key="house.id"
				@click="selectHouse(house)"
			>
				<view class="house-name">{{ house.name }}</view>
				<view class="arrow-icon">
					<uni-icons type="right" size="16" color="#c0c4cc" />
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-else class="empty-container">
			<view class="empty-icon">
				<uni-icons type="home" size="80" color="#d9d9d9" />
			</view>
			<text class="empty-title">暂无房源</text>
			<text class="empty-desc">请先添加房源信息</text>
			<button class="add-house-btn" @click="goToAddHouse">
				添加房源
			</button>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
	import { ref, onMounted,getCurrentInstance ,computed} from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import {
		store2
	} from '@/utils/js/store.js'
	const isLogin = computed(() => store2.hasLogin)
	const userInfo = computed(() => store2.userInfo)
	const db = uniCloud.databaseForJQL()
	
	// 数据状态
	const houseList = ref([])
	const buildingList = ref([])
	// 页面参数
	const pageParams = ref({})
	
	// 页面加载
	onLoad((options) => {
		pageParams.value = options || {}
		console.log('页面参数:', pageParams.value)
	
	})
	
	onMounted(() => {
		// 页面挂载后的初始化
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromIndex', function(data) {
				console.log('acceptDataFromIndex', data)
				buildingList.value = data
				getHouseList()
			})
	})
	
	// 获取房源列表
	const getHouseList = async () => {
		houseList.value = buildingList.value
	}
	
	// 选择房源
	const selectHouse = (house) => {
		console.log('选择房源:', house)
		
		// 根据页面参数决定跳转目标
		uni.navigateTo({
			url: "/pagesB/shuidianList/shuidianList?id="+house.id+"&name="+house.name
		})
	}
	
	// 跳转到添加房源页面
	const goToAddHouse = () => {
		if(isLogin.value){
			uni.navigateTo({
				url: '/pagesB/addHouse/addHouse'
			})
		}else{
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
		}
		
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-top: 20rpx;
	}
	
	/* 加载状态 */
	.loading-container {
		padding: 100rpx 0;
		text-align: center;
	}
	
	/* 房源列表 */
	.house-list {
		padding: 0 30rpx;
	}
	
	.house-item {
		background: #fff;
		border-radius: 16rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.2s;
	}
	
	.house-item:active {
		transform: scale(0.98);
	}
	
	.house-name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		flex: 1;
	}
	
	.arrow-icon {
		margin-left: 20rpx;
	}
	
	/* 空状态 */
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 60rpx;
		text-align: center;
	}
	
	.empty-icon {
		margin-bottom: 40rpx;
		opacity: 0.6;
	}
	
	.empty-title {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 16rpx;
		font-weight: 500;
	}
	
	.empty-desc {
		font-size: 26rpx;
		color: #999;
		line-height: 1.5;
		margin-bottom: 60rpx;
	}
	
	.add-house-btn {
		background: #07c160;
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 24rpx 60rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	/* 底部安全区域 */
	.safe-area-bottom {
		height: 40rpx;
	}
</style>
