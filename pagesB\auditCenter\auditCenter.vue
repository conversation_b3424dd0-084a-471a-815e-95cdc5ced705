<template>
	<view class="audit-center">
		<!-- 顶部搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="16" color="#999" />
				<input
					v-model="searchKeyword"
					class="search-input"
					placeholder="搜索申请标题、申请人、房间号"
					@input="onSearchInput"
				/>
				<view v-if="searchKeyword" class="search-clear" @click="clearSearch">
					<uni-icons type="clear" size="14" color="#999" />
				</view>
			</view>
		</view>

		<!-- 头部统计 -->
		<view class="header-stats">
			<view class="stat-item">
				<text class="stat-number">{{ totalPending }}</text>
				<text class="stat-label">待审核</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ todayProcessed }}</text>
				<text class="stat-label">今日已处理</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ totalProcessed }}</text>
				<text class="stat-label">总计已处理</text>
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view
					v-for="(tab, index) in auditTabs"
					:key="index"
					class="filter-tab"
					:class="{ active: currentTab === index }"
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
					<view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
				</view>
			</view>

			<view class="filter-controls">
				<view class="filter-item" @click="showStatusFilter">
					<text class="filter-text">{{ selectedStatus }}</text>
					<uni-icons type="bottom" size="12" color="#666" />
				</view>
				<view class="filter-item" @click="showDateFilter">
					<text class="filter-text">{{ selectedDateRange }}</text>
					<uni-icons type="bottom" size="12" color="#666" />
				</view>
				<view class="filter-item" @click="showPriorityFilter">
					<text class="filter-text">{{ selectedPriority }}</text>
					<uni-icons type="bottom" size="12" color="#666" />
				</view>
			</view>
		</view>

		<!-- 审核列表 -->
		<view class="audit-list">
			<view
				v-for="(item, index) in filteredAuditList"
				:key="item._id"
				class="audit-item"
				@click="viewDetail(item)"
			>
				<view class="audit-header">
					<view class="audit-type">
						<uni-icons :type="getAuditIcon(item.type)" size="16" :color="getAuditColor(item.type)" />
						<text class="type-text">{{ getAuditTypeName(item.type) }}</text>
					</view>
					<view class="audit-status" :class="getStatusClass(item.status)">
						{{ getStatusText(item.status) }}
					</view>
				</view>

				<view class="audit-content">
					<text class="audit-title">{{ item.title }}</text>
					<text class="audit-desc">{{ item.description }}</text>
				</view>

				<view class="audit-info">
					<view class="info-item">
						<text class="info-label">申请人：</text>
						<text class="info-value">{{ item.applicant_name }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">房间：</text>
						<text class="info-value">{{ item.room_info }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">申请时间：</text>
						<text class="info-value">{{ formatDate(item.create_time) }}</text>
					</view>
				</view>

				<!-- 待审核状态显示操作按钮 -->
				<view v-if="item.status === 0" class="audit-actions">
					<button class="action-btn reject-btn" @click.stop="rejectAudit(item)">拒绝</button>
					<button class="action-btn approve-btn" @click.stop="approveAudit(item)">通过</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="filteredAuditList.length === 0" class="empty-state">
			<image class="empty-icon" src="/static/empty.png" mode="aspectFit" />
			<text class="empty-text">{{ getEmptyText() }}</text>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom" border-radius="20rpx 20rpx 0 0">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">{{ filterTitle }}</text>
					<view class="popup-close" @click="closeFilterPopup">
						<uni-icons type="close" size="18" color="#666" />
					</view>
				</view>
				<view class="popup-content">
					<view
						v-for="(option, index) in currentFilterOptions"
						:key="index"
						class="filter-option"
						:class="{ selected: selectedFilterIndex === index }"
						@click="selectFilterOption(index)"
					>
						<text class="option-text">{{ option.text }}</text>
						<uni-icons v-if="selectedFilterIndex === index" type="checkmarkempty" size="16" color="#01B862" />
					</view>
				</view>
				<view class="popup-actions">
					<button class="popup-btn cancel-btn" @click="closeFilterPopup">取消</button>
					<button class="popup-btn confirm-btn" @click="confirmFilter">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 审核详情弹窗 -->
		<uni-popup ref="detailPopup" type="center" border-radius="20rpx">
			<view class="detail-popup">
				<view class="detail-header">
					<text class="detail-title">审核详情</text>
					<view class="detail-close" @click="closeDetailPopup">
						<uni-icons type="close" size="18" color="#666" />
					</view>
				</view>
				<view class="detail-content">
					<view v-if="currentAuditItem" class="detail-info">
						<view class="detail-row">
							<text class="detail-label">申请类型：</text>
							<text class="detail-value">{{ getAuditTypeName(currentAuditItem.type) }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">申请标题：</text>
							<text class="detail-value">{{ currentAuditItem.title }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">申请描述：</text>
							<text class="detail-value">{{ currentAuditItem.description }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">申请人：</text>
							<text class="detail-value">{{ currentAuditItem.applicant_name }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">联系电话：</text>
							<text class="detail-value">{{ currentAuditItem.applicant_phone }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">房间信息：</text>
							<text class="detail-value">{{ currentAuditItem.room_info }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">申请时间：</text>
							<text class="detail-value">{{ formatDateTime(currentAuditItem.create_time) }}</text>
						</view>
						<view v-if="currentAuditItem.status !== 0" class="detail-row">
							<text class="detail-label">处理时间：</text>
							<text class="detail-value">{{ formatDateTime(currentAuditItem.audit_time) }}</text>
						</view>
						<view v-if="currentAuditItem.audit_remark" class="detail-row">
							<text class="detail-label">审核备注：</text>
							<text class="detail-value">{{ currentAuditItem.audit_remark }}</text>
						</view>

						<!-- 附件信息 -->
						<view v-if="currentAuditItem.attachments && currentAuditItem.attachments.length > 0" class="detail-row">
							<text class="detail-label">相关附件：</text>
							<view class="attachment-list">
								<view
									v-for="(attachment, index) in currentAuditItem.attachments"
									:key="index"
									class="attachment-item"
									@click="previewAttachment(attachment)"
								>
									<uni-icons type="image" size="16" color="#01B862" />
									<text class="attachment-name">{{ attachment.name || `附件${index + 1}` }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="currentAuditItem && currentAuditItem.status === 0" class="detail-actions">
					<button class="detail-btn reject-btn" @click="rejectAuditWithRemark">拒绝</button>
					<button class="detail-btn approve-btn" @click="approveAuditWithRemark">通过</button>
				</view>
			</view>
		</uni-popup>

		<!-- 审核备注弹窗 -->
		<uni-popup ref="remarkPopup" type="center" border-radius="20rpx">
			<view class="remark-popup">
				<view class="remark-header">
					<text class="remark-title">{{ remarkTitle }}</text>
				</view>
				<view class="remark-content">
					<textarea
						v-model="auditRemark"
						class="remark-input"
						placeholder="请输入审核备注（选填）"
						maxlength="200"
					></textarea>
					<text class="remark-count">{{ auditRemark.length }}/200</text>
				</view>
				<view class="remark-actions">
					<button class="remark-btn cancel-btn" @click="closeRemarkPopup">取消</button>
					<button class="remark-btn confirm-btn" @click="confirmAuditAction">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { dayjs } from '../../utils/dayjs.min'

	// 响应式数据
	const auditList = ref([])
	const currentTab = ref(0)
	const userId = ref('')
	const selectedStatus = ref('全部状态')
	const selectedDateRange = ref('全部时间')
	const selectedPriority = ref('全部优先级')
	const searchKeyword = ref('')
	const searchTimer = ref(null)

	// 弹窗相关
	const filterPopup = ref(null)
	const detailPopup = ref(null)
	const remarkPopup = ref(null)
	const filterTitle = ref('')
	const currentFilterOptions = ref([])
	const selectedFilterIndex = ref(0)
	const currentFilterType = ref('')
	const currentAuditItem = ref(null)
	const auditRemark = ref('')
	const remarkTitle = ref('')
	const currentAuditAction = ref('')

	// 审核类型配置
	const auditTabs = ref([
		{ name: '全部', type: 'all', count: 0 },
		{ name: '水电表', type: 'utility', count: 0 },
		{ name: '维修单', type: 'maintenance', count: 0 },
		{ name: '保洁单', type: 'cleaning', count: 0 },
		{ name: '入住申请', type: 'checkin', count: 0 },
		{ name: '退租申请', type: 'checkout', count: 0 },
		{ name: '续租申请', type: 'renewal', count: 0 },
		{ name: '换房申请', type: 'transfer', count: 0 }
	])

	// 状态筛选选项
	const statusOptions = [
		{ text: '全部状态', value: 'all' },
		{ text: '待审核', value: 0 },
		{ text: '已通过', value: 1 },
		{ text: '已拒绝', value: 2 }
	]

	// 时间筛选选项
	const dateOptions = [
		{ text: '全部时间', value: 'all' },
		{ text: '今天', value: 'today' },
		{ text: '最近3天', value: '3days' },
		{ text: '最近7天', value: '7days' },
		{ text: '最近30天', value: '30days' }
	]

	// 优先级筛选选项
	const priorityOptions = [
		{ text: '全部优先级', value: 'all' },
		{ text: '高优先级', value: 3 },
		{ text: '中优先级', value: 2 },
		{ text: '低优先级', value: 1 }
	]

	// 计算属性
	const totalPending = computed(() => {
		return auditList.value.filter(item => item.status === 0).length
	})

	const todayProcessed = computed(() => {
		const today = dayjs().format('YYYY-MM-DD')
		return auditList.value.filter(item =>
			item.status !== 0 &&
			dayjs(item.audit_time).format('YYYY-MM-DD') === today
		).length
	})

	const totalProcessed = computed(() => {
		return auditList.value.filter(item => item.status !== 0).length
	})

	const filteredAuditList = computed(() => {
		let filtered = auditList.value

		// 按关键词搜索
		if (searchKeyword.value.trim()) {
			const keyword = searchKeyword.value.trim().toLowerCase()
			filtered = filtered.filter(item =>
				item.title.toLowerCase().includes(keyword) ||
				item.description.toLowerCase().includes(keyword) ||
				item.applicant_name.toLowerCase().includes(keyword) ||
				item.room_info.toLowerCase().includes(keyword)
			)
		}

		// 按类型筛选
		if (currentTab.value > 0) {
			const tabType = auditTabs.value[currentTab.value].type
			filtered = filtered.filter(item => item.type === tabType)
		}

		// 按状态筛选
		if (selectedStatus.value !== '全部状态') {
			const statusValue = statusOptions.find(option => option.text === selectedStatus.value)?.value
			if (statusValue !== 'all') {
				filtered = filtered.filter(item => item.status === statusValue)
			}
		}

		// 按优先级筛选
		if (selectedPriority.value !== '全部优先级') {
			const priorityValue = priorityOptions.find(option => option.text === selectedPriority.value)?.value
			if (priorityValue !== 'all') {
				filtered = filtered.filter(item => item.priority === priorityValue)
			}
		}

		// 按时间筛选
		if (selectedDateRange.value !== '全部时间') {
			const dateValue = dateOptions.find(option => option.text === selectedDateRange.value)?.value
			if (dateValue !== 'all') {
				const now = dayjs()
				let startDate

				switch (dateValue) {
					case 'today':
						startDate = now.startOf('day')
						break
					case '3days':
						startDate = now.subtract(3, 'day').startOf('day')
						break
					case '7days':
						startDate = now.subtract(7, 'day').startOf('day')
						break
					case '30days':
						startDate = now.subtract(30, 'day').startOf('day')
						break
				}

				if (startDate) {
					filtered = filtered.filter(item =>
						dayjs(item.create_time).isAfter(startDate)
					)
				}
			}
		}

		return filtered.sort((a, b) => {
			// 高优先级排在前面
			if (a.priority !== b.priority) {
				return b.priority - a.priority
			}
			// 待审核的排在前面
			if (a.status === 0 && b.status !== 0) return -1
			if (a.status !== 0 && b.status === 0) return 1
			// 按创建时间倒序
			return new Date(b.create_time) - new Date(a.create_time)
		})
	})

	// 页面加载
	onLoad((e) => {
		console.log("onLoad", e)
		userId.value = uniCloud.getCurrentUserInfo().uid
		if (!userId.value) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
			return
		}
		initData()
	})

	onShow(() => {
		// 页面显示时刷新数据
		if (userId.value) {
			initData()
		}
	})

	// 初始化数据
	const initData = async () => {
		uni.showLoading({
			title: '加载中...'
		})
		try {
			await getAuditList()
			updateTabCounts()
		} catch (error) {
			console.error('初始化数据失败:', error)
			uni.showToast({
				title: '加载数据失败',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	}

	// 获取审核列表数据
	const getAuditList = async () => {
		try {
			// 调用云函数获取审核列表
			const auditObj = uniCloud.importObject('audit')
			const res = await auditObj.getAuditList({
				landlord_id: userId.value,
				page: 1,
				limit: 100
			})

			if (res.errCode === 0) {
				auditList.value = res.data.list
			} else {
				throw new Error(res.errMsg)
			}
		} catch (error) {
			console.error('获取审核列表失败:', error)
			// 如果云函数调用失败，使用模拟数据
			const mockData = [
				{
					_id: '1',
					type: 'utility',
					title: '2024年1月水电表抄录',
					description: '101房间水电表抄录待审核',
					applicant_name: '张三',
					applicant_phone: '13800138001',
					room_info: '1号楼101室',
					status: 0,
					priority: 2,
					create_time: new Date().getTime() - 3600000,
					attachments: []
				},
				{
					_id: '2',
					type: 'maintenance',
					title: '卫生间水龙头维修',
					description: '卫生间水龙头漏水，需要更换',
					applicant_name: '李四',
					applicant_phone: '13800138002',
					room_info: '1号楼102室',
					status: 0,
					priority: 3,
					create_time: new Date().getTime() - 7200000,
					attachments: [
						{ name: '故障图片1.jpg', url: '/static/example.jpg' }
					]
				},
				{
					_id: '3',
					type: 'cleaning',
					title: '深度保洁服务',
					description: '退租后房间深度保洁',
					applicant_name: '王五',
					applicant_phone: '13800138003',
					room_info: '1号楼103室',
					status: 1,
					create_time: new Date().getTime() - 86400000,
					audit_time: new Date().getTime() - 3600000,
					audit_remark: '已安排保洁人员',
					attachments: []
				},
				{
					_id: '4',
					type: 'checkin',
					title: '入住申请',
					description: '申请入住1号楼104室',
					applicant_name: '赵六',
					applicant_phone: '13800138004',
					room_info: '1号楼104室',
					status: 0,
					create_time: new Date().getTime() - 1800000,
					attachments: [
						{ name: '身份证.jpg', url: '/static/example.jpg' },
						{ name: '收入证明.pdf', url: '/static/example.pdf' }
					]
				},
				{
					_id: '5',
					type: 'checkout',
					title: '退租申请',
					description: '申请退租1号楼105室',
					applicant_name: '孙七',
					applicant_phone: '13800138005',
					room_info: '1号楼105室',
					status: 2,
					create_time: new Date().getTime() - 172800000,
					audit_time: new Date().getTime() - 86400000,
					audit_remark: '押金未结清，暂不同意退租',
					attachments: []
				}
			]

			auditList.value = mockData
		}
	}

	// 更新标签页计数
	const updateTabCounts = () => {
		auditTabs.value.forEach((tab, index) => {
			if (index === 0) {
				// 全部
				tab.count = auditList.value.filter(item => item.status === 0).length
			} else {
				// 具体类型
				tab.count = auditList.value.filter(item =>
					item.type === tab.type && item.status === 0
				).length
			}
		})
	}

	// 切换标签页
	const switchTab = (index) => {
		currentTab.value = index
	}

	// 显示状态筛选
	const showStatusFilter = () => {
		filterTitle.value = '状态筛选'
		currentFilterOptions.value = statusOptions
		currentFilterType.value = 'status'
		selectedFilterIndex.value = statusOptions.findIndex(option => option.text === selectedStatus.value)
		filterPopup.value?.open()
	}

	// 显示时间筛选
	const showDateFilter = () => {
		filterTitle.value = '时间筛选'
		currentFilterOptions.value = dateOptions
		currentFilterType.value = 'date'
		selectedFilterIndex.value = dateOptions.findIndex(option => option.text === selectedDateRange.value)
		filterPopup.value?.open()
	}

	// 显示优先级筛选
	const showPriorityFilter = () => {
		filterTitle.value = '优先级筛选'
		currentFilterOptions.value = priorityOptions
		currentFilterType.value = 'priority'
		selectedFilterIndex.value = priorityOptions.findIndex(option => option.text === selectedPriority.value)
		filterPopup.value?.open()
	}

	// 搜索输入处理
	const onSearchInput = () => {
		// 防抖处理
		if (searchTimer.value) {
			clearTimeout(searchTimer.value)
		}
		searchTimer.value = setTimeout(() => {
			// 这里可以添加搜索统计或其他逻辑
			console.log('搜索关键词:', searchKeyword.value)
		}, 500)
	}

	// 清除搜索
	const clearSearch = () => {
		searchKeyword.value = ''
		if (searchTimer.value) {
			clearTimeout(searchTimer.value)
		}
	}

	// 选择筛选选项
	const selectFilterOption = (index) => {
		selectedFilterIndex.value = index
	}

	// 确认筛选
	const confirmFilter = () => {
		const selectedOption = currentFilterOptions.value[selectedFilterIndex.value]

		switch (currentFilterType.value) {
			case 'status':
				selectedStatus.value = selectedOption.text
				break
			case 'date':
				selectedDateRange.value = selectedOption.text
				break
			case 'priority':
				selectedPriority.value = selectedOption.text
				break
		}

		closeFilterPopup()
	}

	// 关闭筛选弹窗
	const closeFilterPopup = () => {
		filterPopup.value?.close()
	}

	// 查看详情
	const viewDetail = (item) => {
		currentAuditItem.value = item
		detailPopup.value?.open()
	}

	// 关闭详情弹窗
	const closeDetailPopup = () => {
		detailPopup.value?.close()
		currentAuditItem.value = null
	}

	// 预览附件
	const previewAttachment = (attachment) => {
		// 根据文件类型进行预览
		if (attachment.url.includes('.jpg') || attachment.url.includes('.png')) {
			uni.previewImage({
				urls: [attachment.url]
			})
		} else {
			uni.showToast({
				title: '暂不支持预览此类型文件',
				icon: 'none'
			})
		}
	}

	// 审核通过
	const approveAudit = (item) => {
		currentAuditItem.value = item
		currentAuditAction.value = 'approve'
		remarkTitle.value = '审核通过'
		auditRemark.value = ''
		remarkPopup.value?.open()
	}

	// 审核拒绝
	const rejectAudit = (item) => {
		currentAuditItem.value = item
		currentAuditAction.value = 'reject'
		remarkTitle.value = '审核拒绝'
		auditRemark.value = ''
		remarkPopup.value?.open()
	}

	// 带备注的审核通过
	const approveAuditWithRemark = () => {
		currentAuditAction.value = 'approve'
		remarkTitle.value = '审核通过'
		auditRemark.value = ''
		remarkPopup.value?.open()
	}

	// 带备注的审核拒绝
	const rejectAuditWithRemark = () => {
		currentAuditAction.value = 'reject'
		remarkTitle.value = '审核拒绝'
		auditRemark.value = ''
		remarkPopup.value?.open()
	}

	// 关闭备注弹窗
	const closeRemarkPopup = () => {
		remarkPopup.value?.close()
		auditRemark.value = ''
	}

	// 确认审核操作
	const confirmAuditAction = async () => {
		if (!currentAuditItem.value) return

		const isApprove = currentAuditAction.value === 'approve'
		const actionText = isApprove ? '通过' : '拒绝'

		uni.showModal({
			title: '确认操作',
			content: `确定要${actionText}这个申请吗？`,
			confirmText: `确定${actionText}`,
			cancelText: '取消',
			success: async (res) => {
				if (res.confirm) {
					await performAuditAction(isApprove)
				}
			}
		})
	}

	// 执行审核操作
	const performAuditAction = async (isApprove) => {
		uni.showLoading({
			title: '处理中...'
		})

		try {
			// 调用云函数处理审核
			const auditObj = uniCloud.importObject('audit')
			const res = await auditObj.processAudit({
				audit_id: currentAuditItem.value._id,
				status: isApprove ? 1 : 2,
				audit_remark: auditRemark.value || ''
			})

			if (res.errCode === 0) {
				// 更新本地数据
				const index = auditList.value.findIndex(item => item._id === currentAuditItem.value._id)
				if (index !== -1) {
					auditList.value[index] = {
						...auditList.value[index],
						status: isApprove ? 1 : 2,
						audit_time: new Date().getTime(),
						audit_user_id: userId.value,
						audit_remark: auditRemark.value || ''
					}
				}

				uni.hideLoading()
				uni.showToast({
					title: `审核${isApprove ? '通过' : '拒绝'}成功`,
					icon: 'success'
				})

				// 关闭弹窗
				closeRemarkPopup()
				closeDetailPopup()

				// 更新统计数据
				updateTabCounts()
			} else {
				throw new Error(res.errMsg)
			}
		} catch (error) {
			uni.hideLoading()
			console.error('审核操作失败:', error)

			// 如果云函数调用失败，使用本地更新
			const auditData = {
				status: isApprove ? 1 : 2,
				audit_time: new Date().getTime(),
				audit_user_id: userId.value,
				audit_remark: auditRemark.value || ''
			}

			// 更新本地数据
			const index = auditList.value.findIndex(item => item._id === currentAuditItem.value._id)
			if (index !== -1) {
				auditList.value[index] = {
					...auditList.value[index],
					...auditData
				}
			}

			uni.hideLoading()
			uni.showToast({
				title: `审核${isApprove ? '通过' : '拒绝'}成功`,
				icon: 'success'
			})

			// 关闭弹窗
			closeRemarkPopup()
			closeDetailPopup()

			// 更新统计数据
			updateTabCounts()
		}
	}

	// 工具函数
	const getAuditTypeName = (type) => {
		const typeMap = {
			utility: '水电表审核',
			maintenance: '维修申请',
			cleaning: '保洁申请',
			checkin: '入住申请',
			checkout: '退租申请',
			renewal: '续租申请',
			transfer: '换房申请'
		}
		return typeMap[type] || '未知类型'
	}

	const getAuditIcon = (type) => {
		const iconMap = {
			utility: 'fire',
			maintenance: 'gear',
			cleaning: 'star',
			checkin: 'home',
			checkout: 'back',
			renewal: 'reload',
			transfer: 'redo'
		}
		return iconMap[type] || 'help'
	}

	const getAuditColor = (type) => {
		const colorMap = {
			utility: '#FF6B35',
			maintenance: '#4ECDC4',
			cleaning: '#45B7D1',
			checkin: '#96CEB4',
			checkout: '#FFEAA7',
			renewal: '#DDA0DD',
			transfer: '#98D8C8'
		}
		return colorMap[type] || '#999999'
	}

	const getStatusText = (status) => {
		const statusMap = {
			0: '待审核',
			1: '已通过',
			2: '已拒绝'
		}
		return statusMap[status] || '未知状态'
	}

	const getStatusClass = (status) => {
		const classMap = {
			0: 'status-pending',
			1: 'status-approved',
			2: 'status-rejected'
		}
		return classMap[status] || ''
	}

	const formatDate = (timestamp) => {
		return dayjs(timestamp).format('MM-DD HH:mm')
	}

	const formatDateTime = (timestamp) => {
		return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
	}

	const getEmptyText = () => {
		if (currentTab.value === 0) {
			return selectedStatus.value === '待审核' ? '暂无待审核项目' : '暂无审核记录'
		} else {
			const tabName = auditTabs.value[currentTab.value].name
			return `暂无${tabName}记录`
		}
	}
</script>

<style lang="scss" scoped>
.audit-center {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.search-section {
	background-color: #fff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;

	.search-box {
		display: flex;
		align-items: center;
		padding: 20rpx 24rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;

		&:focus-within {
			border-color: #01B862;
			background-color: #fff;
			box-shadow: 0 2rpx 8rpx rgba(1, 184, 98, 0.1);
		}

		.search-input {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			margin: 0 16rpx;
			border: none;
			background: transparent;

			&::placeholder {
				color: #999;
			}
		}

		.search-clear {
			padding: 8rpx;
			border-radius: 50%;
		}
	}
}

.header-stats {
	display: flex;
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.stat-item {
		flex: 1;
		text-align: center;

		.stat-number {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			color: #01B862;
			margin-bottom: 10rpx;
		}

		.stat-label {
			font-size: 24rpx;
			color: #666;
		}
	}
}

.filter-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	box-sizing: border-box;
	.filter-tabs {
		display: flex;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		overflow-x: auto;
		white-space: nowrap;

		.filter-tab {
			position: relative;
			padding: 30rpx 20rpx;
			margin-right: 40rpx;

			&.active {
				.tab-text {
					color: #01B862;
					font-weight: bold;
				}

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #01B862;
					border-radius: 2rpx;
				}
			}

			.tab-text {
				font-size: 28rpx;
				color: #333;
			}

			.tab-badge {
				position: absolute;
				top: 20rpx;
				right: 0;
				background-color: #ff4d4f;
				color: #fff;
				font-size: 20rpx;
				padding: 4rpx 8rpx;
				border-radius: 20rpx;
				min-width: 32rpx;
				text-align: center;
				line-height: 1;
			}
		}
	}

	.filter-controls {
		display: flex;
		padding: 20rpx 30rpx;
		flex-wrap: wrap;
		gap: 20rpx;

		.filter-item {
			display: flex;
			align-items: center;
			padding: 16rpx 24rpx;
			background-color: #f8f8f8;
			border-radius: 8rpx;
			white-space: nowrap;
			transition: all 0.3s ease;

			&:active {
				background-color: #e8e8e8;
				transform: scale(0.98);
			}

			.filter-text {
				font-size: 24rpx;
				color: #333;
				margin-right: 8rpx;
			}
		}
	}
}

.audit-list {
	padding: 0 30rpx;

	.audit-item {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.audit-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;

			.audit-type {
				display: flex;
				align-items: center;

				.type-text {
					font-size: 26rpx;
					font-weight: bold;
					color: #333;
					margin-left: 8rpx;
				}
			}

			.audit-status {
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				font-size: 22rpx;

				&.status-pending {
					background-color: #fff7e6;
					color: #fa8c16;
				}

				&.status-approved {
					background-color: #f6ffed;
					color: #52c41a;
				}

				&.status-rejected {
					background-color: #fff2f0;
					color: #ff4d4f;
				}
			}
		}

		.audit-content {
			margin-bottom: 20rpx;

			.audit-title {
				display: block;
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 8rpx;
			}

			.audit-desc {
				font-size: 24rpx;
				color: #666;
				line-height: 1.5;
			}
		}

		.audit-info {
			margin-bottom: 20rpx;

			.info-item {
				display: flex;
				margin-bottom: 8rpx;

				.info-label {
					font-size: 24rpx;
					color: #999;
					width: 120rpx;
				}

				.info-value {
					font-size: 24rpx;
					color: #333;
					flex: 1;
				}
			}
		}

		.audit-actions {
			display: flex;
			justify-content: flex-end;
			gap: 20rpx;

			.action-btn {
				padding: 16rpx 32rpx;
				border-radius: 8rpx;
				font-size: 24rpx;
				border: none;

				&.reject-btn {
					background-color: #fff2f0;
					color: #ff4d4f;
				}

				&.approve-btn {
					background-color: #f6ffed;
					color: #52c41a;
				}
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 100rpx 30rpx;

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

.filter-popup {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.popup-close {
			padding: 10rpx;
		}
	}

	.popup-content {
		max-height: 600rpx;
		overflow-y: auto;

		.filter-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #f8f8f8;

			&.selected {
				background-color: #f6ffed;

				.option-text {
					color: #01B862;
				}
			}

			.option-text {
				font-size: 28rpx;
				color: #333;
			}
		}
	}

	.popup-actions {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;

		.popup-btn {
			flex: 1;
			padding: 24rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			border: none;

			&.cancel-btn {
				background-color: #f5f5f5;
				color: #666;
			}

			&.confirm-btn {
				background-color: #01B862;
				color: #fff;
			}
		}
	}
}

.detail-popup {
	background-color: #fff;
	border-radius: 20rpx;
	width: 680rpx;
	max-height: 80vh;

	.detail-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.detail-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.detail-close {
			padding: 10rpx;
		}
	}

	.detail-content {
		max-height: 60vh;
		overflow-y: auto;
		padding: 30rpx;

		.detail-info {
			.detail-row {
				display: flex;
				margin-bottom: 20rpx;

				.detail-label {
					font-size: 26rpx;
					color: #666;
					width: 160rpx;
					flex-shrink: 0;
				}

				.detail-value {
					font-size: 26rpx;
					color: #333;
					flex: 1;
					word-break: break-all;
				}
			}

			.attachment-list {
				flex: 1;

				.attachment-item {
					display: flex;
					align-items: center;
					padding: 16rpx;
					background-color: #f8f8f8;
					border-radius: 8rpx;
					margin-bottom: 12rpx;

					.attachment-name {
						font-size: 24rpx;
						color: #333;
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	.detail-actions {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
		border-top: 1rpx solid #f0f0f0;

		.detail-btn {
			flex: 1;
			padding: 24rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			border: none;

			&.reject-btn {
				background-color: #fff2f0;
				color: #ff4d4f;
			}

			&.approve-btn {
				background-color: #f6ffed;
				color: #52c41a;
			}
		}
	}
}

.remark-popup {
	background-color: #fff;
	border-radius: 20rpx;
	width: 600rpx;

	.remark-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		text-align: center;

		.remark-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.remark-content {
		padding: 30rpx;

		.remark-input {
			width: 100%;
			min-height: 200rpx;
			padding: 20rpx;
			border: 1rpx solid #e0e0e0;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: #333;
			background-color: #fafafa;
			box-sizing: border-box;
		}

		.remark-count {
			display: block;
			text-align: right;
			font-size: 22rpx;
			color: #999;
			margin-top: 10rpx;
		}
	}

	.remark-actions {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
		border-top: 1rpx solid #f0f0f0;

		.remark-btn {
			flex: 1;
			padding: 24rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			border: none;

			&.cancel-btn {
				background-color: #f5f5f5;
				color: #666;
			}

			&.confirm-btn {
				background-color: #01B862;
				color: #fff;
			}
		}
	}
}
</style>
