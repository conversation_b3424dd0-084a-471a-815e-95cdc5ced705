<template>
	<view class="container">
		<uni-section :title="'房源信息:'+building_name" type="line" class="heard" style="background-color: #EDF2F4;"></uni-section>
		<view class="select_mode">
			<text>房型</text>
			<uni-data-select :localdata="modeList" class="select_bg" v-model="modeName"  @change="changeSelect"></uni-data-select>
			<button type="primary" size="mini" @click="createLayout">新建房型</button>
		</view>
		<unicloud-db ref="udb" v-slot:default="{data, loading, error, options}" collection="fangke_room" :where="sWhere"
			 @load="onqueryload" @error="onqueryerror">
			<view v-if="error">{{error.message}}</view>
			<view v-else-if="loading">正在加载...</view>
			<view v-else>
				<view class="content" v-for="(item,index) in floors" :key="index">
					<uni-section :title="'第'+item.floor+'层'" type="line" class="heard" style="background-color: #EDF2F4;">
						<template v-slot:right>
							<view class="floor_apply_btn" @click="setConfig(item,-1)">整层应用</view>
						</template>
					</uni-section>
					<view class="body">
						<view class="room" v-for="(room,roomIndex) in item.rooms" :key="room._id">
							<button class="num" type="default" size="mini" @click="setConfig(room,roomIndex)">{{room.name}}</button>
							<text class="room_layout">{{room.config_layout?room.config_layout:""}}</text>
						</view>
					</view>
				</view>
				
			</view>
		</unicloud-db>
		
		<view class="bottom">
			<button class="left" @click="close">取消</button>
			<button class="right" type="primary" @click="save">保存</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
	} from 'vue';
	import {
		onLoad,
		onReady
	} from "@dcloudio/uni-app"
	const db = uniCloud.databaseForJQL()
	// 响应式数据
	const floor = ref(1);
	const num = ref(1);
	const floors = ref([]);
	const roomsList = ref([]);
	const name = ref('');
	const origen = ref('');
	const building_id = ref("")
	const udb = ref(null)
	const sWhere = ref("")
	const building_name = ref("")
	const removeList = ref([])
	const uid = ref('')
	const modeList = ref([])
	const modeName = ref('')
	const selectedModeId = ref('') // 当前选择的房型ID
	const changedRooms = ref([]) // 记录发生变化的房间
	const clickTimeout = ref(null) // 用于区分单击和双击
	const CLICK_DELAY = 300 // 点击延迟时间（毫秒）
	let colList = [
		db.collection('fangke_room').where(`building_id =="${building_id.value}"`).getTemp(),
	]
	// 页面加载时处理参数
	onLoad((e) => {
		console.log("buidlingRoom", e);
		if (e.building_id) {
			building_id.value = e.building_id
			sWhere.value = `building_id =="${building_id.value}"`
		}
		uid.value = uniCloud.getCurrentUserInfo().uid
		getMode()
	});
	
	 const getMode = () =>{
		 console.log("getMode",uid.value);
		 db.collection("fangke_room_model").where(`uid == "${uid.value}"`).field("_id,name").get().then(res =>{
			console.log("获取模板",res.data);
			res.data.forEach(item =>{
				modeList.value.push({
					text:item.name,
					value : item._id
				})
			})
			if(res.data.length >0){
				modeName.value = res.data[0].name
				selectedModeId.value = res.data[0]._id
			}
			console.log("modeName.value",modeName.value);
		})
	}

	onReady(() => {
		// udb.value.loadDate() //这里需要手动加载数据
	})


	const onqueryload = (data, ended) => {
		// `data` 当前查询结果
		// `ended` 是否有更多数据
		// 可在此处预处理数据，然后再渲染界面
		console.log("onqueryload", data);
		roomsList.value = data
		if(data.length >0){
			building_name.value = data[0].building_name
		}
		let res =  groupRoomsByFloor(data)
		console.log("排列后的房间",res);
		floor.value = res.length
		num.value = data.length
		floors.value = res
	}
	const onqueryerror = (e) => {
		console.log("onqueryerror",e);
	}
	
	const groupRoomsByFloor = (data) => {
	
		// 创建一个Map来按楼层分组房间
		const floorMap = new Map();
	
		// 遍历每个房间，将它们添加到对应的楼层组
		data.forEach(room => {
			const floor = room.floor;
			if (!floorMap.has(floor)) {
				floorMap.set(floor, {
					floor,
					rooms: []
				});
			}
			floorMap.get(floor).rooms.push(room);
		});
	
		// 将Map转换为数组并按楼层排序
		const result = Array.from(floorMap.values()).sort((a, b) => a.floor - b.floor);
		console.log("groupRoomsByFloor", result);
		return result;
	}


	const changeSelect = (e) =>{
		console.log("changeSelect:", e);
		const selectedMode = modeList.value.find(item => item.value === e);
		if (selectedMode) {
			modeName.value = selectedMode.text;
			selectedModeId.value = selectedMode.value;
		}
		console.log("modeName.value",modeName.value);
		console.log("selectedModeId.value",selectedModeId.value);
	}
	// 房间配置点击处理
	const setConfig = (item, index) => {
		console.log("setConfig", item, index);
		
		if (!selectedModeId.value || !modeName.value) {
			uni.showToast({
				title: '请先选择房型',
				icon: 'none'
			});
			return;
		}
		
		if (index == -1) {
			// 整层应用
			setFloorConfig(item);
		} else {
			// 单独房间设置 - 区分单击和双击
			handleRoomClick(item, index);
		}
	};

	// 处理房间点击（区分单击和双击）
	const handleRoomClick = (room, roomIndex) => {
		if (clickTimeout.value) {
			// 双击 - 设置所有楼层的同位置房间
			clearTimeout(clickTimeout.value);
			clickTimeout.value = null;
			handleDoubleClick(roomIndex);
		} else {
			// 单击 - 设置延迟，等待可能的第二次点击
			clickTimeout.value = setTimeout(() => {
				clickTimeout.value = null;
				handleSingleClick(room);
			}, CLICK_DELAY);
		}
	};

	// 单击处理 - 切换单个房间的房型显示/隐藏
	const handleSingleClick = (room) => {
		console.log("单击房间:", room.name);
		
		if (room.config_layout === modeName.value) {
			// 如果已经显示当前房型，则隐藏
			room.config_layout = "";
			room.config_layout_id = "";
			removeFromChangedRooms(room._id);
		} else {
			// 显示当前选择的房型
			room.config_layout = modeName.value;
			room.config_layout_id = selectedModeId.value;
			addToChangedRooms(room);
		}
	};

	// 双击处理 - 批量设置所有楼层同位置房间
	const handleDoubleClick = (roomIndex) => {
		console.log("双击房间位置:", roomIndex);
		
		floors.value.forEach(floor => {
			if (floor.rooms[roomIndex]) {
				const room = floor.rooms[roomIndex];
				
				if (room.config_layout === modeName.value) {
					// 如果已经显示当前房型，则隐藏
					room.config_layout = "";
					room.config_layout_id = "";
					removeFromChangedRooms(room._id);
				} else {
					// 显示当前选择的房型
					room.config_layout = modeName.value;
					room.config_layout_id = selectedModeId.value;
					addToChangedRooms(room);
				}
			}
		});
		
		uni.showToast({
			title: '已批量设置同位置房间',
			icon: 'success'
		});
	};

	// 整层应用
	const setFloorConfig = (floor) => {
		console.log("整层应用:", floor.floor);
		
		const hasAllRoomsConfig = floor.rooms.every(room => room.config_layout === modeName.value);
		
		floor.rooms.forEach(room => {
			if (hasAllRoomsConfig) {
				// 如果整层都已经是当前房型，则清除
				room.config_layout = "";
				room.config_layout_id = "";
				removeFromChangedRooms(room._id);
			} else {
				// 否则设置为当前房型
				room.config_layout = modeName.value;
				room.config_layout_id = selectedModeId.value;
				addToChangedRooms(room);
			}
		});
		
		uni.showToast({
			title: hasAllRoomsConfig ? '已清除整层房型' : '已设置整层房型',
			icon: 'success'
		});
	};

	// 添加到变更列表
	const addToChangedRooms = (room) => {
		const existingIndex = changedRooms.value.findIndex(item => item._id === room._id);
		const originalRoom = roomsList.value.find(item => item._id === room._id);
		
		if (existingIndex !== -1) {
			// 更新已存在的变更记录
			changedRooms.value[existingIndex] = {
				...room,
				original_config_layout: originalRoom?.config_layout || "",
				original_config_layout_id: originalRoom?.config_layout_id || ""
			};
		} else {
			// 添加新的变更记录
			changedRooms.value.push({
				...room,
				original_config_layout: originalRoom?.config_layout || "",
				original_config_layout_id: originalRoom?.config_layout_id || ""
			});
		}
	};

	// 从变更列表移除
	const removeFromChangedRooms = (roomId) => {
		const index = changedRooms.value.findIndex(item => item._id === roomId);
		if (index !== -1) {
			const originalRoom = roomsList.value.find(item => item._id === roomId);
			const changedRoom = changedRooms.value[index];
			
			// 如果恢复到原始状态，则从变更列表中移除
			if ((!changedRoom.config_layout && !changedRoom.original_config_layout) ||
				(changedRoom.config_layout === changedRoom.original_config_layout)) {
				changedRooms.value.splice(index, 1);
			}
		}
	};

	// 关闭页面
	const close = () => {
		uni.navigateBack();
	};

	// 新建房型
	const createLayout = () => {
		uni.navigateTo({
			url: '/pagesB/layout/layout'
		});
	};

	// 保存房间数据
	const save = async () => {
		console.log("房型配置变更", changedRooms.value);
		
		let updateList = []
		let configUpdateList = [] // 房型配置更新列表
		

		// 处理房型配置变更
		changedRooms.value.forEach(changedRoom => {
			// const originalRoom = roomsList.value.find(room => room._id === changedRoom._id);
			// if (originalRoom && 
			// 	(originalRoom.config_layout !== changedRoom.config_layout || 
			// 	 originalRoom.config_layout_id !== changedRoom.config_layout_id)) {
				
			// }
			
			configUpdateList.push({
								_id: changedRoom._id,
								config_layout: changedRoom.config_layout_id
			});
		});
		console.log('changedRooms',configUpdateList);
		try {
			uni.showLoading({
				title: '保存中...'
			});

			let params = {
				uid:uid.value,
				list:configUpdateList
			}
			let res = await uni.$lkj.api.setRoomConfig(params)
			console.log("更新房型配置成功", res);
			uni.hideLoading();
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			});

			// 清空变更记录
			changedRooms.value = [];
			
			console.log("操作完成");
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.reLaunch({
					url:"/pages/house/house"
				})
			}, 1500);

		} catch (error) {
			uni.hideLoading();
			console.error("保存失败", error);
			uni.showToast({
				title: '保存失败',
				icon: 'error'
			});
		}
	};

	// 输入框聚焦事件
	const focus = (e) => {
		console.log("focus", e);
		origen.value = e.detail.value;
	};
</script>
<style lang="scss">
	.container {
		padding: 10rpx 20rpx;
		box-sizing: border-box;
		
		.select_mode{
			display: flex;
			font-size: 30rpx;
			gap: 10rpx;
			align-items: center;
			.select_bg{
				flex: 1;
			}
		}
		
		.title {
			margin: 20rpx;
			display: flex;
			justify-content: space-between;
		}

		.tip{
			font-size: 28rpx;
			background-color: orange;
			color: white;
			padding: 20rpx;
		}
		.content {
			display: flex;
			flex-direction: column;

			.head {
				background-color: darkgray;
			}

			.body {
				display: grid;
				grid-template-columns: auto auto auto auto;
				box-sizing: border-box;
				gap: 10rpx;
				.room {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-content: center;
					align-items: center;
					.num {
						border: 1rpx solid gray;
						border-radius: 10rpx;
						transition: all 0.2s ease;
					}
					.num:active {
						transform: scale(0.95);
						background-color: #f0f0f0;
					}
					.room_layout{
						font-size: 22rpx;
						color: #07c160;
						margin-top: 8rpx;
						padding: 4rpx 8rpx;
						background-color: #e8f5e8;
						border-radius: 6rpx;
						border: 1px solid #07c160;
						min-height: 20rpx;
						text-align: center;
						font-weight: 500;
						transition: all 0.3s ease;
					}
					.room_layout:empty {
						display: none;
					}
				}
			}

			.foot {
				box-sizing: border-box;

				.slide {
					width: 100%;
					height: 2rpx;
					background-color: #eee;
					margin: 0 20rpx;
				}
			}
		}

		.bottom {
			display: flex;
			margin: 20rpx 0rpx;

			button {
				padding: 0rpx 40rpx;
			}

		}

		.floor_apply_btn {
			color: #07c160;
			font-size: 26rpx;
			font-weight: 500;
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			border: 1px solid #07c160;
			background-color: #e8f5e8;
			transition: all 0.2s ease;
		}

		.floor_apply_btn:active {
			background-color: #07c160;
			color: #fff;
			transform: scale(0.95);
		}
	}
</style>