<template>
  <view class="container">
    <!-- 房间信息 -->
    <view class="room-info">
      <view class="room-bullet"></view>
      <text class="room-name">{{ buildingName}}-{{roomName}}</text>
    </view>

    <!-- 待收金额显示 -->
    <view v-if="isViewMode && pendingAmount > 0" class="pending-amount">
      <text class="pending-label">待收金额：</text>
      <text class="pending-value">¥{{ (pendingAmount / 100).toFixed(2) }}</text>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 租客姓名 -->
      <view class="form-item">
        <text class="label">租客姓名</text>
        <input
          class="input"
          v-model="formData.name"
          placeholder="请输入租客姓名"
          maxlength="20"
          :disabled="isViewMode"
        />
      </view>

      <!-- 手机号码 -->
      <view class="form-item">
        <text class="label">手机号码</text>
        <input
          class="input"
          v-model="formData.phone"
          placeholder="请输入手机号码"
          type="number"
          maxlength="11"
        />
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <text class="label">性别</text>
        <view class="gender-selector">
          <view
            :class="['gender-option', formData.gender === '男' ? 'active' : '']"
            @click="formData.gender = '男'"
            >男</view
          >
          <view
            :class="['gender-option', formData.gender === '女' ? 'active' : '']"
            @click="formData.gender = '女'"
            >女</view
          >
        </view>
      </view>

      <!-- 预定金额 -->
      <view class="form-item">
        <text class="label">预定金额（元）</text>
        <input
          class="input"
          v-model="formData.amount"
          placeholder="请输入预定金额（元）"
          type="digit"
          :disabled="isViewMode"
        />
      </view>

      <!-- 预定日期 -->
      <view class="form-item">
        <text class="label">预定日期</text>
        <picker
          mode="date"
          :value="formData.bookDate"
          @change="onBookDateChange"
          class="date-picker"
          :disabled="isViewMode"
        >
          <view class="picker-display">
            <text>{{ formData.bookDate || '请选择' }}</text>
          </view>
        </picker>
      </view>

      <!-- 最晚签约日 -->
      <view class="form-item">
        <text class="label">最晚签约日</text>
        <picker
          mode="date"
          :value="formData.latestDate"
          @change="onLatestDateChange"
          class="date-picker"
          :disabled="isViewMode"
        >
          <view class="picker-display">
            <text>{{ formData.latestDate || '请选择' }}</text>
          </view>
        </picker>
      </view>

      <!-- 备注 -->
      <view class="form-item remark-item">
        <text class="label">备注</text>
        <view class="textarea-container">
          <textarea
            class="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
            maxlength="200"
            @input="onRemarkInput"
          />
          <view class="char-count">{{ formData.remark.length }}/200</view>
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="button-container">
      <!-- 未预定状态：只显示预定按钮 -->
      <button
        v-if="!isViewMode"
        class="submit-btn"
        :class="{ disabled: isSaving }"
        :disabled="isSaving"
        @click="submitBooking"
      >
        {{ isSaving ? '提交中...' : '完成预定' }}
      </button>

      <!-- 已预定状态：显示取消预定和收款按钮 -->
      <view v-else class="action-buttons">
        <button
          class="cancel-btn"
          :disabled="isSaving"
          @click="cancelBooking"
        >
          取消预定
        </button>
        <button
          class="payment-btn"
          :disabled="isSaving"
          @click="confirmPayment"
        >
          收款
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { dayjs } from '../../utils/dayjs.min.js'
const dbObj = uniCloud.importObject("prebook")
const db = uniCloud.databaseForJQL()

// 表单数据
const formData = ref({
  name: '',
  phone: '',
  gender: '男',
  amount: '',
  bookDate: '',
  latestDate: '',
  remark: ''
})

// 房间信息
const roomName = ref('')
const roomId = ref('')
const buildingName = ref('')
const isSaving = ref(false)
const isViewMode = ref(false) // 是否已预定
const bookingId = ref('') // 预定记录ID
const pendingAmount = ref(0) // 待收金额

	// 页面加载
	onLoad(options => {
		console.log('addBooking onLoad:', options)

		// 检查是否为查看模式
		if (options.status !== "0") {
			isViewMode.value = true
			console.log('进入查看预约模式')
		}

		// 获取房间信息
		if (options.room_id) {
			roomId.value = options.room_id
			let split = options.name.split('-')
			buildingName.value = split[0]
			roomName.value = split[1]
			// 如果是查看模式，获取现有预定信息
			if (isViewMode.value) {
				getBookingInfo()
			} else {
				formData.value.bookDate = dayjs().format("YYYY-MM-DD")
			}
		}

	})

// 获取现有预定信息
	const getBookingInfo = async() => {
  try {
			console.log('获取房间预定信息:', roomId.value)
			
			// 查询该房间的预定记录
    const result = await db
      .collection('fangke_addbooking')
      .where(`(status == 1 || status == 3) && room_id == "${roomId.value}" `)
      .orderBy('create_time', 'desc')
      .limit(1)
      .get()

			console.log('预定信息查询结果:', result)

    if (result.data && result.data.length > 0) {
      const bookingData = result.data[0]
      bookingId.value = bookingData._id

      // 计算待收金额
      if (bookingData.payment_status === 0) { // 待收款状态
        pendingAmount.value = bookingData.money || 0
      }

      // 填充表单数据
      formData.value = {
        name: bookingData.name || '',
        phone: bookingData.phone || '',
        gender: bookingData.sex === 0 ? '男' : '女',
        amount: bookingData.money ? (bookingData.money / 100).toString() : '',
        bookDate: bookingData.target_date || '',
        latestDate: bookingData.canceling_date || '',
        remark: bookingData.comment || ''
      }

				// console.log('已加载预定信息:', formData.value)

				// 显示提示
      uni.showToast({
        title: '已加载预定信息',
        icon: 'success'
      })
			} else {
				console.log('未找到该房间的预定信息')
				uni.showToast({
					title: '未找到预定信息',
					icon: 'none'
				})
    }
  } catch (error) {
    console.error('获取预定信息失败:', error)
    uni.showToast({
      title: '获取预定信息失败',
      icon: 'none'
    })
  }
}

// 取消预定
const cancelBooking = async () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个预定吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          isSaving.value = true
          const uid = uniCloud.getCurrentUserInfo().uid
          
          const result = await dbObj.deleteBooking({
            id: bookingId.value,
            uid: uid
          })

          if (result.errCode === 0) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/house/house'
              })
            }, 800)
          } else {
            throw new Error(result.errMsg)
          }
        } catch (error) {
          console.error('取消预定失败:', error)
          uni.showToast({
            title: '取消失败，请重试',
            icon: 'none'
          })
        } finally {
          isSaving.value = false
        }
      }
    }
  })
}

// 确认收款
const confirmPayment = async () => {
  uni.showModal({
    title: '确认收款',
    content: `确认已收到预定金额 ¥${(pendingAmount.value / 100).toFixed(2)} 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          isSaving.value = true
          
          // 更新收款状态
          const result = await db
            .collection('fangke_addbooking')
            .doc(bookingId.value)
            .update({
              payment_status: 1, // 已收款
              update_time: new Date().getTime()
            })

          if (result.updated > 0) {
            pendingAmount.value = 0 // 清零待收金额
            uni.showToast({
              title: '收款成功',
              icon: 'success'
            })
          } else {
            throw new Error('更新失败')
          }
        } catch (error) {
          console.error('收款失败:', error)
          uni.showToast({
            title: '收款失败，请重试',
            icon: 'none'
          })
        } finally {
          isSaving.value = false
        }
      }
    }
  })
}

// 预定日期变化
	const onBookDateChange = e => {
		formData.value.bookDate = e.detail.value
		// console.log('预定日期:', formData.value.bookDate)
	}

	// 最晚签约日变化
	const onLatestDateChange = e => {
		formData.value.latestDate = e.detail.value
		// console.log('最晚签约日:', formData.value.latestDate)
	}

	// 备注输入
	const onRemarkInput = e => {
		formData.value.remark = e.detail.value
	}

	// 提交预定
	const submitBooking = async () => {
		console.log('提交预定数据:', formData.value)

		// 根据模式决定是新增还是更新
		if (isViewMode.value) {
			await updateBooking()
		} else {
			await createBooking()
		}
	}

	// 创建新预定
	const createBooking = async () => {
		console.log('创建新预定')

		// 表单验证
		if (!formData.value.name.trim()) {
			uni.showToast({
				title: '请输入租客姓名',
				icon: 'none'
			})
			return
		}

		if (!formData.value.phone.trim()) {
			uni.showToast({
				title: '请输入手机号码',
				icon: 'none'
			})
			return
		}

		// 手机号格式验证
		const phoneRegex = /^1[3-9]\d{9}$/
		if (!phoneRegex.test(formData.value.phone)) {
			uni.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			})
			return
		}

		if (!formData.value.amount || parseFloat(formData.value.amount) <= 0) {
			uni.showToast({
				title: '请输入预定金额',
				icon: 'none'
			})
			return
		}

		if (!formData.value.bookDate) {
			uni.showToast({
				title: '请选择预定日期',
				icon: 'none'
			})
			return
		}

		if (!formData.value.latestDate) {
			uni.showToast({
				title: '请选择最晚签约日',
				icon: 'none'
			})
			return
		}

		// 验证最晚签约日不能早于预定日期
		if (new Date(formData.value.latestDate) < new Date(formData.value.bookDate)) {
			uni.showToast({
				title: '最晚签约日不能早于预定日期',
				icon: 'none'
			})
			return
		}

		// 检查房间ID
		if (!roomId.value) {
			uni.showToast({
				title: '房间信息错误，请重新选择',
				icon: 'none'
			})
			return
		}


		// 获取 uniCloud 当前用户信息
		let uid = ""
		try {
			uid = uniCloud.getCurrentUserInfo().uid
		} catch (e) {
			console.log('获取云端用户信息失败:', e)
		}


		if (uid === "") {
			console.log('登录检查失败，原因: 所有登录状态都无效')
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
			// 可以跳转到登录页面
			// uni.navigateTo({ url: '/pages/login/login' })
			return
		}

		// 防止重复提交
		if (isSaving.value) {
			return
		}

		// 设置保存状态
		isSaving.value = true

		// 准备提交数据
		const submitData = {
			uid: uid,
			// uid 由数据库自动设置，不需要手动传递
			name: formData.value.name.trim(),
			phone: formData.value.phone.trim(),
			sex: formData.value.gender === '男' ? 0 : 1,
			money: Math.round(parseFloat(formData.value.amount) * 100), // 转换为分
			target_date: formData.value.bookDate,
			canceling_date: formData.value.latestDate,
			comment: formData.value.remark || '',
			room_id: roomId.value,
			building_name: buildingName.value,
			room_name: roomName.value, // 移除楼栋名称前缀
			status: 1 // 待确认状态
		}

		console.log('准备提交的数据:', submitData)

		// 显示加载提示
		uni.showLoading({
			title: '提交中...',
			mask: true
		})

		// 直接使用 uniCloud 数据库操作（更可靠的方式）
		try {
			if (bookingId.value !== "") {
				await dbObj.deleteBooking({
					uid: uid,
					data: submitData
				}).then(res => {
					console.log('取消预定成功：' + res)
					uni.showToast({
						title: '更新成功',
						icon: 'success'
					})
					setTimeout(() => {
						// uni.navigateBack()
						uni.reLaunch({
							url: '/pages/house/house'
						})
					}, 800)
				})
			} else {
				// 添加预定记录
				const result = await dbObj.addBooking({
					uid: uid,
					data: submitData
				})

				if (result.errCode === 0) {
					console.log('预定成功：', result)
					uni.showToast({
						title: '预定成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/house/house'
						})
					}, 800)
				} else {
					throw new Error(result.errMsg || '预定失败')
				}
			}
			uni.hideLoading()
			isSaving.value = false
		} catch (error) {
			uni.hideLoading()
			isSaving.value = false

			console.error('预定提交失败:', error)

			// 根据错误类型提供更具体的提示
			let errorMessage = '预定失败，请重试'

			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	}
</script>

<style scoped>
.pending-amount {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pending-label {
  font-size: 28rpx;
  color: #856404;
}

.pending-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #e17055;
  margin-left: 10rpx;
}

.input:disabled,
.textarea:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.picker-display:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.button-container {
  padding: 40rpx 20rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.payment-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.payment-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.cancel-btn:active {
  background: #e9ecef;
}

.payment-btn:active {
  opacity: 0.8;
}
.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.room-info {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 30rpx 20rpx;
		margin-bottom: 20rpx;
		border-radius: 12px;
	}

	.room-bullet {
		width: 12px;
		height: 12rpx;
		background-color: #07c160;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.room-name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.form-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 40rpx;
	}

	.form-item {
		display: flex;
		align-items: flex-start;
		padding: 20rpx 0;
		border-bottom: 1px solid #f0f0f0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.form-item .label {
		width: 210rpx;
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		flex-shrink: 0;
		align-self: center;
	}

	.form-item .input {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		border: none;
		text-align: right;
		background: transparent;
	}

	.form-item .input::placeholder {
		color: #999;
	}

	.gender-selector {
		display: flex;
		gap: 20rpx;
		margin-left: auto;
	}

	.gender-option {
		padding: 16rpx 40rpx;
		border: 2px solid #07c160;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #07c160;
		background-color: #fff;
		transition: all 0.3s ease;
		text-align: center;
	}

	.gender-option.active {
		background-color: #07c160;
		color: #fff;
	}

	.date-picker {
		flex: 1;
		margin-left: auto;
		min-width: 200rpx;
		text-align: right;
	}

	.picker-display {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 30rpx;
		color: #333;
	}

	.picker-display .arrow {
		color: #999;
		font-size: 24rpx;
	}

	.textarea-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		margin-top: 0;
	}

	.textarea {
		width: 100%;
		top: 20rpx;
		min-height: 60rpx;
		max-height: 300rpx;
		padding: 8rpx 12rpx;
		border: 1px solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #fafafa;
		resize: none;
		line-height: 1.4;
		box-sizing: border-box;
		margin: 0;
	}

	.textarea::placeholder {
		color: #999;
	}

	.char-count {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		margin-top: 30rpx;
	}

	.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #07c160;
		color: #fff;
		font-size: 32rpx;
		font-weight: 500;
		border: none;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.submit-btn:active {
		background-color: #06a84e;
	}

	.submit-btn.disabled {
		background-color: #a0d468 !important;
		color: rgba(255, 255, 255, 0.7) !important;
		cursor: not-allowed;
	}

	.submit-btn.disabled:active {
		background-color: #a0d468 !important;
		transform: none !important;
	}

	.form-item.remark-item {
		flex-direction: column;
		align-items: stretch;
	}

	.form-item.remark-item .label {
		width: 100%;
		text-align: left;
		padding-right: 0;
		margin-bottom: 8rpx;
		line-height: 1.2;
	}

	.form-item.remark-item .textarea-container {
		width: 100%;
		margin-top: 0;
	}
</style>
