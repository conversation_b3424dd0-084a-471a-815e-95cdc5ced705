// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const {
	result
} = require('result');
let dbJQL;
module.exports = {
	_before: function() { // 通用预处理器
		this.startTime = Date.now();
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		let info = this.getClientInfo()
		console.log("this.getClientInfo", info);
	},

	// 添加预定
	async addBooking(event) {
		let {
			data,
			uid
		} = event
		let { room_id } = data
		// 验证手机号格式
		const phoneRegex = /^1[3-9]\d{9}$/;
		if (!phoneRegex.test(data.phone)) {
			return result(400, '手机号格式不正确', '')
		}

		// 验证日期
		const targetDate = new Date(data.target_date);
		const cancelingDate = new Date(data.canceling_date);
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		if (targetDate < today) {
			return result(400, '预定日期不能早于今天', '')
		}
		if (cancelingDate < targetDate) {
			return result(400, '最晚签约日不能早于预定日期', '')
		}

		// 检查是否已有该房间的有效预定
		const existingBooking = await dbJQL.collection("fangke_addbooking")
			.where(`room_id == "${data.room_id}" && ( status ==1 || status == 3)`)
			.get();
		
		console.log("获得已预约的数据", existingBooking);
		if (existingBooking.data && existingBooking.data.length > 0) {
			let arr = []
			arr = existingBooking.data.map(item => item._id)
			await dbJQL.collection("fangke_addbooking").where(`_id in ['${arr.join("','")}']`).update({
				status: 0,
				update_time: new Date().getTime()
			}).then(res => {
				console.log("修改之前预定状态", res);
			})
		}

		// 插入数据
		const res = await dbJQL.collection("fangke_addbooking").add(data);
		
		if (res.id) {
			// 获取房间当前状态并更新
			await dbJQL.collection("fangke_room").doc(room_id).get().then(async roomRes => {
				console.log("获取房间的room_status", roomRes);
				if (roomRes.data && roomRes.data.length > 0) {
					let roomData = roomRes.data[0];
					let currentStatus = roomData.room_status || [];
					
					// 确保是数组格式
					if (!Array.isArray(currentStatus)) {
						currentStatus = [currentStatus];
					}
					
					// 添加预定状态（7）如果不存在
					if (!currentStatus.includes(7)) {
						currentStatus.push(7);
					}
					
					// 更新房间状态
					await dbJQL.collection("fangke_room").doc(room_id).update({
						room_status: currentStatus,
						booked: true,
						update_time: new Date().getTime()
					}).then(updateRes => {
						console.log("更新房间预定状态成功", updateRes);
					}).catch(updateErr => {
						console.log("更新房间预定状态失败", updateErr);
					});
				}
			}).catch(err => {
				console.log("获取房间信息失败", err);
			});
			
			return result(0, "预定成功", "");
		} else {
			return {
				errCode: 500,
				errMsg: '预定失败'
			};
		}
	},
	// 获取预定列表
	async getBooking(event) {
		let {
			data,
			uid
		} = event
	
		let limit = 20
		let skip = 20
		const res = await dbJQL.collection("fangke_addbooking")
			.where(`uid == "${uid}"`)
			.orderBy('create_time', 'desc')
			.limit(limit)
			.skip(skip)
			.get();

		return result(0,"success",res.data)
	},



	// 删除预定
	async deleteBooking(event) {
		if (!event.id) {
			return {
				errCode: 400,
				errMsg: '缺少预定ID'
			};
		}
		let {
			id,
			uid
		} = event

		// 删除数据
		const res = await dbJQL.collection("fangke_addbooking").doc(id).remove();

		if (res.deleted) {
			return {
				errCode: 0,
				errMsg: 'success'
			};
		} else {
			return {
				errCode: 500,
				errMsg: '删除失败'
			};
		}
	},

	_after: function(error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}
}
