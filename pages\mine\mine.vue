<template>
	<view class="user">
		<!-- #ifdef MP-WEIXIN -->
		<custom-nav-bar title="个人中心" :showLeftIcon="false" :showSearch="false" :isTransparent="true"
			:showTitle="true"></custom-nav-bar>
		<!-- #endif -->
		<view class="top">
			<view class="userInfo" @click="goInfo">
				<view class="left">
					<view class="pic">
						<image v-if="isLogin && userInfo.avatar_file&&userInfo.avatar_file.url"
							:src="userInfo.avatar_file.url"></image>
						<image v-else src="../../static/logo.png" mode="aspectFill"></image>
					</view>

					<view v-if="isLogin">
						<view class="info">
							<view class="name">
								<text v-if="isLogin">{{userInfo.nickname||userInfo.username||userInfo.mobile}}</text>
								<text v-else>游客</text>
							</view>
							<view class="level">
								<uni-icons type="medal" size="16" color="#FFD700"></uni-icons>
								<text class="level-text" v-if="isLogin">普通会员</text>
							</view>
							
						</view>
					</view>
					<view v-else>
						<view class="info">
							<view class="name">请登录</view>
						</view>
					</view>

				</view>

				<view class="right">
					<text class="iconfont icon-a-10-you"></text>
				</view>
			</view>
			<view class="bg">
				<image v-if="isLogin && userInfo.avatar_file&&userInfo.avatar_file.url" :src="userInfo.avatar_file.url">
				</image>
				<image v-else src="../../static/logo.png" mode="aspectFill"></image>
			</view>
		</view>

		<view class="container">

			<!-- 账户资产区域 -->
			<view class="assets">
				<view class="asset-item" @click="balance">
					<text class="asset-value">￥1,234.56</text>
					<text class="asset-label">账户余额</text>
				</view>
				<view class="asset-item" @click="toCoupon">
					<text class="asset-value">12</text>
					<text class="asset-label">优惠券</text>
				</view>
				<view class="asset-item">
					<text class="asset-value">888</text>
					<text class="asset-label">积分</text>
				</view>
			</view>

			<!-- 管理功能区域 -->
			<view class="group">
				<view class="list" @click="toInvite">
					<view class="left">
						<view class="icon-wrapper template">
							<uni-icons type="compose" size="20" color="#fff" />
						</view>
						<view class="title">分享管理</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list">
					<view class="left" @click="toAddress">
						<view class="icon-wrapper star">
							<uni-icons type="home" size="20" color="#fff" />
						</view>
						<view class="title">收货地址</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list" @click="toIdentify">
					<view class="left">
						<view class="icon-wrapper house">
							<uni-icons type="star" size="20" color="#fff" />
						</view>
						<view class="title">银行卡管理</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>
			</view>

			<view class="group">
				<view class="list" @click="toCoupon">
					<view class="left">
						<view class="icon-wrapper gift">
							<uni-icons type="gift" size="20" color="#fff" />
						</view>
						<view class="title">优惠券管理</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list" @click="toPoints">
					<view class="left">
						<view class="icon-wrapper link">
							<uni-icons type="paperplane" size="20" color="#fff" />
						</view>
						<view class="title">积分商城</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list" @click="toFeedback">
					<view class="left">
						<view class="icon-wrapper feedback">
							<uni-icons type="chatbubble" size="20" color="#fff" />
						</view>
						<view class="title">意见反馈</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>
			</view>

			<view class="group">
				<view class="list" @click="toHelp">
					<view class="left">
						<view class="icon-wrapper help">
							<uni-icons type="help" size="20" color="#fff" />
						</view>
						<view class="title">帮助中心</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list" @click="toService">
					<view class="left">
						<view class="icon-wrapper service">
							<uni-icons type="headphones" size="20" color="#fff" />
						</view>
						<view class="title">官方客服</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>

				<view class="list" @click="onLogout">
					<view class="left">
						<view class="icon-wrapper settings">
							<uni-icons type="download" size="20" color="#fff" />
						</view>
						<view class="title">退出登录</view>
					</view>
					<view class="right">
						<uni-icons type="right" size="16" color="#c8c9cc" />
					</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		wxlogin,
		pwLogin,
		logout
	} from '@/utils/js/common.js';
	import {
		store2
	} from '@/utils/js/store.js'
	const isLogin = computed(() => store2.hasLogin)
	const userInfo = computed(() => store2.userInfo)
	const dbJQL = uniCloud.databaseForJQL();
	const db = uniCloud.database();
	const dbCmd = db.command
	
	// 定义响应式数据
	const totalObj = ref({
		artNum: 0,
		likeNum: 0,
	});
	const title = ref(["我的信息"]);


	// 生命周期钩子
	onLoad(() => {
		console.log("onload", userInfo.value, isLogin);
	});


	const onLogout = () => {
		uni.showModal({
			title: "是否确认退出？",
			success: (res) => {
				console.log(res);
				if (res.confirm) {
					logout()
				}
			}
		});
	};

	const goInfo = () => {
		if (isLogin.value) {
			uni.navigateTo({
				url: "/unid/uni-id-pages/pages/userinfo/userinfo"
			});
		} else {
			uni.navigateTo({
				url: "/unid/uni-id-pages/pages/login/login-withoutpwd"
			});
		}

	};

	const onFeed = () => {
		console.log("点击意见反馈");
		if (isLogin.value) return;
	};

	const toInvite = () => {
		uni.navigateTo({
			url: "/pagesB/invite/invite"
		});
	};
	
	const balance = () => {
		uni.navigateTo({
			url: '/vip/balance/balance'
		})
	}

	const toIdentify = () => {
		uni.navigateTo({
			url: "/vip/identify/identify"
		});
	};
	
	const toAddress = () => {
		uni.navigateTo({
			url: "/vip/address/address"
		});
	};

	const toCoupon = () => {
		console.log("优惠券管理");
		uni.navigateTo({
		    url: "/vip/coupon/coupon"
		});
	};

	const toPoints = () => {
		console.log("我的积分");
		// uni.navigateTo({
		//     url: "/pagesB/favorites/favorites"
		// });
	};

	const toFeedback = () => {
		console.log("意见反馈");
		// uni.navigateTo({
		//     url: "/pagesB/feedback/feedback"
		// });
	};

	const toHelp = () => {
		console.log("帮助中心");
		// uni.navigateTo({
		//     url: "/pagesB/help/help"
		// });
	};

	const toService = () => {
		console.log("官方客服");
		// uni.navigateTo({
		//     url: "/pagesB/service/service"
		// });
	};

	const toSettings = () => {
		console.log("设置");
		// uni.navigateTo({
		//     url: "/pagesB/settings/settings"
		// });
	};
</script>
<style lang="scss" scoped>
	.user {
		background:
			linear-gradient(to bottom, transparent, #fff 500rpx),
			linear-gradient(to right, #beecd8 20%, #F4E2D8);

		.top {
			padding: 20rpx;
			height: 200rpx;
			background: #bbb;
			position: relative;
			// padding-top: var(--status-bar-height);
			display: flex;
			align-items: center;

			.userInfo {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
				z-index: 10;
				padding: 10rpx;

				.left {
					display: flex;
					font-size: 20rpx;
					align-items: center;

					.pic {
						width: 100rpx;
						height: 100rpx;

						image {
							box-sizing: border-box;
							border-radius: 50%;
							width: 100%;
							height: 100%;
						}
					}

					.info {
						padding-left: 20rpx;

						.name {
							font-size: 40rpx;
						}

						.level {
							display: flex;
							align-items: center;
							background-color: rgba(0, 0, 0, 0.3);
							padding: 4rpx 12rpx;
							border-radius: 16rpx;
							margin-right: 16rpx;
							margin-top: 10rpx;

							.level-text {
								font-size: 12px;
								color: #FFD700;
								margin-left: 4rpx;
							}
						}

					}
				}

				.right {
					margin-right: 10rpx;
					color: #fff;
				}
			}

			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
					transform: scale(2); //放大两倍
					filter: blur(20rpx); //高斯模糊
					opacity: 0.5;
				}
			}
		}

		.container {
			border-radius: 30rpx;
			transform: translateY(-30rpx);
			background: #fff;

			.assets {
				background-color: #ffffff;
				border-radius: 16rpx;
				padding: 30rpx;
				display: flex;
				justify-content: space-around;
			}

			.asset-item {
				display: flex;
				flex-direction: column;
				align-items: center;
			}

			.asset-value {
				font-size: 24px;
				color: #333333;
				font-weight: bold;
				margin-bottom: 8rpx;
			}

			.asset-label {
				font-size: 14px;
				color: #666666;
			}

			.group {
				margin: 20rpx 30rpx;
				background: #fff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

				.list {
					display: flex;
					width: 100%;
					justify-content: space-between;
					padding: 24rpx 30rpx;
					box-sizing: border-box;
					align-items: center;
					color: #333;
					border-bottom: 1px solid #f5f5f5;

					&:last-child {
						border-bottom: none;
					}

					.left {
						align-items: center;
						display: flex;

						.icon-wrapper {
							width: 60rpx;
							height: 60rpx;
							border-radius: 12rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 24rpx;

							&.gift {
								background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
							}

							&.star {
								background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
							}

							&.link {
								background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
							}

							&.feedback {
								background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
							}

							&.help {
								background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
							}

							&.service {
								background: linear-gradient(135deg, #a8e6cf 0%, #dcedc8 100%);
							}

							&.settings {
								background: linear-gradient(135deg, #ffd3a5 0%, #fd9853 100%);
							}

							&.template {
								background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							}

							&.house {
								background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
							}
						}

						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: 500;
						}
					}

					.right {
						display: flex;
						align-items: center;
					}
				}
			}

			.group:last-child {
				border: none;
			}
		}
	}
</style>