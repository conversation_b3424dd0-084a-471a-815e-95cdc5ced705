<template>
	<view class="empty">
		<view style="display: flex;justify-content: center;">
			<view class="pic">
				<image src="../../static/empty.png"></image>
			</view>
		</view>
		<text>暂时没有数据哦，请添加房子</text>
	</view>
</template>

<script setup>
</script>

<style lang="scss" scoped>
	.empty{
		padding: 20rpx;
		display: flex;
		height: 100%;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		.pic{
			display: flex;
			width: 120rpx;
			height: 100rpx;
			image{
				width: 100%;
				height: 100%;
			}
		}
	}
</style>