<template>
  <view class="container">
    <uni-section title="房间基本信息" type="line" class="section">
      <uni-forms ref="roomFormRef" :modelValue="roomFormData" :rules="rules">
        <uni-forms-item label="房间名称" name="name" required>
          <uni-easyinput v-model="roomFormData.name" placeholder="请输入房间名称" />
        </uni-forms-item>

        <uni-forms-item label="面积(m²)" name="area">
          <uni-easyinput v-model="roomFormData.area" type="number" placeholder="请输入房间面积" />
        </uni-forms-item>

        <uni-forms-item label="户型" name="layout">
          <uni-easyinput v-model="roomFormData.layout" placeholder="请输入户型，如：一室一厅" />
        </uni-forms-item>

        <uni-forms-item label="楼层" name="floor">
          <uni-easyinput v-model="roomFormData.floor" type="number" placeholder="请输入楼层" />
        </uni-forms-item>
      </uni-forms>
    </uni-section>

    <uni-section title="租金信息" type="line" class="section">
      <uni-forms ref="rentForm" :modelValue="roomFormData">
        <uni-forms-item label="月租金(元)" name="rent">
          <uni-easyinput v-model="roomFormData.rent" type="number" placeholder="请输入月租金" />
        </uni-forms-item>

        <uni-forms-item label="押金(元)" name="deposit">
          <uni-easyinput v-model="roomFormData.deposit" type="number" placeholder="请输入押金" />
        </uni-forms-item>

        <uni-forms-item label="付租方式" name="payment">
          <uni-data-select v-model="roomFormData.payment" :localdata="paymentOptions" placeholder="请选择付租方式"></uni-data-select>
        </uni-forms-item>

        <uni-forms-item label="交租日期" name="day">
          <uni-easyinput v-model="roomFormData.day" type="number" placeholder="请输入每月交租日期(1-31)" :disabled="isRented" :style="{ opacity: isRented ? 0.6 : 1 }" />
          <view v-if="isRented" class="disabled-tip">
            <text>房间已入住，不可修改交租日期</text>
          </view>
        </uni-forms-item>
      </uni-forms>
    </uni-section>

    <uni-section title="水电信息" type="line" class="section">
      <uni-forms ref="utilityForm" :modelValue="roomFormData">
        <uni-forms-item label="电表读数" name="ele">
          <uni-easyinput v-model="roomFormData.ele" type="digit" placeholder="请输入当前电表读数" />
        </uni-forms-item>

        <uni-forms-item label="水表读数" name="water">
          <uni-easyinput v-model="roomFormData.water" type="digit" placeholder="请输入当前水表读数" />
        </uni-forms-item>
      </uni-forms>
    </uni-section>

    <uni-section title="设备配置" type="line" class="section">
      <view class="facilities-grid">
        <view
          v-for="(item, index) in facilities"
          :key="index"
          class="facility-item"
          :class="{ active: selectedFacilities.includes(item.value) }"
          @click="toggleFacility(item.value)"
        >
          {{ item.name }}
        </view>
      </view>
    </uni-section>

    <uni-section title="房间图片" type="line" class="section">
      <image-picker
        ref="imagePickerRef"
        v-model="roomImages"
        :max-count="9"
        :enable-cover="true"
        :enable-preview="true"
        :enable-delete="true"
        :show-tips="true"
        :tips="imageTips"
        upload-text="添加房间图片"
        grid-type="grid-3"
        cloud-path-prefix="room-images"
        :auto-upload="false"
        @cover-change="onCoverChange"
        @delete="onImageDelete"
        @preview="onImagePreview"
      />
    </uni-section>

    <uni-section title="备注信息" type="line" class="section">
      <uni-forms ref="descForm" :modelValue="roomFormData">
        <uni-forms-item label="房间描述" name="desc">
          <uni-easyinput type="textarea" v-model="roomFormData.desc" placeholder="请输入房间描述信息" :maxlength="200" />
        </uni-forms-item>
      </uni-forms>
    </uni-section>

    <view class="button-group">
      <button class="cancel-btn" :disabled="isSaving" @click="cancel">取消</button>
      <button class="save-btn" type="primary" :disabled="isSaving" :loading="isSaving" @click="saveRoom">
        {{ isEdit ? '保存修改' : '添加房间' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import ImagePicker from '@/components/image-picker/image-picker.vue'
const db = uniCloud.databaseForJQL()

// 响应式数据
const roomFormData = ref({
  name: '',
  area: '',
  layout: '',
  floor: '',
  rent: '',
  deposit: '',
  payment: '',
  day: '',
  ele: '',
  water: '',
  desc: ''
})

const selectedFacilities = ref([])
const roomImages = ref([])
const roomId = ref('')
const isEdit = ref(false)
const isRented = ref(false) // 是否已入住
const isSaving = ref(false) // 是否正在保存
const imagePickerRef = ref(null) // 图片选择器引用
const roomFormRef = ref(null) // 房间表单引用

// 图片提示信息
const imageTips = ref([
  '• 点击图片可预览',
  '• 第一张图片默认为封面，可点击"设为封面"更改',
  '• 最多可上传9张图片'
])

// 付租方式选项
const paymentOptions = ref([
  { value: '押一付一', text: '押一付一' },
  { value: '押一付二', text: '押一付二' },
  { value: '押一付三', text: '押一付三' },
  { value: '押二付一', text: '押二付一' },
  { value: '押三付一', text: '押三付一' },
  { value: '半年付', text: '半年付' },
  { value: '年付', text: '年付' }
])

// 设施设备选项
const facilities = ref([
  { name: '智能锁', value: 1 },
  { name: '床', value: 2 },
  { name: '衣柜', value: 3 },
  { name: '书桌椅', value: 4 },
  { name: '暖气', value: 5 },
  { name: '天然气', value: 6 },
  { name: '宽带', value: 7 },
  { name: '电视', value: 8 },
  { name: '冰箱', value: 9 },
  { name: '洗衣机', value: 10 },
  { name: '空调', value: 11 },
  { name: '热水器', value: 12 },
  { name: '微波炉', value: 13 },
  { name: '油烟机', value: 14 },
  { name: '电磁炉', value: 15 },
  { name: '阳台', value: 16 },
  { name: '可做饭', value: 17 },
  { name: '独立卫生间', value: 18 },
  { name: '沙发', value: 19 },
  { name: '电梯', value: 20 }
])

// 表单验证规则
const rules = ref({
  name: {
    rules: [
      {
        required: true,
        errorMessage: '房间名称不能为空'
      }
    ]
  }
})

// 页面加载时处理参数
onLoad(options => {
  console.log('addRoom onLoad:', options)

  if (options.room_id) {
    roomId.value = options.room_id
    isEdit.value = true
    // 获取房间信息
    getRoomInfo()
  }
})

// 获取房间信息
const getRoomInfo = () => {
  if (!roomId.value) return

  const params = {
    id: roomId.value,
    uid: uni.getStorageSync('uid') || ''
  }

  uni.$lkj.api
    .getRoomInfo(params)
    .then(res => {
      console.log('getRoomInfo success:', res)
      if (res.errCode === 0 && res.data) {
        updateFormData(res.data)
      } else {
        uni.showToast({
          title: res.errMsg || '获取房间信息失败',
          icon: 'none'
        })
      }
    })
    .catch(err => {
      console.error('getRoomInfo error:', err)
      uni.showToast({
        title: '获取房间信息失败',
        icon: 'none'
      })
    })
}

// 更新表单数据
const updateFormData = data => {
  roomFormData.value = {
    name: data.name || '',
    area: data.area ? (data.area / 100).toString() : '', // 面积从分转换为元
    layout: data.layout || '',
    floor: data.floor ? data.floor.toString() : '',
    rent: data.rent ? (data.rent / 100).toString() : '', // 租金从分转换为元
    deposit: data.deposit ? (data.deposit / 100).toString() : '', // 押金从分转换为元
    payment: data.payment || '',
    day: data.day ? data.day.toString() : '',
    ele: data.ele ? (data.ele / 100).toString() : '', // 电表从分转换为元
    water: data.water ? (data.water / 100).toString() : '', // 水表从分转换为元
    desc: data.desc || ''
  }

  // 设置选中的设施
  selectedFacilities.value = data.devices || []

  // 判断是否已入住 (rent_status: 3=已出租, 4=租约过期, 5=租约快到期, 6=占用)
  isRented.value = data.rent_status >= 3

  // 设置房间图片
  if (data.images && Array.isArray(data.images)) {
    roomImages.value = data.images.map((img, index) => ({
      url: img.url || img,
      isCover: img.isCover || index === 0, // 第一张默认为封面
      id: img.id || Date.now() + index
    }))
  } else {
    roomImages.value = []
  }

  console.log('Form data updated:', roomFormData.value)
  console.log('Selected facilities:', selectedFacilities.value)
  console.log('Is rented:', isRented.value)
  console.log('Room images:', roomImages.value)
}

// 切换设施选择
const toggleFacility = value => {
  const index = selectedFacilities.value.indexOf(value)
  if (index > -1) {
    selectedFacilities.value.splice(index, 1)
  } else {
    selectedFacilities.value.push(value)
  }
  console.log('Selected facilities:', selectedFacilities.value)
}

// 保存房间信息
const saveRoom = () => {

  // 防止重复提交
  if (isSaving.value) {
    return
  }

  // 1. 基础表单验证
  if (!validateForm()) {
    return
  }

  // 2. 表单组件验证
  if (roomFormRef.value) {
    roomFormRef.value
      .validate()
      .then(() => {
        // 验证通过，继续保存
        performSave()
      })
      .catch(error => {
        console.error('表单验证失败:', error)
        uni.showToast({
          title: '请检查表单输入',
          icon: 'none'
        })
      })
  } else {
    // 没有表单引用，直接保存
    performSave()
  }
}

// 执行保存操作
const performSave = () => {
  try {
    // 设置保存状态
    isSaving.value = true

    // 准备提交数据
    const submitData = prepareSubmitData()
    console.log('提交数据:', submitData)

    // 显示加载提示
    uni.showLoading({
      title: isEdit.value ? '保存中...' : '添加中...',
      mask: true
    })

    // 调用API保存数据
    const apiMethod = isEdit.value ? 'updateRoomModel' : 'addRoomModel'
    console.log('调用API方法:', apiMethod)

    // 检查API是否存在
    if (
      !uni.$lkj ||
      !uni.$lkj.api ||
      typeof uni.$lkj.api[apiMethod] !== 'function'
    ) {
      throw new Error(`API方法 ${apiMethod} 不存在或不是函数`)
    }

    uni.$lkj.api[apiMethod](submitData)
      .then(result => {
        uni.hideLoading()
        isSaving.value = false

        console.log('API返回结果:', result)

        if (result && result.errCode === 0) {
          uni.showToast({
            title: isEdit.value ? '修改成功' : '添加成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          const errorMsg = result?.errMsg || result?.message || '操作失败'
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          })
        }
      })
      .catch(error => {
        uni.hideLoading()
        isSaving.value = false

        console.error('API调用失败:', error)
        uni.showToast({
          title: '网络请求失败，请重试',
          icon: 'none'
        })
      })
  } catch (error) {
    uni.hideLoading()
    isSaving.value = false

    console.error('保存过程出错:', error)
    uni.showToast({
      title: error.message || '保存失败，请重试',
      icon: 'none'
    })
  }
}

// 基础表单验证
const validateForm = () => {
  // 验证房间名称
  if (!roomFormData.value.name || !roomFormData.value.name.trim()) {
    uni.showToast({
      title: '请输入房间名称',
      icon: 'none'
    })
    return false
  }

  // 验证房间名称长度
  if (roomFormData.value.name.trim().length > 50) {
    uni.showToast({
      title: '房间名称不能超过50个字符',
      icon: 'none'
    })
    return false
  }

  // 验证面积
  if (roomFormData.value.area && parseFloat(roomFormData.value.area) <= 0) {
    uni.showToast({
      title: '房间面积必须大于0',
      icon: 'none'
    })
    return false
  }

  // 验证楼层
  if (roomFormData.value.floor && parseInt(roomFormData.value.floor) <= 0) {
    uni.showToast({
      title: '楼层必须大于0',
      icon: 'none'
    })
    return false
  }

  // 验证租金
  if (roomFormData.value.rent && parseFloat(roomFormData.value.rent) < 0) {
    uni.showToast({
      title: '租金不能为负数',
      icon: 'none'
    })
    return false
  }

  // 验证押金
  if (
    roomFormData.value.deposit &&
    parseFloat(roomFormData.value.deposit) < 0
  ) {
    uni.showToast({
      title: '押金不能为负数',
      icon: 'none'
    })
    return false
  }

  // 验证交租日期
  if (roomFormData.value.day) {
    const day = parseInt(roomFormData.value.day)
    if (day < 1 || day > 31) {
      uni.showToast({
        title: '交租日期必须在1-31之间',
        icon: 'none'
      })
      return false
    }
  }

  // 验证描述长度
  if (roomFormData.value.desc && roomFormData.value.desc.length > 200) {
    uni.showToast({
      title: '房间描述不能超过200个字符',
      icon: 'none'
    })
    return false
  }

  return true
}

// 准备提交数据
const prepareSubmitData = () => {
  const submitData = {
    name: roomFormData.value.name.trim(),
    area: roomFormData.value.area
      ? Math.round(parseFloat(roomFormData.value.area) * 100)
      : 0, // 转换为分
    layout: roomFormData.value.layout || '',
    floor: roomFormData.value.floor ? parseInt(roomFormData.value.floor) : 1,
    rent: roomFormData.value.rent
      ? Math.round(parseFloat(roomFormData.value.rent) * 100)
      : 0, // 转换为分
    deposit: roomFormData.value.deposit
      ? Math.round(parseFloat(roomFormData.value.deposit) * 100)
      : 0, // 转换为分
    payment: roomFormData.value.payment || '',
    day: roomFormData.value.day ? parseInt(roomFormData.value.day) : null,
    ele: roomFormData.value.ele
      ? Math.round(parseFloat(roomFormData.value.ele) * 100)
      : 0, // 转换为分
    water: roomFormData.value.water
      ? Math.round(parseFloat(roomFormData.value.water) * 100)
      : 0, // 转换为分
    desc: roomFormData.value.desc || '',
    devices: selectedFacilities.value || [],
    images: roomImages.value || [],
    uid: uni.getStorageSync('uid') || '' // 添加用户ID
  }

  if (isEdit.value) {
    submitData.id = roomId.value
  }

  return submitData
}

// 图片组件事件处理
const onCoverChange = (image, index) => {
  console.log('封面已更改:', image, index)
  uni.showToast({
    title: '封面设置成功',
    icon: 'success'
  })
}

const onImageDelete = (image, index) => {
  console.log('图片已删除:', image, index)
}

const onImagePreview = (image, index) => {
  console.log('预览图片:', image, index)
}

// 检查是否有未保存的修改
const hasUnsavedChanges = () => {
  // 检查表单数据是否有修改
  const hasFormChanges =
    roomFormData.value.name.trim() !== '' ||
    roomFormData.value.area !== '' ||
    roomFormData.value.layout !== '' ||
    roomFormData.value.floor !== '' ||
    roomFormData.value.rent !== '' ||
    roomFormData.value.deposit !== '' ||
    roomFormData.value.payment !== '' ||
    roomFormData.value.day !== '' ||
    roomFormData.value.ele !== '' ||
    roomFormData.value.water !== '' ||
    roomFormData.value.desc !== ''

  // 检查设施是否有选择
  const hasFacilityChanges = selectedFacilities.value.length > 0

  // 检查图片是否有上传
  const hasImageChanges = roomImages.value.length > 0

  return hasFormChanges || hasFacilityChanges || hasImageChanges
}

// 取消操作
const cancel = () => {
  // 如果是编辑模式或者有未保存的修改，显示确认对话框
  if (isEdit.value || hasUnsavedChanges()) {
    uni.showModal({
      title: '确认取消',
      content: isEdit.value
        ? '确定要取消修改吗？未保存的修改将丢失'
        : '确定要取消添加吗？已填写的信息将丢失',
      confirmText: '确定取消',
      cancelText: '继续编辑',
      confirmColor: '#ff4757',
      success: res => {
        if (res.confirm) {
          handleCancel()
        }
        // 如果点击"继续编辑"，什么都不做
      }
    })
  } else {
    // 没有修改直接返回
    handleCancel()
  }
}

// 处理取消操作
const handleCancel = () => {
  // 清理可能的定时器
  clearTimeout()

  // 返回上一页
  uni.navigateBack({
    delta: 1,
    success: () => {
      console.log('取消操作，已返回上一页')
    },
    fail: error => {
      console.error('返回上一页失败:', error)
      // 如果返回失败，尝试跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  })
}

// 页面卸载时的清理工作
const cleanupPage = () => {
  // 清理图片选择器
  if (imagePickerRef.value) {
    imagePickerRef.value.clearImages()
  }

  // 清理表单数据
  roomFormData.value = {
    name: '',
    area: '',
    layout: '',
    floor: '',
    rent: '',
    deposit: '',
    payment: '',
    day: '',
    ele: '',
    water: '',
    desc: ''
  }

  selectedFacilities.value = []
  roomImages.value = []
}

// 注册页面卸载钩子
onUnload(() => {
  cleanupPage()
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.section {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 20rpx;
}

.facility-item {
  height: 76rpx;
  line-height: 76rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
  cursor: pointer;
}

.facility-item:active {
  transform: scale(0.95);
}

.facility-item.active {
  background-color: #e8f5e8;
  color: #07c160;
  border-color: #07c160;
  font-weight: 500;
}

/* 图片组件样式已移至 image-picker 组件内部 */

/* 禁用状态样式 */
.disabled-tip {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6rpx;
}

.disabled-tip text {
  font-size: 24rpx;
  color: #856404;
}

.button-group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.cancel-btn,
.save-btn {
  flex: 1;
  height: 80rpx !important;
  line-height: 80rpx !important;
  border-radius: 12rpx !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  border: none !important;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f8f9fa !important;
  color: #666 !important;
  border: 1px solid #e8e8e8 !important;
}

.cancel-btn:active {
  background-color: #e9ecef !important;
  transform: scale(0.98);
}

.save-btn {
  background-color: #07c160 !important;
  color: #fff !important;
}

.save-btn:active {
  background-color: #06a84e !important;
  transform: scale(0.98);
}

/* 按钮禁用状态 */
.cancel-btn:disabled {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
  border-color: #e8e8e8 !important;
  cursor: not-allowed;
}

.save-btn:disabled {
  background-color: #a0d468 !important;
  color: rgba(255, 255, 255, 0.7) !important;
  cursor: not-allowed;
}

.cancel-btn:disabled:active,
.save-btn:disabled:active {
  transform: none !important;
}

/* 表单样式优化 */
:deep(.uni-forms-item) {
  margin-bottom: 24rpx;
}

:deep(.uni-forms-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.uni-easyinput__content) {
  border-radius: 8rpx;
  border-color: #e8e8e8;
}

:deep(.uni-easyinput__content-input) {
  font-size: 28rpx;
}

:deep(.uni-data-select) {
  border-radius: 8rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .facilities-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 600rpx) {
  .facilities-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
