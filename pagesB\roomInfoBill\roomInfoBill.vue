<template>
	<view class="container">
		<!-- 顶部待收金额区域 -->
		<view class="header-section">
			<view :class="['header-bg',billInfo.status>0?'':'payed'] ">
				<view class="nav-bar" :style="{height:getTitleBarHeight+'px'}">
					<uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
					<text class="nav-title">账单详情</text>
					<uni-icons type="more-filled" size="20" color="#fff"></uni-icons>
				</view>
				<view class="amount-display">
					<text class="amount-value">{{billInfo.status > 0?formatAmount(billInfo.sum?(billInfo.sum - billInfo.money):0):formatAmount(billInfo.money)}}</text>
					<text class="amount-label">{{billInfo.status > 0?"待收(元)":"已收(元)"}}</text>
				</view>
				<view class="bill-info">
					<text class="bill-period">{{billInfo.name}}</text>
					<view class="bill-tags">
						<text class="status-text over" v-if="billInfo.status == 2">
							逾期{{billInfo.overdueDays}}天
						</text>
						<text class="status-text pay" v-else-if="billInfo.status == 0">
							已支付
						</text>
						<text class="status-text overdue" v-else>
							待支付
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 房间信息 -->
		<view class="section room-info">
			<view class="room-header">
				<text class="room-name">{{roomInfo.name}}</text>
				<uni-icons type="phone" size="20" color="#00C8B3"></uni-icons>
			</view>
			<text class="tenant-name">{{roomInfo.tenantName}}-{{roomInfo.tenantPhone}}</text>
		</view>

		<!-- 账单基本信息 -->
		<view class="section">
			<view class="info-row">
				<text class="info-label">账单周期</text>
				<text class="info-value">{{billInfo.time}}</text>
			</view>
			<view class="info-row">
				<text class="info-label">应收总额</text>
				<text class="info-value">{{formatAmount(billInfo.sum)}}元</text>
			</view>
			<view class="info-row">
				<text class="info-label">已收总额</text>
				<text class="info-value">{{formatAmount(billInfo.money)}}元</text>
			</view>
			<view class="info-row">
				<text class="info-label">应收款日</text>
				<text class="info-value">{{billInfo.day}}</text>
			</view>
		</view>

		<!-- 费用明细 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">费用明细</text>
				<text class="status-text" v-if="status >0">待收</text>
			</view>

			<uni-collapse>
				<uni-collapse-item :title="fee.title" v-for="(fee, index) in unPayfeeList"
					:key="index" :rigth-title="formatAmount(fee.value?fee.value/100:0)" :show-arrow="!fee.is_month">
					<view class="fee-item">
						<view class="fee-details">
							<text class="fee-detail">{{fee.desc}}</text>
							<text class="fee-detail-amount" v-if="fee.late_fee">¥{{formatAmount(fee.late_fee? fee.late_fee/100:0)}}</text>
						</view>
						<view class="fee-details">
							<text class="fee-detail">应收日期</text>
							<text class="fee-detail-date">{{fee.day}}</text>
						</view>
					</view>
				</uni-collapse-item>

			</uni-collapse>


			<view v-if="payfeeList.length > 0">
				<view class="section-header">
					<text class="section-title"></text>
					<view class="status-text pay">已收</view>
				</view>

				<uni-collapse>
					<uni-collapse-item :title="fee.title" v-for="(fee, index) in payfeeList"
						:key="index" :rigth-title="formatAmount(fee.value?fee.value/100:0)" :show-arrow="!fee.is_month">
						<view class="fee-item">
							<view class="fee-details">
								<text class="fee-detail">{{fee.desc}}</text>
								<text class="fee-detail-amount"  v-if="fee.late_fee">¥{{formatAmount(fee.late_fee? fee.late_fee/100:0)}}</text>
							</view>
							<view class="fee-details">
								<text class="fee-detail">应收日期</text>
								<text class="fee-detail-date">{{fee.day}}</text>
							</view>
						</view>
					</uni-collapse-item>

				</uni-collapse>
			</view>
		</view>

		<!-- 交易明细 -->
		<view class="section">
			<view class="section-title">交易明细</view>
			<view class="transaction-item" v-for="(transaction, index) in transactionList" :key="index">
				<view class="transaction-info">
					<text class="transaction-operator">经办人：{{transaction.handler}}</text>
					<text class="transaction-method">{{transaction.paymentMethod}}</text>
				</view>
				<view class="transaction-details">
					<text class="transaction-time">{{transaction.paymentDate}}</text>
					<text class="transaction-amount">¥{{formatAmount(transaction.amount/100 || 0)}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="footer" v-if="status > 0">
			<button class="footer-btn secondary" @click="showOverdueDialog">{{status==1?"发送账单":"逾期催缴"}}</button>
			<button class="footer-btn primary" @click="showCollectDialog">收款</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		getNavBarHeight
	} from "../utils/system.js"
	import { dayjs } from '../../utils/dayjs.min.js'
	const getTitleBarHeight = computed(() => getNavBarHeight())
	const bill_id = ref("")
	const room_id = ref("")
	const db = uniCloud.databaseForJQL()
	// 账单信息
	const billInfo = ref({	})
	const status = ref(0)
	// 房间信息
	const roomInfo = ref({
		name: '公寓-302',
		tenantName: '灵敏',
		tenantPhone: '13265656544'
	})

	// 未收费用明细
	const unPayfeeList = ref([])

	const payfeeList = ref([])

	// 交易明细
	const transactionList = ref([{
		user: '13632244771',
		method: '微信转账',
		time: '2025-06-18 11:55:46',
		amount: 290.33
	}])

	// 格式化金额
	const formatAmount = (amount) => {
		console.log("formatAmount",amount);
		return amount?amount.toFixed(2):0
	}

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 显示收款对话框
	const showCollectDialog = () => {
		let data = []
		
		unPayfeeList.value.forEach(item =>{
			data.push({
				name:item.text,
				value:item.value?(item.value/100):0
			})
		})
		uni.navigateTo({
			url:"/pagesB/billPayment/billPayment?bill_id="+bill_id.value+"&data="+encodeURIComponent(JSON.stringify(data))+"&name="+roomInfo.value.tenantName
		})
	}

	// 显示逾期催缴对话框
	const showOverdueDialog = () => {
		uni.showToast({
			title: '逾期催缴功能',
			icon: 'none'
		})
	}

	onLoad((options) => {
		// 根据传入参数加载账单数据
		console.log('账单页面参数:', options)
		if(options.id){
			bill_id.value = options.id
			getBillInfo()
		}
	})
	
	const getBillInfo = async() =>{
		let tenantCol = db.collection("fangke_tenants").field("name,_id,phone").getTemp()
		
		const res = await db.collection("fangke_room_bill", tenantCol).where(`_id == "${bill_id.value}"`).get().catch(err =>{
			console.log("请求失败",err);
		})
		if(res.errCode == 0){
			if(res.data.length>0){
				let info =  res.data[0]
				console.log("info",info);
				status.value = info.status
				roomInfo.value.name = info.room_name
				roomInfo.value.tenantName = info.tenant[0].name
				roomInfo.value.tenantPhone = info.tenant[0].phone
				if(info.startTime === ""){
					billInfo.value.time = info.day
				}else{
					billInfo.value.time = info.startTime +" ~ "+info.endTime
				}
				billInfo.value.sum = info.sum?info.sum/100 : 0
				billInfo.value.money = info.money?info.money/100 : 0
				billInfo.value.day = info.day
				billInfo.value.overdueDays= info.dueDay || 0
				billInfo.value.status = info.status
				billInfo.value.name = info.name
				info.detail.forEach(item =>{
					item.title = item.unit?item.text+"("+(item.unit/100)+item.unit_name+")":item.text+"(元)"
					console.log("info.detail",item,item.status);
					if(item.status == 0){
						payfeeList.value.push(item)
					}else{
						unPayfeeList.value.push(item)
					}
				})
				
			}
			
		}
		let payDetail = await db.collection("fangke_room_account").where(`bill == "${bill_id.value}"`).field("_id,paymentMethod,handler,amount,paymentDate").get().then( res =>{
			console.log("获取明细",res);
			if(res.errCode == 0){
				transactionList.value = res.data.length?res.data:[]
			}
		})
		
		console.log("订单信息", res);
		
		console.log("billInfo",billInfo.value)
		console.log("payfeeList",payfeeList.value);
		console.log("unPayfeeList",unPayfeeList.value);
		console.log("transactionList",transactionList.value);
	}
</script>

<style>
	page {
		height: 100%;
		background-color: #F5F5F5;
	}

	.container {
		min-height: 100vh;
		background-color: #F5F5F5;
	}

	/* 头部区域 */
	.header-section {
		position: relative;
		height: 400rpx;
	}

	.header-bg {
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
		height: 100%;
		padding: 0 30rpx;
		position: relative;
	}
	
	.header-bg.payed{
		background: #00B888;
	}

	.nav-bar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		padding-top: 44rpx;
	}

	.nav-title {
		color: #fff;
		font-size: 18px;
		font-weight: 500;
	}

	.amount-display {
		text-align: center;
		margin-top: 20rpx;
	}

	.amount-value {
		display: block;
		color: #fff;
		font-size: 48px;
		font-weight: bold;
		line-height: 1;
	}

	.amount-label {
		color: rgba(255, 255, 255, 0.8);
		font-size: 14px;
		margin-top: 8rpx;
	}

	.bill-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;
	}

	.bill-period {
		color: #fff;
		font-size: 35rpx;
		font-weight: bold;
	}

	.bill-tags {
		display: flex;
		gap: 16rpx;
	}

	.tag-item {
		background-color: rgba(255, 255, 255, 0.2);
		color: #fff;
		font-size: 12px;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}


	/* 房间信息 */
	.room-info {
		background-color: #fff;
		margin-top: -40rpx;
		border-radius: 16rpx 16rpx 0 0;
		padding: 30rpx;
		position: relative;
		z-index: 1;
	}

	.room-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.room-name {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.tenant-name {
		font-size: 14px;
		color: #666;
	}

	/* 通用section样式 */
	.section {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 30rpx;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 10rpx 0;
	}

	.section-title {
		font-size: 16px;
		font-weight: 500;
		color: #333;
	}

	.collect-btn {
		background-color: #FF6B6B;
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
	}

	.collect-text {
		color: #fff;
		font-size: 12px;
	}

	/* 信息行 */
	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
		padding-bottom: 24rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.info-row:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}

	.info-label {
		font-size: 14px;
		color: #666;
	}

	.info-value {
		font-size: 14px;
		color: #333;
	}

	/* 费用明细 */
	.fee-item {
		position: relative;
		padding: 10rpx 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.fee-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}

	.fee-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.fee-name {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	.fee-unit {
		font-size: 12px;
		color: #999;
		font-weight: normal;
	}

	.fee-amount {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	.fee-arrow {
		margin-left: 8rpx;
		color: #999;
	}

	.fee-details {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.fee-detail {
		font-size: 12px;
		color: #999;
	}

	.fee-detail-amount {
		font-size: 12px;
		color: #333;
	}

	.fee-detail-date {
		font-size: 12px;
		color: #333;
	}

	.fee-status {
		position: absolute;
		top: 0;
		right: 0;
	}

	.status-text {
		background-color: orange;
		color: #fff;
		font-size: 12px;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	.status-text.pay {
		background-color: #00C8B3;
	}
	
	.status-text.over {
		background-color: #FF6B6B;
	}

	/* 交易明细 */
	.transaction-item {
		margin-bottom: 30rpx;
		padding-bottom: 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.transaction-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}

	.transaction-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.transaction-operator {
		font-size: 14px;
		color: #333;
	}

	.transaction-method {
		font-size: 14px;
		color: #00C8B3;
	}

	.transaction-details {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.transaction-time {
		font-size: 12px;
		color: #999;
	}

	.transaction-amount {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	/* 底部按钮 */
	.footer {
		display: flex;
		gap: 20rpx;
		padding: 30rpx;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}

	.footer-btn {
		flex: 1;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 16px;
		border: none;
	}

	.footer-btn.secondary {
		background-color: #F5F5F5;
		color: #333;
	}

	.footer-btn.primary {
		background-color: #4A90E2;
		color: #fff;
	}

	/* 为底部按钮预留空间 */
	.container {
		padding-bottom: 148rpx;
	}
</style>