<template>
	<view class="door-lock-page">
		

		<!-- 筛选选项卡 -->
		<view class="filter-tabs">
			<view 
				class="tab-item" 
				:class="{ active: selectedTab === item.value }"
				v-for="(item, index) in tabOptions" 
				:key="index"
				@click="selectTab(item.value)"
			>
				<text class="tab-text">{{ item.label }}</text>
			</view>
			<view class="tab-actions">
				<uni-icons type="upload" size="16" color="#999" />
				<uni-icons type="more" size="16" color="#999" />
			</view>
		</view>

		<!-- 门锁设备网格 -->
		<view class="device-grid">
			<view 
				class="device-item" 
				v-for="(device, index) in filteredDevices" 
				:key="index"
				@click="controlDevice(device)"
			>
				<!-- 左侧：设备图标和名称 -->
				<view class="device-left">
					<view class="device-icon">
						<view class="icon-wrapper" :class="{ 'has-notification': device.hasNotification }">
							<image 
								src="https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/button.png" 
								class="device-icon-img"
								mode="aspectFit"
							/>
							<view v-if="device.hasNotification" class="notification-dot"></view>
						</view>
					</view>
					<view class="device-name">{{ device.name }}</view>
				</view>

				<!-- 右侧：电源开关和连接状态 -->
				<view class="device-right">
					<!-- 电源按钮 -->
					<view class="power-button" @click.stop="togglePower(device)">
						<image 
							:src="device.isOnline ? 'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/switch_on.png' : 'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/switch_off.png'"
							class="power-switch-img"
							mode="aspectFit"
						/>
					</view>

					<!-- 连接状态 -->
					<view class="device-status">
						<image 
							:src="device.isOnline ? 'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/wifi.png' : 'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/no_wifi.png'"
							class="wifi-icon"
							mode="aspectFit"
						/>
						<uni-icons  :type="device.temperature?'person':'staff'" class="temperature" color="#CBE5F3"></uni-icons> 
					</view>
				</view>
			</view>
		</view>

		<!-- 添加设备浮动按钮 -->
		<view class="floating-add-btn" @click="showAddDevice">
			<uni-icons type="plus" size="24" color="#fff" />
		</view>

		<!-- 添加设备弹窗 -->
		<uni-popup ref="addDevicePopup" type="bottom" background-color="#fff">
			<view class="add-device-popup">
				<view class="popup-header">
					<text class="popup-title">添加设备</text>
					<uni-icons type="close" size="20" color="#666" @click="closeAddDevice" />
				</view>
				<view class="device-types">
					<view class="device-type" @click="addDeviceType('smart_lock')">
						<view class="type-icon">
							<uni-icons type="locked" size="24" color="#4A90E2" />
						</view>
						<text class="type-name">智能门锁</text>
					</view>
					<!-- <view class="device-type" @click="addDeviceType('camera')">
						<view class="type-icon">
							<uni-icons type="eye" size="24" color="#FF6B6B" />
						</view>
						<text class="type-name">摄像头</text>
					</view>
					<view class="device-type" @click="addDeviceType('sensor')">
						<view class="type-icon">
							<uni-icons type="pulse" size="24" color="#4ECDC4" />
						</view>
						<text class="type-name">传感器</text>
					</view> -->
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'

	// 响应式数据
	const selectedTab = ref('all')
	const addDevicePopup = ref(null)

	// 筛选选项
	const tabOptions = [
		{ label: '全部设备', value: 'all' },
		{ label: '大门', value: 'door' },
		{ label: '客厅', value: 'living_room' },
		{ label: '卧室', value: 'bedroom' },
		{ label: '其他', value: 'other' }
	]

	// 设备数据
	const devices = ref([
		{
			id: 1,
			name: '东3栋3号',
			type: 'smart_lock',
			room: 'bedroom',
			isOnline: true,
			hasNotification: true,
			temperature: null
		},
		{
			id: 2,
			name: '东10',
			type: 'smart_lock',
			room: 'living_room',
			isOnline: true,
			hasNotification: true,
			temperature: null
		},
		{
			id: 3,
			name: '南街十巷',
			type: 'sensor',
			room: 'living_room',
			isOnline: true,
			hasNotification: false,
			temperature: null
		},
		{
			id: 4,
			name: '北36',
			type: 'smart_lock',
			room: 'bedroom',
			isOnline: true,
			hasNotification: false,
			temperature: null
		},
		{
			id: 5,
			name: '北38',
			type: 'sensor',
			room: 'other',
			isOnline: false,
			hasNotification: false,
			temperature: null
		},
		{
			id: 6,
			name: '东一街8号',
			type: 'smart_lock',
			room: 'bedroom',
			isOnline: true,
			hasNotification: true,
			temperature: null
		},
		{
			id: 7,
			name: '东3栋15号',
			type: 'camera',
			room: 'living_room',
			isOnline: false,
			hasNotification: true,
			temperature: null
		},
		{
			id: 8,
			name: '中街33号',
			type: 'smart_lock',
			room: 'other',
			isOnline: true,
			hasNotification: false,
			temperature: 24
		},
		{
			id: 9,
			name: '南约后中3栋12',
			type: 'sensor',
			room: 'other',
			isOnline: true,
			hasNotification: false,
			temperature: null
		}
	])

	// 筛选后的设备列表
	const filteredDevices = computed(() => {
		if (selectedTab.value === 'all') {
			return devices.value
		}
		return devices.value.filter(device => device.room === selectedTab.value)
	})

	// 生命周期
	onLoad(() => {
		console.log('门锁页面加载')
	})

	// 方法
	const goBack = () => {
		uni.navigateBack()
	}

	const selectTab = (tabValue) => {
		selectedTab.value = tabValue
	}

	const controlDevice = (device) => {
		console.log('控制设备:', device)
		uni.showToast({
			title: `控制${device.name}`,
			icon: 'none'
		})
	}

	const togglePower = (device) => {
		device.isOnline = !device.isOnline
		uni.showToast({
			title: device.isOnline ? '设备已开启' : '设备已关闭',
			icon: 'none'
		})
	}

	const showAddDevice = () => {
		addDevicePopup.value?.open()
	}

	const closeAddDevice = () => {
		addDevicePopup.value?.close()
	}

	const addDeviceType = (deviceType) => {
		console.log('添加设备类型:', deviceType)
		uni.showToast({
			title: '添加设备功能开发中',
			icon: 'none'
		})
		closeAddDevice()
	}
</script>

<style lang="scss" scoped>
	.door-lock-page {
		min-height: 100vh;
		background: linear-gradient(135deg, #e8f4fd 0%, #d6ebf7 30%, #c4e2f1 100%);
		padding-bottom: 100rpx;
	}

	.custom-navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background: rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(10rpx);
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);

		.navbar-left {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.navbar-title {
				font-size: 36rpx;
				font-weight: 500;
				color: #4a5568;
			}
		}

		.navbar-right {
			display: flex;
			align-items: center;
			gap: 20rpx;
		}
	}

	.filter-tabs {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background: rgba(255, 255, 255, 0.6);
		backdrop-filter: blur(5rpx);

		.tab-item {
			margin-right: 40rpx;
			padding: 8rpx 0;
			border-bottom: 3rpx solid transparent;

			&.active {
				border-bottom-color: #5a9fd4;

				.tab-text {
					color: #2d3748;
					font-weight: 500;
				}
			}

			.tab-text {
				font-size: 28rpx;
				color: #718096;
			}
		}

		.tab-actions {
			margin-left: auto;
			display: flex;
			align-items: center;
			gap: 20rpx;
		}
	}

	.device-grid {
		padding: 30rpx;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
	}

	.device-item {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 16rpx;
		padding: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		position: relative;
		box-shadow: 0 2rpx 12rpx rgba(90, 159, 212, 0.15);
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		min-height: 120rpx;

		.device-left {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 24rpx;
			width: 120rpx;
			justify-content: center;

			.device-icon {
				position: relative;

				.icon-wrapper {
					width: 50rpx;
					height: 50rpx;
					background: transparent;
					border-radius: 12rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.device-icon-img {
						width: 100%;
						height: 100%;
					}

					&.has-notification {
						.notification-dot {
							position: absolute;
							top: -4rpx;
							right: -4rpx;
							width: 16rpx;
							height: 16rpx;
							background: #f56565;
							border-radius: 50%;
							border: 2rpx solid #fff;
						}
					}
				}
			}

			.device-name {
				font-size: 25rpx;
				color: #2d3748;
				font-weight: 500;
				text-align: left;
			}
		}

		.device-right {
			display: flex;
			align-items: center;
			gap: 20rpx;
			flex-direction: column;
			flex: 1;
			justify-content: center;
			.power-button {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.power-switch-img {
					width: 100%;
					height: 100%;
				}
			}

			.device-status {
				display: flex;
				align-items: center;
				gap: 8rpx;
				justify-content: space-between;

				.wifi-icon {
					width: 24rpx;
					height: 24rpx;
				}

				.temperature {
					font-size: 20rpx;
					color: #718096;
				}
			}
		}
	}

	.floating-add-btn {
		position: fixed;
		bottom: 60rpx;
		right: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #5a9fd4 0%, #4a8bc2 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 6rpx 20rpx rgba(90, 159, 212, 0.3);
		z-index: 100;
	}

	.add-device-popup {
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding: 30rpx;

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2d3748;
			}
		}

		.device-types {
			display: flex;
			gap: 30rpx;

			.device-type {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 20rpx;
				background: #f7fafc;
				border-radius: 12rpx;
				border: 1rpx solid #e2e8f0;

				.type-icon {
					width: 60rpx;
					height: 60rpx;
					background: #fff;
					border-radius: 12rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 16rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				}

				.type-name {
					font-size: 24rpx;
					color: #4a5568;
				}
			}
		}
	}
</style>
