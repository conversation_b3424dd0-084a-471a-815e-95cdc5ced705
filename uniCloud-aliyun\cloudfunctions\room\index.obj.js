const {
	result
} = require('result');
const db = uniCloud.database()
const dbCmd = db.command
let dbJQL;
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function() { // 通用预处理器
		// this.params = this.getParams()[0]
		// dbJQL = uniCloud.databaseForJQL()
		this.startTime = Date.now();
		this.params = this.getHttpInfo()
		console.log("httpMethod", this.params.httpMethod);
		console.log("httpInfo", this.params);
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		let info = this.getClientInfo()
		console.log("this.getClientInfo", info);
		if (this.params.httpMethod == "POST") {
			//post请求
			// queryStringParameters: { uid: 'ddaadw' },	//参数在form-data时
			let body = this.getHttpInfo().body;
			if (!body) throw result(400, "required");
			this.params = JSON.parse(this.getHttpInfo().body)
		} else {
			//get请求
			this.params = this.getParams()[0]
		}
	},

	async get() {
		console.log("get", this.params);
		let {
			uid
		} = this.params
		let list = []
		let building_list = []
		let building_list2 = []
		dbJQL.setUser({ //设置用户状态
			uid: uid,
			role: [],
			permission: []
		})
		let owner = await dbJQL.collection("fangke_building").where(`uid == "${uid}"`).field('_id').get().then(
			res => {
				console.log("获取楼房", res.data);
				return res.data
			}) //获取自己楼房
		let owner_list = []
		owner.forEach(item => {
			owner_list.push(item._id)
		})
		console.log("building", owner);
		let building = dbJQL.collection("fangke_building").where(`uid == "${uid}"`).getTemp()
		let rooms = dbJQL.collection("fangke_room").where(`building_id in ['${owner_list.join("','")}']`).field(
				"_id,rent_status,room_status,floor,rent,name,building_id,arrears,day,layout,area,book,payment,idle_num,arrears_num,overday_num")
			.getTemp()
		let res = await dbJQL.collection(building, rooms).field("_id,number,floor,type").get()
		console.log("自己楼房", res.data);
		building_list = res.data
		//获取管理的楼房
		let admins = await dbJQL.collection("fangke_building_admins").where(`uid == "${uid}"`).field(
			"building_id").get()
		admins.data.forEach((item, index) => {
			building_list2.push(item.building_id)
		})
		if (building_list2.length > 0) {
			let building2 = await dbJQL.collection("fangke_building").where(
				`_id in ['${building_list2.join("','")}']`).getTemp() //筛选数组里有的楼房
			let rooms2 = dbJQL.collection("fangke_room").where(`building_id in ['${owner_list.join("','")}']`)
				.field(
					"_id,rent_status,room_status,floor,rent,name,building_id,arrears,day,layout,area,book,payment,,idle_num,arrears_num,overday_num"
					).getTemp()
			console.log("building2", building2);
			let res2 = await dbJQL.collection(building2, rooms2).field("_id,number,floor,type").get()
			console.log("管理的楼房", res2.data);
			building_list2 = res2.data
		}
		list = building_list.concat(building_list2);
		let data = {
			building_list: list,
		}
		console.log("res3", data);
		return result(0, "success", data)

	},


	_after: function(error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}

}