<template>
	<view class="page">
		<!-- 顶部筛选区域 -->
		<view class="filter-section">
			<view class="filter-item" @click="showDeviceFilter">
				<text class="filter-text">{{ selectedDevice }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showRoomFilter">
				<text class="filter-text">{{ selectedRoom }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showStatusFilter">
				<text class="filter-text">{{ selectedStatus }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
		</view>

		<!-- 表头 -->
		<view class="table-header">
			<text class="header-cell device-name">设备名</text>
			<text class="header-cell status-time">状态/时间</text>
			<text class="header-cell last-reading">上次读数</text>
			<text class="header-cell current-reading">最近读数</text>
		</view>

		<!-- 房间列表 -->
		<view class="room-list">
			<view v-for="(room, roomIndex) in filteredRooms" :key="room._id" class="room-section">
				<!-- 房间号标题 -->
				<view class="room-title">{{ room.name }}</view>
				
				<!-- 水电表列表 -->
				<view v-for="(device, deviceIndex) in getDeviceList(room)" :key="`${room._id}-${device.type}`" class="device-row">
					<view class="device-info">
						<text class="device-name">{{ getDeviceName(device.type) }}</text>
						<view class="status-info">
							<text class="status" :class="getStatusClass(device)">
								{{ getStatusText(device) }}
							</text>
							<text class="record-time">({{ formatDate(device.record_date) }})</text>
						</view>
					</view>
					<view class="reading-input">
						<input 
							class="input-field" 
							:class="{ 'input-disabled': device.is_submit }"
							type="digit" 
							inputmode="decimal"
							:value="device.last_num?device.last_num:'0'"
							:disabled="device.is_submit"
							placeholder="请输入"
							@input="onLastNumInputChange($event, room._id, device.type)"
							@blur="validateLastNumInput($event,room._id, device.type)"
						/>
					</view>
					
					<view class="reading-input">
						<input 
							class="input-field current-input" 
							:class="{ 'input-disabled': device.is_submit }"
							type="digit" 
							inputmode="decimal"
							:value="device.num ? device.num : ''"
							:placeholder="device.num ? '' : (device.is_submit ? '已提交' : '请输入')"
							:disabled="device.is_submit"
							@input="onInputChange($event, room._id, device.type)"
							@blur="validateInput($event,room._id, device.type)"
						/>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<button class="save-btn secondary" @click="saveOnly">仅保存</button>
			<button class="save-btn primary" @click="saveAndGenerateBill">保存并生成账单</button>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom" background-color="#fff">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeFilterPopup">取消</text>
					<text class="popup-title">{{ filterTitle }}</text>
					<text class="confirm-btn" @click="confirmFilter">确定</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" 
						v-for="(option, index) in currentFilterOptions" 
						:key="index"
						:class="{ active: selectedFilterIndex === index }"
						@click="selectFilterOption(index)">
						<text class="option-text">{{ option.text }}</text>
						<uni-icons v-if="selectedFilterIndex === index" type="checkmarkempty" size="16" color="#07c160" />
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import { dayjs } from '../../utils/dayjs.min'
	
	const db = uniCloud.databaseForJQL()

	// 响应式数据
	const rooms = ref([])
	const utilityRecords = ref([])
	const historyRecords = ref([]) // 添加历史记录
	const selectedDevice = ref('全部设备')
	const selectedRoom = ref('全部房间')
	const selectedStatus = ref('全部状态')
	const currentMonth = ref(dayjs().format('YYYY-MM'))
	const buildingId = ref('')
	const buildingName = ref('')

	// 筛选弹窗相关
	const filterPopup = ref(null)
	const filterTitle = ref('')
	const currentFilterOptions = ref([])
	const selectedFilterIndex = ref(0)
	const currentFilterType = ref('')

	// 筛选选项
	const deviceOptions = [
		{ text: '全部设备', value: 'all' },
		{ text: '冷水表', value: 1 },
		{ text: '电表', value: 2 },
		{ text: '燃气表', value: 3 }
	]

	// 房间选项 - 动态生成
	const roomOptions = computed(() => {
		const options = [{ text: '全部房间', value: 'all' }]
		rooms.value.forEach(room => {
			options.push({ text: room.name, value: room._id })
		})
		return options
	})

	// 筛选后的房间列表
	const filteredRooms = computed(() => {
		let filtered = rooms.value

		// 根据房间筛选
		if (selectedRoom.value !== '全部房间') {
			const selectedRoomId = roomOptions.value.find(option => option.text === selectedRoom.value)?.value
			if (selectedRoomId !== 'all') {
				filtered = filtered.filter(room => room._id === selectedRoomId)
			}
		}

		// 根据状态筛选
		if (selectedStatus.value !== '全部状态') {
			filtered = filtered.filter(room => {
				const devices = room.devices || []
				
				switch(selectedStatus.value) {
					case '已抄':
						// 房间中所有设备都已抄读且未提交
						return devices.every(device => device.is_update && !device.is_submit)
					case '未抄':
						// 房间中有设备未抄读
						return devices.some(device => !device.is_update)
					case '已提交':
						// 房间中所有设备都已提交
						return devices.every(device => device.is_submit)
					default:
						return true
				}
			})
		}

		return filtered
	})

	// 页面加载
	onLoad((e) => {
		console.log("onLoad", e)
		if (e.id && e.name) {
			buildingId.value = e.id
			buildingName.value = e.name
			uni.setNavigationBarTitle({
				title:e.name
			})
			initData()
		}
	})

	// 初始化数据
	const initData = async () => {
		await getRoomList()
		await getUtilityRecords()
		mergeRoomAndRecords()
	}

	// 获取房间列表
	const getRoomList = async () => {
		try {
			const res = await db.collection("fangke_room")
				.where(`building_id=="${buildingId.value}"`)
				.field("_id,name,building_id")
				.get()
			
			console.log('getRoomList', res)
			rooms.value = res.data
		} catch (error) {
			console.error('获取房间列表失败:', error)
			uni.showToast({
				title: '获取房间列表失败',
				icon: 'none'
			})
		}
	}

	// 获取房间当月的水电记录
	const getUtilityRecords = async () => {
		try {
			const roomIds = rooms.value.map(room => room._id)
			if (roomIds.length === 0) return

			// 获取当月记录
			const currentRes = await db.collection("fangke_utility_records")
				.where(`room_id in ["${roomIds.join('","')}"] && date == "${currentMonth.value}"`)
				.get()
			
			console.log('获取当月水电记录', currentRes)
			utilityRecords.value = currentRes.data

			// 获取历史记录（用于没有当月记录时获取上月读数）
			const historyRes = await db.collection("fangke_utility_records")
				.where(`room_id in ["${roomIds.join('","')}"] && date < "${currentMonth.value}"`)
				.orderBy('date', 'desc')
				.orderBy('record_date', 'desc')
				.get()
			
			console.log('获取历史水电记录', historyRes)
			historyRecords.value = historyRes.data
		} catch (error) {
			console.error('获取水电记录失败:', error)
		}
	}

	// 合并房间和记录数据
	const mergeRoomAndRecords = () => {
		rooms.value.forEach(room => {
			// 为每个房间添加水电表记录
			room.devices = [
				{ type: 1, name: '冷水表' },
				{ type: 2, name: '电表' }
			].map(deviceType => {
				// 查找当月对应的记录
				const currentRecord = utilityRecords.value.find(r => 
					r.room_id === room._id && r.type === deviceType.type
				)
				
				// 如果当月有记录，直接使用当月记录的数据
				if (currentRecord) {
					return {
						type: deviceType.type,
						name: deviceType.name,
						last_num: currentRecord.last_num/100 || 0,
						num: currentRecord.num/100 || 0,
						is_update: currentRecord.is_update || false,
						is_submit: currentRecord.is_submit || false,
						record_date: currentRecord.record_date || null,
						max_num: currentRecord.max_num || 9999999,
						_id: currentRecord._id || null,
						original_last_num: currentRecord.last_num/100 || 0 // 保存原始上次读数
					}
				} else {
					// 如果当月没有记录，查找最近的历史记录
					const latestHistoryRecord = historyRecords.value.find(r => 
						r.room_id === room._id && r.type === deviceType.type
					)
					
					// 如果有历史记录，使用历史记录的num作为last_num
					const lastNum = latestHistoryRecord ? (latestHistoryRecord.num/100 || 0) : 0
					
					console.log(`房间${room.name}的${deviceType.name}没有当月记录，使用历史记录上月读数:`, lastNum)
					
					return {
						type: deviceType.type,
						name: deviceType.name,
						last_num: lastNum,
						num: 0,
						is_update: false,
						is_submit: false,
						record_date: latestHistoryRecord?.record_date || null, // 使用最近历史记录的抄表时间
						max_num: latestHistoryRecord?.max_num || 9999999,
						_id: null,
						original_last_num: lastNum // 保存原始上次读数
					}
				}
			})
		})
	}

	// 获取设备列表
	const getDeviceList = (room) => {
		if (selectedDevice.value === '全部设备') {
			return room.devices || []
		}
		
		const deviceType = deviceOptions.find(option => option.text === selectedDevice.value)?.value
		if (deviceType === 'all') {
			return room.devices || []
		}
		
		return (room.devices || []).filter(device => device.type === deviceType)
	}

	// 获取设备名称
	const getDeviceName = (type) => {
		const deviceMap = {
			1: '冷水表',
			2: '电表',
			3: '燃气表'
		}
		return deviceMap[type] || '未知设备'
	}

	// 获取状态文本
	const getStatusText = (device) => {
		if (device.is_submit) {
			return '已提交'
		}
		return device.is_update ? '已抄' : '未抄'
	}

	// 获取状态样式类
	const getStatusClass = (device) => {
		if (device.is_submit) {
			return 'status-submitted'
		}
		return device.is_update ? 'status-done' : 'status-pending'
	}

	// 格式化数字显示
	const formatNumber = (num) => {
		if (!num) return '0.0'
		return (num / 100).toFixed(1)
	}

	// 格式化日期
	const formatDate = (timestamp) => {
		if (!timestamp) return dayjs().format('MM-DD')
		return dayjs(timestamp).format('MM-DD')
	}

	// 判断是否为换表
	const isReplacedMeter = (device) => {
		// 转换为数字进行比较
		const currentNum = parseFloat(device.num || 0)
		const lastNum = parseFloat(device.last_num || 0)
		
		// 情况1: 当前读数超过最大值
		if (currentNum > device.max_num) {
			return true
		}
		
		// 情况2: 上个月读数距离最大值不到5%，且当月读数小于上个月读数
		if (lastNum > device.max_num * 0.95 && currentNum < lastNum && currentNum > 0) {
			return true
		}
		
		return false
	}

	// 数值输入验证函数
	const validateNumberInput = (value) => {
		// 只允许数字和小数点
		const numberRegex = /^[0-9]*\.?[0-9]*$/
		return numberRegex.test(value)
	}

	// 格式化数值输入
	const formatNumberInput = (value) => {
		// 移除非数字和小数点的字符
		let formattedValue = value.replace(/[^\d.]/g, '')
		
		// 确保只有一个小数点
		const parts = formattedValue.split('.')
		if (parts.length > 2) {
			formattedValue = parts[0] + '.' + parts.slice(1).join('')
		}
		
		// 限制小数点后最多2位
		if (parts.length === 2 && parts[1].length > 2) {
			formattedValue = parts[0] + '.' + parts[1].substring(0, 2)
		}
		
		// 防止以多个0开头（除了0.xx的情况）
		if (formattedValue.length > 1 && formattedValue[0] === '0' && formattedValue[1] !== '.') {
			formattedValue = formattedValue.substring(1)
		}
		
		return formattedValue
	}

	// 处理键盘输入限制
	const onKeyInput = (event, roomId, deviceType) => {
		const key = event.detail.key
		const value = event.detail.value
		
		// 允许的键：数字、小数点、退格键、删除键
		const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
		
		// 如果不是允许的键，阻止输入
		if (!allowedKeys.includes(key)) {
			event.preventDefault()
			return false
		}
		
		// 检查小数点输入
		if (key === '.') {
			// 如果已经包含小数点，阻止再次输入
			if (value.includes('.')) {
				event.preventDefault()
				return false
			}
		}
		
		// 限制数字长度（整数部分最多8位，小数部分最多2位）
		if (key >= '0' && key <= '9') {
			const parts = value.split('.')
			if (parts.length === 1 && parts[0].length >= 8) {
				// 整数部分已达到最大长度
				event.preventDefault()
				return false
			}
			if (parts.length === 2 && parts[1].length >= 2) {
				// 小数部分已达到最大长度
				event.preventDefault()
				return false
			}
		}
		
		return true
	}

	// 输入值变化
	const onInputChange = (event, roomId, deviceType) => {
		const rawValue = event.detail.value
		console.log("onInputChange原始值:", rawValue)
		
		// 验证输入是否为有效数字
		if (rawValue && !validateNumberInput(rawValue)) {
			uni.showToast({
				title: '请输入有效的数字',
				icon: 'none',
				duration: 1500
			})
			return
		}
		
		// 格式化输入值
		const formattedValue = formatNumberInput(rawValue)
		console.log("onInputChange格式化后:", formattedValue)
		
		const room = rooms.value.find(r => r._id === roomId)
		if (room) {
			const device = room.devices.find(d => d.type === deviceType)
			if (device && !device.is_submit) {
				device.num = formattedValue
				device.is_update = true
			}
		}
	}

	// 上个月读数输入变化
	const onLastNumInputChange = (event, roomId, deviceType) => {
		const rawValue = event.detail.value
		console.log("onLastNumInputChange原始值:", rawValue)
		
		// 验证输入是否为有效数字
		if (rawValue && !validateNumberInput(rawValue)) {
			uni.showToast({
				title: '请输入有效的数字',
				icon: 'none',
				duration: 1500
			})
			return
		}
		
		// 格式化输入值
		const formattedValue = formatNumberInput(rawValue)
		console.log("onLastNumInputChange格式化后:", formattedValue)
		
		const room = rooms.value.find(r => r._id === roomId)
		if (room) {
			const device = room.devices.find(d => d.type === deviceType)
			if (device && !device.is_submit) {
				device.last_num = formattedValue
				device.is_update = true
			}
		}
	}

	// 验证输入
	const validateInput = (event,roomId, deviceType) => {
		console.log("validateInput",event);
		const room = rooms.value.find(r => r._id === roomId)
		if (room) {
			const device = room.devices.find(d => d.type === deviceType)
			if (device) {
				// 检查是否已提交，已提交的不能修改
				if (device.is_submit) {
					uni.showToast({
						title: '该数据已提交，不能修改',
						icon: 'none'
					})
					return
				}
				
				// 转换为数字进行比较
				const currentNum = parseFloat(device.num || 0)
				const lastNum = parseFloat(device.last_num || 0)
				
				// 检查是否为换表
				if (isReplacedMeter(device)) {
					// 换表情况，允许输入
					console.log('检测到换表，允许输入')
				} else if (currentNum < lastNum) {
					// 非换表情况下，当前读数不能小于上次读数
					uni.showToast({
						title: '当前读数不能小于上次读数',
						icon: 'none'
					})
					// 重置为上次读数
					device.num = device.last_num
				}
			}
		}
	}

	// 验证上个月读数输入
	const validateLastNumInput = (event,roomId, deviceType) => {
		console.log("validateLastNumInput",event);
		const room = rooms.value.find(r => r._id === roomId)
		if (room) {
			const device = room.devices.find(d => d.type === deviceType)
			if (device) {
				// 检查是否已提交，已提交的不能修改
				if (device.is_submit) {
					uni.showToast({
						title: '该数据已提交，不能修改',
						icon: 'none'
					})
					return
				}
				
				// 转换为数字进行比较
				const currentNum = parseFloat(device.num || 0)
				const lastNum = parseFloat(device.last_num || 0)
				
				// 检查上个月读数是否大于当月读数
				if (currentNum > 0 && lastNum > currentNum && !isReplacedMeter(device)) {
					uni.showToast({
						title: '上次读数不能大于当前读数',
						icon: 'none'
					})
					// 重置为原始值
					device.last_num = device.original_last_num || 0
				}
			}
		}
	}

	// 验证所有数据
	const validateAllData = () => {
		let hasError = false
		
		rooms.value.forEach(room => {
			room.devices.forEach(device => {
				if (device.is_update && !device.is_submit) {
					// 转换为数字进行比较
					const currentNum = parseFloat(device.num || 0)
					const lastNum = parseFloat(device.last_num || 0)
					
					// 检查是否为换表，如果不是换表且当前读数小于上次读数，则报错
					if (!isReplacedMeter(device) && currentNum < lastNum) {
						uni.showToast({
							title: `${room.name}的${getDeviceName(device.type)}读数不能小于上次读数`,
							icon: 'none'
						})
						hasError = true
					}
					
					// 检查上次读数是否大于当前读数（非换表情况）
					if (currentNum > 0 && lastNum > currentNum && !isReplacedMeter(device)) {
						uni.showToast({
							title: `${room.name}的${getDeviceName(device.type)}上次读数不能大于当前读数`,
							icon: 'none'
						})
						hasError = true
					}
				}
			})
		})
		
		return !hasError
	}

	// 获取抄表数据用于生成账单
	const getMeterDataForBill = () => {
		const meterData = []
		
		rooms.value.forEach(room => {
			room.devices.forEach(device => {
				if (device.is_update && !device.is_submit) {
					// 转换为数字进行计算
					const currentNum = parseFloat(device.num || 0)
					const lastNum = parseFloat(device.last_num || 0)
					
					// 计算用量差值
					let usage = 0
					if (isReplacedMeter(device)) {
						// 换表情况：用量 = 当前读数 + (最大值 - 上次读数)
						usage = currentNum + (device.max_num - lastNum)
					} else {
						// 正常情况：用量 = 当前读数 - 上次读数
						usage = currentNum - lastNum
					}
					
					meterData.push({
						room_id: room._id,
						room_name: room.name,
						device_type: device.type,
						device_name: getDeviceName(device.type),
						last_num: lastNum,
						current_num: currentNum,
						usage: usage,
						record_id: device._id
					})
				}
			})
		})
		
		return meterData
	}

	// 显示设备筛选
	const showDeviceFilter = () => {
		filterTitle.value = '设备筛选'
		currentFilterOptions.value = deviceOptions
		currentFilterType.value = 'device'
		selectedFilterIndex.value = deviceOptions.findIndex(option => option.text === selectedDevice.value)
		filterPopup.value?.open()
	}

	// 显示房间筛选
	const showRoomFilter = () => {
		filterTitle.value = '房间筛选'
		currentFilterOptions.value = roomOptions.value
		currentFilterType.value = 'room'
		selectedFilterIndex.value = roomOptions.value.findIndex(option => option.text === selectedRoom.value)
		filterPopup.value?.open()
	}

	// 显示状态筛选
	const showStatusFilter = () => {
		filterTitle.value = '状态筛选'
		currentFilterOptions.value = [
			{ text: '全部状态', value: 'all' },
			{ text: '已抄', value: 'done' },
			{ text: '未抄', value: 'pending' },
			{ text: '已提交', value: 'submitted' }
		]
		currentFilterType.value = 'status'
		selectedFilterIndex.value = currentFilterOptions.value.findIndex(option => option.text === selectedStatus.value)
		filterPopup.value?.open()
	}

	// 选择筛选选项
	const selectFilterOption = (index) => {
		selectedFilterIndex.value = index
	}

	// 确认筛选
	const confirmFilter = () => {
		const selectedOption = currentFilterOptions.value[selectedFilterIndex.value]
		
		switch (currentFilterType.value) {
			case 'device':
				selectedDevice.value = selectedOption.text
				break
			case 'room':
				selectedRoom.value = selectedOption.text
				break
			case 'status':
				selectedStatus.value = selectedOption.text
				break
		}
		
		closeFilterPopup()
	}

	// 关闭筛选弹窗
	const closeFilterPopup = () => {
		filterPopup.value?.close()
	}

	// 仅保存
	const saveOnly = async () => {
		try {
			// 检查是否有可保存的数据
			const hasUnsavedData = rooms.value.some(room => 
				room.devices.some(device => device.is_update && !device.is_submit)
			)
			
			if (!hasUnsavedData) {
				uni.showToast({
					title: '没有可保存的数据',
					icon: 'none'
				})
				return
			}
			
			await saveUtilityRecords(false)
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			})
		} catch (error) {
			console.error('保存失败:', error)
			uni.showToast({
				title: '保存失败',
				icon: 'none'
			})
		}
	}

	// 保存并生成账单
	const saveAndGenerateBill = async () => {
		try {
			// 检查是否有可提交的数据
			const hasUnsavedData = rooms.value.some(room => 
				room.devices.some(device => device.is_update && !device.is_submit)
			)
			
			if (!hasUnsavedData) {
				uni.showToast({
					title: '没有可提交的数据',
					icon: 'none'
				})
				return
			}
			
			// 验证所有数据
			if (!validateAllData()) {
				return
			}
			
			await saveUtilityRecords(false)
			
			// 准备已抄数据传递给下一个页面
			const meterData = getMeterDataForBill()
			console.log("计算差值数据：",meterData);
			uni.showToast({
				title: '保存成功并生成账单',
				icon: 'success'
			})
			
			// 跳转到账单页面，传递已抄数据
			setTimeout(() => {
				uni.navigateTo({
					url: `/pagesB/auditUtility/auditUtility?uid=${uniCloud.getCurrentUserInfo().uid}`
				})
			}, 1000)
		} catch (error) {
			console.error('保存失败:', error)
			uni.showToast({
				title: '保存失败',
				icon: 'none'
			})
		}
	}

	// 保存水电记录
	const saveUtilityRecords = async (generateBill = false) => {
		const recordsToSave = []
		const recordsToUpdate = []
		console.log("new Date() ==",new Date().getTime());
		rooms.value.forEach(room => {
			room.devices.forEach(device => {
				// 只保存已更新且未提交的数据
				if (device.is_update && !device.is_submit) {
					// 转换为数字进行计算
					const currentNum = parseFloat(device.num || 0)
					const lastNum = parseFloat(device.last_num || 0)
					
					// 计算使用量
					let usage = 0
					if (isReplacedMeter(device)) {
						// 换表情况：用量 = 当前读数 + (最大值 - 上次读数)
						usage = currentNum + (device.max_num/100 - lastNum)
					} else {
						// 正常情况：用量 = 当前读数 - 上次读数
						usage = currentNum - lastNum
					}
					
					const recordData = {
						room_id: room._id,
						building_id: buildingId.value,
						type: device.type,
						date: currentMonth.value,
						record_date: new Date().getTime(),
						last_num: parseFloat(device.last_num || 0) * 100,
						num: parseFloat(device.num || 0) * 100,
						usage: Math.round(usage * 100), // 转换为分并四舍五入
						is_submit: generateBill,
						is_update: true,
						max_num: device.max_num,
						room_name: room.name
					}

					if (device._id) {
						// 更新现有记录
						recordsToUpdate.push({
							_id: device._id,
							...recordData
						})
					} else {
						// 创建新记录
						recordsToSave.push(recordData)
					}
				}
			})
		})
		console.log("recordsToSave",recordsToSave);
		console.log("recordsToUpdate",recordsToUpdate);
		// 批量保存新记录
		if (recordsToSave.length > 0) {
			await db.collection('fangke_utility_records').add(recordsToSave).then(res =>{
				console.log("批量保存新记录",res);
			})
		}

		// 批量更新现有记录
		for (const record of recordsToUpdate) {
			const { _id ,...data} = record
			console.log("data ==",data);
			await db.collection('fangke_utility_records').doc(record._id).update(data).then(res =>{
				console.log("批量更新现有记录",res);
			})
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}

	.filter-section {
		display: flex;
		background: #fff;
		padding: 20rpx 30rpx;
		gap: 40rpx;
		border-bottom: 1px solid #f0f0f0;

		.filter-item {
			display: flex;
			align-items: center;
			gap: 8rpx;
			flex: 1;
		}

		.filter-text {
			font-size: 28rpx;
			color: #333;
		}
	}

	.table-header {
		display: flex;
		background: #f8f8f8;
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #e0e0e0;

		.header-cell {
			font-size: 26rpx;
			color: #666;
			font-weight: 500;
		}

		.device-name {
			width: 140rpx;
		}

		.status-time {
			width: 200rpx;
		}

		.last-reading {
			flex: 1;
			text-align: center;
		}

		.current-reading {
			flex: 1;
			text-align: center;
		}
	}

	.room-list {
		.room-section {
			margin-bottom: 20rpx;

			.room-title {
				padding: 20rpx 30rpx;
				background-color: #e8f5e8;
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				border-left: 4px solid #07c160;
			}

			.device-row {
				display: flex;
				align-items: center;
				padding: 20rpx 30rpx;
				background: #fff;
				border-bottom: 1px solid #f0f0f0;

				.device-info {
					width: 300rpx;
					display: flex;
					align-items: center;

					.device-name {
						width: 100rpx;
						font-size: 28rpx;
						color: #333;
						margin-right: 20rpx;
					}

					.status-info {
						width: 200rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.status {
							font-size: 24rpx;
							padding: 4rpx 8rpx;
							border-radius: 4rpx;
							margin-bottom: 4rpx;
						}

						.status-done {
							background: #e8f5e8;
							color: #07c160;
						}

						.status-pending {
							background: #fff3e0;
							color: #ff9800;
						}

						.status-submitted {
							background: #f3f4f6;
							color: #6b7280;
						}

						.record-time {
							font-size: 22rpx;
							color: #999;
						}
					}
				}

				.reading-input {
					width: 200rpx;
					margin: 0 20rpx;

					.input-field {
						width: 100%;
						height: 60rpx;
						border: 2rpx solid #e0e0e0;
						border-radius: 8rpx;
						padding: 0 16rpx;
						font-size: 26rpx;
						text-align: center;
						background: #f8f8f8;
					}
					
					.input-field:not(.input-disabled) {
						background: #fff;
						border-color: #999;
					}

					.current-input {
						background: #fff;
						border-color: #07c160;
					}

					.current-input:focus {
						border-color: #07c160;
						box-shadow: 0 0 0 2rpx rgba(7, 193, 96, 0.2);
					}

					.input-disabled {
						background: #f5f5f5 !important;
						border-color: #e0e0e0 !important;
						color: #999 !important;
						cursor: not-allowed;
					}
				}
			}
		}
	}

	.bottom-actions {
		display: flex;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		padding: 20rpx 30rpx;
		background: #fff;
		border-top: 1px solid #f0f0f0;
		box-sizing: border-box;
		z-index: 10;
		gap: 20rpx;

		.save-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			font-weight: 500;
			border: none;
		}

		.secondary {
			background: #f0f0f0;
			color: #666;
		}

		.primary {
			background: #07c160;
			color: #fff;
		}
	}

	/* 筛选弹窗样式 */
	.filter-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #EBEDF0;
	}

	.cancel-btn {
		color: #666;
		font-size: 28rpx;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.confirm-btn {
		color: #07c160;
		font-weight: 500;
		font-size: 28rpx;
	}

	.filter-options {
		padding: 20rpx 0;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.filter-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.filter-option:last-child {
		border-bottom: none;
	}

	.filter-option.active {
		background: #f0f9ff;
	}

	.option-text {
		font-size: 28rpx;
		color: #333;
	}

	.filter-option.active .option-text {
		color: #07c160;
	}
</style>