<template>
	<view class="container">
		<view class="title">
			<text>总层数/每层房间数</text>
			<text>{{floor}}层/{{num}}间</text>
		</view>
		<view class="content" v-for="(item,index) in floors" :key="index">
			<uni-section :title="'第'+(index+1)+'层'" type="line" class="heard" style="background-color: #EDF2F4;">
				<template v-slot:right>
					<view @click="delFloor(index)">删除楼层</view>
				</template>
			</uni-section>
			<view class="body">
				<view class="room" v-for="(item2,roomIndex) in rooms[index]" :key="roomIndex">
					<input class="num" v-model="item2.name" type="number" maxlength="5"
						@blur="change(index,roomIndex,item2.name)" @confirm="change(index,roomIndex,item2.name)"
						@focus="focus" />
					<view class="image" @click="del(index,roomIndex)">
						<image src="../../static/close.png" style="width: 100%;height: 100%;" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="foot">
				<button size="mini" style="margin: 20rpx;" @click="add(index)"> +添加房间 </button>
			</view>
		</view>

		<view class="bottom">
			<button class="left" @click="close">取消</button>
			<button class="right" type="primary" @click="save">保存</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
	} from 'vue';
	import { onLoad } from "@dcloudio/uni-app"
	import uniCloud from '@dcloudio/uni-cloud';

	// 响应式数据
	const floor = ref(1);
	const num = ref(1);
	const floors = ref([]);
	const rooms = ref([]);
	const name = ref('');
	const origen = ref('');


	// 页面加载时处理参数
	onLoad((e) => {
		console.log("buidlingRoom", e.floor, e.num);
		if (e.floor && e.num) {
			floor.value = parseInt(e.floor);
			num.value = parseInt(e.num);
			initRooms();
		}
	});

	// 初始化房间数据
	const initRooms = () => {
		floors.value = [];
		rooms.value = [];

		for (let i = 1; i <= floor.value; i++) {
			floors.value.push(i);
			let roomList = [];
			for (let j = 1; j <= num.value; j++) {
				let room = {
					floor: i,
					name: j < 10 ? i + "0" + j : i + "" + j
				};
				roomList.push(room);
			}
			rooms.value.push(roomList);
		}

		console.log("data", floors.value);
		console.log("data", rooms.value);
	};

	// 删除楼层
	const delFloor = (index) => {
		floors.value.splice(index, 1);
		floor.value--;
	};

	// 删除房间
	const del = (floorIndex, roomIndex) => {
		rooms.value[floorIndex].splice(roomIndex, 1);
	};

	// 添加房间
	const add = (floorIndex) => {
		let l = rooms.value[floorIndex].length + 1;
		let f = floorIndex + 1;
		let room = {
			floor: f,
			name: l < 10 ? f + "0" + l : f + "" + l
		};
		rooms.value[floorIndex].push(room);
	};

	// 更改房间名称
	const change = (floorIndex, roomIndex, newName) => {
		let map = rooms.value[floorIndex].map(function(item) {
			return item.name === newName;
		});
		console.log("map", map);

		let sum = 0;
		map.forEach(function(element) {
			if (element) {
				sum = sum + 1;
			}
		});

		console.log("sum", sum);
		if (sum > 1) {
			uni.showToast({
				title: '修改的房名已重复，请重新修改',
				icon: 'none'
			});
			console.log(rooms.value[floorIndex][roomIndex].name);
			rooms.value[floorIndex][roomIndex].name = origen.value;
		}
	};

	// 关闭页面
	const close = () => {
		uni.navigateBack();
	};

	// 保存房间数据
	const save = () => {
		uni.$emit("roomList", rooms.value);
		uni.navigateBack();
	};

	// 输入框聚焦事件
	const focus = (e) => {
		console.log("focus", e);
		origen.value = e.detail.value;
	};
</script>
<style lang="scss">
	.container {
		padding: 10rpx 20rpx;
		box-sizing: border-box;

		.title {
			margin: 20rpx;
			display: flex;
			justify-content: space-between;
		}

		.content {
			display: flex;
			flex-direction: column;

			.head {
				background-color: darkgray;
			}

			.body {
				display: grid;
				grid-template-columns: auto auto auto auto;

				.room {
					width: max-content;
					position: relative;
					margin: 20rpx;

					.num {
						display: flex;
						padding: 10rpx;
						border: 1rpx solid gray;
						align-content: center;
						align-items: center;
						width: 100rpx;
						border-radius: 10rpx;
					}

					.image {
						position: absolute;
						top: -15rpx;
						right: -15rpx;
						width: 30rpx;
						height: 30rpx;
					}
				}
			}

			.foot {
				box-sizing: border-box;

				.slide {
					width: 100%;
					height: 2rpx;
					background-color: #eee;
					margin: 0 20rpx;
				}
			}
		}

		.bottom {
			display: flex;
			margin: 20rpx 0rpx;

			button {
				padding: 0rpx 40rpx;
			}

		}
	}
</style>