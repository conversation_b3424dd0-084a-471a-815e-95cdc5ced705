const {
	result
} = require('result');
let dbJQL;
const dayjs = require('dayjs')
var utc = require('dayjs/plugin/utc')
const customParseFormat = require('dayjs/plugin/customParseFormat');
var timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)
dayjs.tz.setDefault("Asia/Shanghai")
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function() { // 通用预处理器
		// this.params = this.getParams()[0]
		this.startTime = Date.now();
		this.params = this.getHttpInfo()
		console.log("httpMethod", this.params.httpMethod);
		console.log("httpInfo", this.params);
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		let info = this.getClientInfo()
		console.log("this.getClientInfo", info);
		if (this.params.httpMethod == "POST") {
			//post请求
			// queryStringParameters: { uid: 'ddaadw' },	//参数在form-data时
			let body = this.getHttpInfo().body;
			if (!body) throw result(400, "required");
			this.params = JSON.parse(this.getHttpInfo().body)
		} else {
			//get请求
			this.params = this.getParams()[0]
		}
	},

	async get() {
		console.log("get", this.params);
		
		return null
	},
	
	
	async commit(){
		console.log("commit", this.params);
		let {
			uid,
			data
		} = this.params
		dbJQL.setUser({ //设置用户状态
			uid: uid
		})
		let bill_id = data.bill
		let arr = []
		let detail_list = []
		let status = 1
		let room_id = ""
		detail_list = await dbJQL.collection("fangke_room_bill").doc(bill_id).get().then(res =>{
			console.log("获取订单",res);
			if(res.errCode == 0){
				let bill = res.data[0]
				data.name = bill.name
				data.room_name = bill.room_name
				data.room_id = bill.room_id
				data.building_id = bill.building_id
				detail_list = bill.detail
				status = bill.status
				bill.detail.forEach(item =>{
					if(data.detail.includes(item.text)){	//修改明细里的状态
						item.status = 0
						arr.push(item)
					}
				})
				room_id = bill.room_id
				data.detail = arr
				return bill.detail
			}else{
				return []
			}
		})
		console.log("最后订单",data);
		let res = await dbJQL.collection("fangke_room_account").add({...data}).then(res =>{
			console.log("添加流水",res);
			return res.errCode == 0
		})
		if(res){
			let len = 0
			let sum = 0
			detail_list.forEach(item =>{
				if(item.status == 0){
					len ++
					sum = sum + item.value
				}
			})
			if(detail_list.length == len && detail_list.length !== 0){
				status = 0
			}
			console.log("更新明细",detail_list);
			console.log("已收金额",sum);
			await dbJQL.collection("fangke_room_bill").doc(bill_id).update({
				money:sum,
				detail:detail_list,
				status:status
			}).then(res =>{
				console.log("更新订单成功",res);
			})
			let time =  dayjs.tz().format("YYYY-MM")
			const escapedYearMonth = time.replace(/-/g, '\\-'); // 转义输入中的特殊字符（如 -）
			let reg = new RegExp(`^${escapedYearMonth}-\\d{2}$`) //判断本月的订单
			//检查是否本月应收订单都支付完了，是则修改房间状态
			let isFinish = true
			await dbJQL.collection("fangke_room_bill").where(`room_id == "${room_id}" && ${new RegExp(`^${escapedYearMonth}-\\d{2}$`)}.test(day)`).get().then(res =>{
				console.log("获取本月指定房间账单",res.data);
				res.data.forEach(item =>{
					if(item.status !== 0){
						isFinish = false
					}
				})
			})
			if(isFinish){
				await dbJQL.collection("fangke_room").doc(room_id).update({
					room_status:1
				}).then(res =>{
					console.log("更新房间成功");
				})
			}
			
			return result(0,"success","提交成功")
		}else{
			return result(1000,"fail","提交失败")
		}
		
	},

	


	_after: function(error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}

}