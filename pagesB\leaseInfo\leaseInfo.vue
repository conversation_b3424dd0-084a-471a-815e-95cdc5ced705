<template>
	<view class="page">
		<view class="header">
			<view :style="{height:getTitleBarHeight+'px'}" class="bar">
				<view class="nav-left">
					<uni-icons type="back" size="24" color="#fff" @click="onBack()" />
				</view>
			</view>
			<view class="room-info">
				<text class="room-number">{{room_name||""}}</text>
				<view class="lease-info">
					<text class="lease-date">租期：{{contract.start_time||""}} ~ {{contract.end_time || ""}}</text>
					<text class="tenant">签约人：{{tenant.name || ""}}</text>
				</view>
			</view>
		</view>
		
		<view class="tab-bar">
			<view class="tab-item" :class="{ active: activeTab === 'detail' }" @click="switchTab('detail')">
				<text>详情</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'bill' }" @click="switchTab('bill')">
				<text>账单</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'flow' }" @click="switchTab('flow')">
				<text>流水</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'tenant' }" @click="switchTab('tenant')">
				<text>租客</text>
			</view>
		</view>

		<scroll-view 
			class="scroll-content"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refresherTriggered"
			@refresherrefresh="onRefresh"
			@refresherrestore="onRestore"
		>
			<view class="content">
				<template v-if="activeTab === 'tenant'">
					<view class="content-item">
						<view class="tenant-section">
							<view class="list-item">
								<text class="section-title">承租人信息</text>
								<uni-icons type="compose" size="20" color="#666" @click="editRoommate(tenant,0,1)" />
							</view>

							<view class="list-item">
								<text class="item-label">姓名</text>
								<text class="item-value">{{tenant.name ||""}}</text>
							</view>
							<view class="list-item">
								<text class="item-label">性别</text>
								<text class="item-value">{{tenant.gender == 1?"男":"女"}}</text>
							</view>
							<view class="list-item">
								<text class="item-label">手机号码</text>
								<text class="item-value">{{tenant.phone || ""}}</text>
							</view>
							<view class="list-item">
								<text class="item-label">身份证号码</text>
								<text class="item-value">{{ tenant.id_card || ""}}</text>
							</view>
						</view>
						<view class="tenant-section">
							<view class="section-header">
								<text class="section-title">同住人信息</text>
								<uni-icons type="plus" size="20" color="#00C389" @click="addRoomate" />
							</view>
							<view class="co-tenant-list">
								<view class="co-tenant-item" v-for="(item, index) in tenantList" :key="index">
									<view class="co-tenant-info">
										<view style="display: flex;">
											<text class="name">{{ item.name }}</text>
											<text class="gender">({{ item.gender == 1?"男":"女"}})</text>
										</view>
										<text class="phone">{{ item.phone }}</text>
									</view>
									<view class="co-tenant-actions">
										<uni-icons type="compose" size="20" color="#666"
											@click="editRoommate(item,index,2)" />
										<uni-icons type="trash" size="20" color="#FF6B6B"
											@click="removeCoTenant(item._id,index)" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>

				<template v-if="activeTab === 'flow'">
					<view class="content-title">一共有{{accountList.length || 0}}条流水账</view>
					<view class="content-card" v-for="(item, index) in accountList" :key="index" @click="jumpAmountInfo(item)">
						<view class="flow-content">
							<view class="list-item">
								<view class="flow-item">
									<view style="display: flex; align-items: baseline;gap: 10rpx;">
										<text class="item-value,large">{{ item.name }}</text>
										<text class="item-label">{{item.room_name}}</text>
									</view>
									<view style="display: flex;">
										<text class="item-label">{{ item.paymentDate }}</text>
										<text class="item-value">({{ item.paymentMethod }})</text>
									</view>
								</view>
								<view class="amount income">
									{{item.isPay?"+":"-"}}{{item.amount/100}}
								</view>
							</view>
						</view>
					</view>
				</template>

				<template v-if="activeTab === 'bill'">
					<view class="content-title">一共有{{billList.length || 0}}条账单</view>
					<view class="content-card" v-for="(item, index) in billList" :key="index"
						@click="jumpBillInfo(item._id)">
						<view class="card-header">
							<text class="card-title">{{item.name}}</text>
							<text class="status-tag"
								:class="item.status == 0 ? 'success' : item.status == 1 ?'warning':'err'">{{ item.status == 0 ? '已支付' : item.status == 1 ?'待支付':'已逾期'  }}</text>
						</view>
						<view class="divider">
							<view class="list-item">
								<text class="item-label small">应收日期</text>
								<text
									class="item-value">{{ item.startTime === ""?item.day:item.startTime+"-"+item.endTime }}</text>
							</view>
							<view class="list-item">
								<text class="item-label">应收金额(元)</text>
								<text class="item-value">¥{{ item.sum?item.sum/100:0 }}</text>
							</view>
							<view class="list-item">
								<text class="item-label">已收金额(元)</text>
								<text class="amount large income">¥{{ item.money?item.money/100:0 }}</text>
							</view>
						</view>
					</view>
				</template>

				<template v-if="activeTab === 'detail'">
					<view class="content-item">
						<text class="section-title">基本信息</text>
						<view class="list-item">
							<view class="item-label">
								租期<text class="status-tag info">{{contract.diff || 0}}个月</text>
							</view>
							<view class="item-value">
								<text>{{contract.start_time||""}}至{{contract.end_time || ""}}</text>
							</view>
						</view>
						<view class="list-item">
							<text class="item-label">租金(元/月)</text>
							<text class="item-value">¥ {{contract.rent/100 || 0}}</text>
						</view>
						<view class="list-item">
							<view class="item-value">
								<text class="item-label">押金总额（元）</text>
								<text class="desc">（包含历史已缴租所有押金）</text>
							</view>
							<text class="amount">¥ {{info.deposit/100 || 0}}</text>
						</view>
					</view>

					<view class="content-item">
						<text class="section-title">签约信息</text>
						<view class="sign-card" v-for="(item ,index) in contractList" :key="index">
							<view class="sign-header">
								<text class="card-title">第{{index+1}}次签约</text>
								<view class="detail-link">
									<text>查看详情</text>
									<uni-icons type="right" size="14" color="#999" />
								</view>
							</view>
							<view class="sign-content">
								<view class="list-item">
									<view class="item-label">租期<text class="status-tag info">{{item.diff}}个月</text>
									</view>
									<view class="item-value">
										<text>{{item.start_time}}~{{item.end_time }}</text>
									</view>
								</view>
								<view class="list-item">
									<text class="item-label">租金（元/月）</text>
									<text class="item-value">¥{{item.rent/100}}</text>
								</view>
								<view class="list-item">
									<text class="item-label">经办人</text>
									<text class="item-value">13632244771</text>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</scroll-view>

		<!-- 底部操作栏移到scroll-view外面 -->
		<view v-if="activeTab === 'detail'" class="footer">
			<button class="btn" @click="showMorePopup">更多</button>
			<button class="btn">换房</button>
			<button class="btn primary" @click="checkout">退租</button>
		</view>
		
		<view v-if="activeTab === 'bill'" class="footer">
			<button class="btn primary" @click="addBill">添加账单</button>
		</view>

	</view>
</template>
<script lang="ts" setup>
	import { ref, computed, reactive } from 'vue';
	import { onLoad } from "@dcloudio/uni-app"
	import { getNavBarHeight } from '../utils/system.js'
	import { dayjs } from "../../utils/dayjs.min.js"
	const db = uniCloud.databaseForJQL()
	const getTitleBarHeight = computed(() => getNavBarHeight())
	const activeTab = ref('detail');
	const room_id = ref('')
	const info = ref({})
	const contract = ref({})
	const tenant = ref({})
	const contractList = ref([])
	const billList = ref([])
	const accountList = ref([])
	const tenantList = ref([])
	const room_name = ref('')
	const paging = ref()
	const refresherTriggered = ref(false)
	// 流水数据
	const flowData = ref([
		{
			title: '账单1期',
			type: '支付宝付款',
			time: '06-01 13:24',
			amount: 1240.00
		},
		{
			title: '账单2期',
			type: '微信支付',
			time: '05-15 16:42',
			amount: 1260.00
		}
	]);


	onLoad((e) => {
		console.log("onLoad", e);
		if (e.room_id) {
			room_id.value = e.room_id
			uni.showLoading({
				title:"加载中"
			})
			getInfo()
			getContract()
			getBill()
			getFlow()
			getTenant()
			uni.hideLoading()
		}
	})

	const getInfo = async () => {
		let contractsCol = db.collection("fangke_contracts").field("start_time,end_time,rent,tenant_id,_id,status,isPaper").getTemp()
		let tenantCol = db.collection("fangke_tenants").field("name,_id").getTemp()
		const res = await db.collection("fangke_room", contractsCol, tenantCol).where(`_id == "${room_id.value}"`).get()
		console.log("房屋信息", res);
		if (res.data.length > 0) {
			tenant.value = res.data[0].tenant_id[0]
			info.value = res.data[0]
			room_name.value = info.value.building_name + "-" + info.value.name
			console.log("info :", info.value);
		}
	}
	
	const getContract = async()=>{
		let res = await db.collection("fangke_contracts").where(`room_id == "${room_id.value}"`).field("start_time,end_time,rent,tenant_id,_id,status,isPaper").get()
		console.log("获取合约",res);
		if (res.data.length > 0) {
			res.data.forEach(item => {
				console.log("item",item);
				let diff = dayjs(item.end_time).diff(item.start_time, 'month')
				console.log("相差几月：", diff);
				item.diff = diff
				if (item.status == 1) {
					contract.value = item
				}
			})
			contractList.value = res.data
		}
	}

	const showMorePopup = () => {
		uni.showActionSheet({
			itemList: ['续租', '删除租约', '修改租约'],
			success: (res) => {
				switch (res.tapIndex) {
					case 0:	//续租
						break
					case 1:		//删除租约
						break
					case 2:		//修改租约
						break
				}
			}
		});
	};

	const jumpBillInfo = (id) => {
		uni.navigateTo({
			url: "/pagesB/roomInfoBill/roomInfoBill?id=" + id
		})
	}
	
	const jumpAmountInfo = (item)=>{
		let data = {
			transactionId:item._id,
			amount:item.amount,
			name:item.name,
			room_name:item.room_name,
			tenant:item.tenant_name,
			bill_id:item.bill,
			time:item.paymentDate,
			payment:item.paymentMethod,
			handler:item.handler
		}
		uni.navigateTo({
			url: "/pagesB/amountInfo/amountInfo",
			success(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromOpenerPage', data)
			}
		})
	}

	const onRefresh = async () => {
		console.log("=== 下拉刷新被触发 ===");
		console.log("当前tab:", activeTab.value);
		
		refresherTriggered.value = true;
		
		// 显示加载提示
		uni.showToast({
			title: '正在刷新...',
			icon: 'loading',
			duration: 1000
		});
		
		try {
			switch (activeTab.value) {
				case "detail":
					console.log("刷新详情数据");
					await getContract()
					break
				case "bill":
					console.log("刷新账单数据");
					await getBill()
					break
				case "flow":
					console.log("刷新流水数据");
					await getFlow()
					break
				case "tenant":
					console.log("刷新租客数据");
					await getTenant()
					break
			}
			
			console.log("数据刷新完成");
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1000
			});
			
		} catch (error) {
			console.error("刷新数据失败:", error)
			uni.showToast({
				title: '刷新失败',
				icon: 'error',
				duration: 1000
			});
		} finally {
			// 结束刷新状态
			console.log("结束刷新状态");
			refresherTriggered.value = false;
		}
	}

	const onRestore = () => {
		console.log("刷新状态恢复");
		refresherTriggered.value = false;
	}

	const switchTab = (tab : string) => {
		activeTab.value = tab;

	};
	
	const addBill = ()=>{
		let period =[]
		billList.value.forEach(item =>{
			let time = item.startTime === ""?item.day:item.startTime+"-"+item.endTime
			let name = item.name + time
			period.push({
				name:name,
				id:item._id
			})
		})
		uni.navigateTo({
			url:"/pagesB/addBill/addBill?room_name="+room_name.value,
			success(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromOpenerPage', { data: period })
			}
		})
	}

	const getBill = async () => {
		await db.collection("fangke_room_bill").where(`room_id == "${room_id.value}"`).field("_id,day,sum,money,name,status,startTime,endTime").get().then(res => {
			console.log("获取订单", res);
			if (res.errCode == 0) {
				billList.value = res.data
			}

		})

	}

	const getFlow = async () => {
		await db.collection("fangke_room_account").where(`room_id == "${room_id.value}"`).get().then(res => {
			console.log("获取流水", res);
			if (res.errCode == 0) {
				accountList.value = res.data
			}
		})
	}

	const getTenant = async () => {
		await db.collection("fangke_tenants").where(`room_id == "${room_id.value}"`).field("name,phone,id_card,gender,_id").get().then(res => {
			console.log("获取租客", res);
			if (res.errCode == 0) {
				let list = res.data.filter(item => {
					if (item.name === tenant.value.name) {
						tenant.value.phone = item.phone
						tenant.value.id_card = item.id_card
						tenant.value.gender = item.gender
					}
					return item.name !== tenant.value.name
				})
				tenantList.value = list
			}
		})
	}

	const onBack = () => {
		uni.navigateBack();
	}

	const checkout = () => {
		let data = {
			room_name: room_name.value,
			name: tenant.value.name,
			phone: tenant.value.phone,
			time: contract.value.start_time + "~" + contract.value.end_time
		}
		uni.navigateTo({
			url: "/pagesB/checkout/checkout?data=" + encodeURIComponent(JSON.stringify(data)) +
				"&room_id=" + room_id.value
		})
	}

	const addRoomate = () => {
		uni.navigateTo({
			url: "/pagesB/addroomMate/addroomMate",
			events: {
				add: function (data) {
					console.log("add", data);
					data.room_id = room_id.value
					db.collection("fangke_tenants").add(data).then(res => {
						console.log("添加成功", res);
						tenantList.value.push(data)
					})
				}
			}
		})
	}

	const editRoommate = (item, index, type) => {
		uni.navigateTo({
			url: "/pagesB/addroomMate/addroomMate?item=" + encodeURIComponent(JSON.stringify(item)) +
				"&index=" + index,
			events: {
				editEvent: function (data, index) {
					console.log("edit", data);
					if ("_id" in data) {
						delete data._id
					}
					db.collection("fangke_tenants").update({ ...data }).then(res => {
						console.log("修改成功", res);
						if (type == 1) {
							Object.keys(data).forEach(key => {
								if (item.hasOwnProperty(key)) {
									tenant.value[key] = data[key]
								}
							})
							console.log("修改后的tenant", tenant.value);
						} else {
							tenantList.value[index] = data
						}

					})
				}
			}
		})
	}

	const removeCoTenant = (id : string, index : number) => {
		// 删除同住人逻辑
		db.collection("fangke_tenants").doc(id).remove().then(res => {
			console.log("删除成功", res);
			tenantList.value.splice(index)
		})
	};
</script>
<style scoped>
	page {
		background-color: #F5F6F7;
	}

	.page {
		min-height: 100vh;
		background-color: #F5F6F7;
		display: flex;
		flex-direction: column;
	}

	.header {
		flex-shrink: 0;
		padding: 0 32rpx;
		color: #fff;
		background: linear-gradient(180deg, #00C389 0%, #00B087 100%);
	}


	.bar {
		display: flex;
		align-items: flex-end;
		justify-content: flex-start;
		padding-bottom: 10rpx;
	}

	.nav-left {
		display: flex;
		align-items: center;
	}

	.room-info {
		padding: 20rpx 0 40rpx;
	}

	.room-number {
		font-size: 24px;
		font-weight: 600;
		margin-bottom: 16rpx;
		display: block;
	}

	.lease-info {
		font-size: 14px;
		opacity: 0.9;
	}

	.lease-date {
		margin-right: 20rpx;
	}

	.tab-bar {
		flex-shrink: 0;
		display: flex;
		background: #fff;
		padding: 0 32rpx;
		height: 88rpx;
		border-radius: 24rpx 24rpx 0 0;
	}

	.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		color: #666;
		position: relative;
	}

	.tab-item.active {
		color: #00C389;
		font-weight: 500;
	}

	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		width: 32rpx;
		height: 4rpx;
		background: #00C389;
		border-radius: 2rpx;
	}

	.scroll-content {
		flex: auto;
		height: calc(100vh - 300rpx);
		padding-bottom: 100rpx;
	}

	.content {
		background: #F5F6F7;
		box-sizing: border-box;
		padding-bottom: 40rpx;
	}

	.content-title {
		font-size: 16px;
		color: #333;
		margin: 24rpx;
		display: block;
		font-weight: bolder;
	}

	.content-item {
		display: flex;
		flex-direction: column;
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		gap: 15rpx;
	}

	/* 通用卡片样式 */
	.content-card {
		display: flex;
		background: #fff;
		border-radius: 16rpx;
		margin: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		flex-direction: row;
		padding: 20rpx;
	}

	.content-card:last-child {
		margin-bottom: 0;
	}

	/* 通用标题样式 */
	.section-title {
		font-size: 16px;
		color: #333;
		display: block;
		font-weight: bolder;
	}

	/* 通用头部样式 */
	.card-header {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-bottom: 10rpx;
	}

	.card-title {
		display: flex;
		width: 170rpx;
		font-size: 16px;
		font-weight: bold;
		color: #333;
		justify-content: center;
	}

	/* 通用列表项样式 */
	.list-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex: 1;
	}

	.list-item:last-child {
		margin-bottom: 0;
	}


	/* 通用状态标签 */
	.status-tag {
		font-size: 14px;
		padding: 4rpx 16rpx;
		border-radius: 4rpx;
		margin: 10rpx;
	}

	.status-tag.success {
		color: #00C389;
		background: rgba(0, 195, 137, 0.1);
	}

	.status-tag.warning {
		color: orange;
		background: rgba(255, 107, 107, 0.1);
	}

	.status-tag.err {
		color: red;
		background: rgba(255, 107, 107, 0.1);
	}

	.status-tag.info {
		background: rgba(0, 195, 137, 0.1);
		color: #00C389;
		font-size: 12px;
	}

	/* 通用按钮样式 */
	.action-btn {
		width: 160rpx;
		height: 72rpx;
		line-height: 72rpx;
		font-size: 14px;
		margin: 0;
		background: #F5F6F7;
		color: #666;
		border-radius: 36rpx;
	}

	.action-btn.primary {
		background: #00C389;
		color: #fff;
	}

	/* 通用分割线 */
	.divider {
		border-left: 1px solid #EBEDF0;
		padding-left: 10rpx;
		padding-right: 10rpx;
		flex: 1;
		display: flex;
		gap: 10rpx;
		flex-direction: column;
	}

	/* 通用金额样式 */
	.amount {
		font-size: 16px;
		font-weight: 500;
	}

	.amount.income {
		color: #FF6B6B;
	}

	.amount.expense {
		color: #00C389;
	}

	.amount.large {
		font-size: 18px;
		font-weight: 600;
	}

	/* 通用操作区域 */
	.actions {
		display: flex;
		justify-content: flex-end;
		gap: 16rpx;
		margin-top: 24rpx;
	}

	/* 特殊布局 - 租客信息 */
	.tenant-section {
		margin-bottom: 40rpx;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.co-tenant-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #F8F9FA;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
	}

	.co-tenant-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.co-tenant-actions {
		display: flex;
		gap: 24rpx;
	}

	/* 特殊布局 - 签约卡片 */
	.sign-card {
		background: #fff;
		padding: 20rpx;
		border-radius: 12rpx;
		border: 1px solid #EBEDF0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		
	}


	.sign-header {
		display: flex;
		padding: 18rpx;
		border-bottom: 1px solid #EBEDF0;
		margin-bottom: 0;
		justify-content: space-between;

	}

	.sign-content {
		padding: 24rpx;
	}

	.detail-link {
		display: flex;
		align-items: center;
		color: #999;
		font-size: 14px;
	}

	/* 底部操作栏 */
	.footer {
		flex-shrink: 0;
		display: flex;
		padding: 16rpx 32rpx;
		background: #fff;
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
	}

	.btn {
		flex: 1;
		margin: 0 8rpx;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 16px;
		background: #F5F6F7;
		color: #333;
	}

	.btn.primary {
		background: #00C389;
		color: #fff;
	}


	.flow-content {
		display: flex;
		gap: 24rpx;
		width: 100%;
	}

	.flow-item {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}


	.tenant-list {
		padding: 32rpx;
	}


	.tenant-item {
		display: flex;
		margin-bottom: 24rpx;
		align-items: center;
	}

	.tenant-item:last-child {
		margin-bottom: 0;
	}

	.item-label {
		width: 200rpx;
		color: #666;
		font-size: 14px;
		flex-shrink: 0;
	}

	.item-label.small {
		width: 140rpx;
	}

	.item-value {
		color: #333;
		font-size: 14px;
	}

	.item-value.large {
		font-size: 35rpx;
		font-weight: bold;
	}

	.co-tenant-list {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	.co-tenant-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.co-tenant-info text {
		font-size: 14px;
		color: #333;
	}

	.co-tenant-actions {
		display: flex;
		gap: 24rpx;
	}
</style>