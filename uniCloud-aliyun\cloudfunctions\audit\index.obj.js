// 审核管理云函数
const db = uniCloud.database()
let dbJQL = uniCloud.databaseForJQL()

module.exports = {
	_before: function () {
		// 统一预处理器
		this.startTime = Date.now()
		// 身份验证
		if (!this.getUniIdToken()) {
			throw new Error('未登录')
		}
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		this.currentUser = this.getUniIdToken()
	},

	/**
	 * 获取审核列表
	 * @param {Object} params 查询参数
	 * @param {String} params.landlord_id 房东ID
	 * @param {String} params.type 审核类型
	 * @param {Number} params.status 审核状态
	 * @param {Number} params.page 页码
	 * @param {Number} params.limit 每页数量
	 */
	async getAuditList(params = {}) {
		try {
			const {
				landlord_id,
				type,
				status,
				page = 1,
				limit = 20
			} = params

			// 构建查询条件
			let whereCondition = {}
			
			if (landlord_id) {
				whereCondition.landlord_id = landlord_id
			}
			
			if (type && type !== 'all') {
				whereCondition.type = type
			}
			
			if (status !== undefined && status !== 'all') {
				whereCondition.status = status
			}

			// 查询数据
			const res = await dbJQL.collection('fangke_audit_records')
				.where(whereCondition)
				.orderBy('create_time', 'desc')
				.skip((page - 1) * limit)
				.limit(limit)
				.get()

			// 获取总数
			const countRes = await dbJQL.collection('fangke_audit_records')
				.where(whereCondition)
				.count()

			return {
				errCode: 0,
				errMsg: 'success',
				data: {
					list: res.data,
					total: countRes.total,
					page,
					limit
				}
			}
		} catch (error) {
			console.error('获取审核列表失败:', error)
			return {
				errCode: -1,
				errMsg: error.message || '获取审核列表失败'
			}
		}
	},

	/**
	 * 创建审核申请
	 * @param {Object} auditData 审核数据
	 */
	async createAuditRecord(auditData) {
		try {
			const {
				type,
				title,
				description,
				applicant_id,
				applicant_name,
				applicant_phone,
				landlord_id,
				building_id,
				room_id,
				room_info,
				attachments = [],
				related_data = {},
				priority = 2
			} = auditData

			// 验证必填字段
			if (!type || !title || !applicant_id || !landlord_id || !room_id) {
				throw new Error('缺少必填参数')
			}

			// 创建审核记录
			const auditRecord = {
				type,
				title,
				description,
				applicant_id,
				applicant_name,
				applicant_phone,
				landlord_id,
				building_id,
				room_id,
				room_info,
				status: 0, // 待审核
				priority,
				attachments,
				related_data,
				create_time: new Date(),
				update_time: new Date()
			}

			const res = await dbJQL.collection('fangke_audit_records').add(auditRecord)

			// 发送通知给房东（这里可以集成消息推送）
			await this.sendAuditNotification(landlord_id, auditRecord)

			return {
				errCode: 0,
				errMsg: 'success',
				data: {
					audit_id: res.id
				}
			}
		} catch (error) {
			console.error('创建审核申请失败:', error)
			return {
				errCode: -1,
				errMsg: error.message || '创建审核申请失败'
			}
		}
	},

	/**
	 * 处理审核
	 * @param {Object} params 审核参数
	 * @param {String} params.audit_id 审核ID
	 * @param {Number} params.status 审核状态 1-通过 2-拒绝
	 * @param {String} params.audit_remark 审核备注
	 */
	async processAudit(params) {
		try {
			const {
				audit_id,
				status,
				audit_remark = ''
			} = params

			// 验证参数
			if (!audit_id || ![1, 2].includes(status)) {
				throw new Error('参数错误')
			}

			// 获取审核记录
			const auditRes = await dbJQL.collection('fangke_audit_records')
				.doc(audit_id)
				.get()

			if (!auditRes.data || auditRes.data.length === 0) {
				throw new Error('审核记录不存在')
			}

			const auditRecord = auditRes.data[0]

			// 检查权限
			if (auditRecord.landlord_id !== this.currentUser.uid) {
				throw new Error('无权限操作')
			}

			// 检查状态
			if (auditRecord.status !== 0) {
				throw new Error('该申请已处理')
			}

			// 更新审核状态
			const updateData = {
				status,
				audit_user_id: this.currentUser.uid,
				audit_time: new Date(),
				audit_remark,
				update_time: new Date()
			}

			await dbJQL.collection('fangke_audit_records')
				.doc(audit_id)
				.update(updateData)

			// 根据审核类型执行相应的后续操作
			await this.handleAuditResult(auditRecord, status)

			// 发送通知给申请人
			await this.sendAuditResultNotification(auditRecord.applicant_id, auditRecord, status, audit_remark)

			return {
				errCode: 0,
				errMsg: 'success',
				data: {
					audit_id,
					status
				}
			}
		} catch (error) {
			console.error('处理审核失败:', error)
			return {
				errCode: -1,
				errMsg: error.message || '处理审核失败'
			}
		}
	},

	/**
	 * 获取审核统计
	 * @param {String} landlord_id 房东ID
	 */
	async getAuditStats(landlord_id) {
		try {
			if (!landlord_id) {
				throw new Error('缺少房东ID')
			}

			// 获取各状态的统计数据
			const pendingRes = await dbJQL.collection('fangke_audit_records')
				.where({
					landlord_id,
					status: 0
				})
				.count()

			const approvedRes = await dbJQL.collection('fangke_audit_records')
				.where({
					landlord_id,
					status: 1
				})
				.count()

			const rejectedRes = await dbJQL.collection('fangke_audit_records')
				.where({
					landlord_id,
					status: 2
				})
				.count()

			// 获取今日处理数量
			const today = new Date()
			today.setHours(0, 0, 0, 0)
			
			const todayProcessedRes = await dbJQL.collection('fangke_audit_records')
				.where({
					landlord_id,
					status: db.command.in([1, 2]),
					audit_time: db.command.gte(today)
				})
				.count()

			// 按类型统计待审核数量
			const typeStats = {}
			const types = ['utility', 'maintenance', 'cleaning', 'checkin', 'checkout', 'renewal', 'transfer']
			
			for (const type of types) {
				const typeRes = await dbJQL.collection('fangke_audit_records')
					.where({
						landlord_id,
						type,
						status: 0
					})
					.count()
				typeStats[type] = typeRes.total
			}

			return {
				errCode: 0,
				errMsg: 'success',
				data: {
					pending: pendingRes.total,
					approved: approvedRes.total,
					rejected: rejectedRes.total,
					todayProcessed: todayProcessedRes.total,
					typeStats
				}
			}
		} catch (error) {
			console.error('获取审核统计失败:', error)
			return {
				errCode: -1,
				errMsg: error.message || '获取审核统计失败'
			}
		}
	},

	/**
	 * 处理审核结果的后续操作
	 * @param {Object} auditRecord 审核记录
	 * @param {Number} status 审核状态
	 */
	async handleAuditResult(auditRecord, status) {
		try {
			const { type, related_data } = auditRecord

			if (status === 1) { // 审核通过
				switch (type) {
					case 'utility':
						// 水电表审核通过，更新相关记录状态
						if (related_data.utility_records) {
							for (const recordId of related_data.utility_records) {
								await dbJQL.collection('fangke_utility_records')
									.doc(recordId)
									.update({
										is_submit: true,
										audit_status: 1,
										update_time: new Date()
									})
							}
						}
						break
					case 'checkin':
						// 入住申请通过，更新房间状态
						await dbJQL.collection('fangke_room')
							.doc(auditRecord.room_id)
							.update({
								rent_status: 1, // 已出租
								tenant_id: auditRecord.applicant_id,
								update_time: new Date()
							})
						break
					case 'checkout':
						// 退租申请通过，更新房间状态
						await dbJQL.collection('fangke_room')
							.doc(auditRecord.room_id)
							.update({
								rent_status: 0, // 空闲
								tenant_id: null,
								update_time: new Date()
							})
						break
					// 其他类型的后续处理...
				}
			}
		} catch (error) {
			console.error('处理审核结果失败:', error)
		}
	},

	/**
	 * 发送审核通知
	 * @param {String} userId 用户ID
	 * @param {Object} auditRecord 审核记录
	 */
	async sendAuditNotification(userId, auditRecord) {
		// 这里可以集成uni-im或其他消息推送服务
		console.log('发送审核通知:', userId, auditRecord.title)
	},

	/**
	 * 发送审核结果通知
	 * @param {String} userId 用户ID
	 * @param {Object} auditRecord 审核记录
	 * @param {Number} status 审核状态
	 * @param {String} remark 审核备注
	 */
	async sendAuditResultNotification(userId, auditRecord, status, remark) {
		// 这里可以集成uni-im或其他消息推送服务
		const statusText = status === 1 ? '通过' : '拒绝'
		console.log('发送审核结果通知:', userId, `${auditRecord.title} 审核${statusText}`)
	}
}
