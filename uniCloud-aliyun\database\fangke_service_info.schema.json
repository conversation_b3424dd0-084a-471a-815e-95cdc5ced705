// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "增值服务信息",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"create_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"update_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"name":{
			"bsonType": "string",
			"label": "服务名称"
		},
		"price":{
			"bsonType": "int",
			"label": "价格",
			"defaultValue":0
		},
		"prebook":{
			"bsonType": "bool",
			"label": "是否需要预约",
			"defaultValue":false
		},
		"type":{
			"bsonType": "int",
			"label": "服务类型",
			"defaultValue":0,
			"description": "0.添加房间，1.电子合同 2.短信充值 3.巡房管理 4.维修服务 5.保洁服务 6.智能门锁"
		},
		"valid_date":{
			"bsonType": "int",
			"label": "有效时间/月",
			"defaultValue":0,
			"description": "有效时间按月份计算"
		},
		"unit":{
			"bsonType": "string",
			"label": "单位",
			"defaultValue":""
		},
		"agreement":{
			"bsonType": "string",
			"label": "链接",
			"defaultValue":"",
			"description": "协议链接"
		},
		"service_type":{
			"bsonType": "array",
			"label": "服务项目类型",
			"arrayType": "string"
		},
		"detail":{
			"bsonType": "string",
			"label": "详情",
			"defaultValue":"",
			"description": "服务详情的内容html格式"
		},
		"rule":{
			"bsonType": "string",
			"label": "服务规则",
			"defaultValue":"",
			"description": "服务规则的内容html格式"
		},
		"tip":{
			"bsonType": "string",
			"label": "温馨提示",
			"defaultValue":"",
			"description": "温馨提示的内容html格式"
		}
		
	}
}