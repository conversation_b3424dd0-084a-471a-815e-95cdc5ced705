{"name": "uni-cloud-router", "version": "0.0.11", "description": "", "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "jest", "build": "rollup -c", "lint": "eslint --ext .ts src __tests__", "format": "prettier --write --parser typescript \"src/**/*.ts?(x)\" \"__tests__/**/*.ts?(x)\""}, "keywords": [], "author": "fxy060608", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/fxy060608/uni-cloud-router.git"}, "devDependencies": {"@cloudbase/database": "^1.2.2", "@rollup/plugin-node-resolve": "^9.0.0", "@types/debug": "^4.1.5", "@types/koa": "^2.11.4", "@types/koa-compose": "^3.2.5", "@types/node": "^14.10.2", "@typescript-eslint/parser": "^4.1.1", "eslint": "^7.9.0", "jest": "^26.4.2", "koa-compose": "^4.1.0", "noader": "^3.1.1", "path-to-regexp": "^6.1.0", "prettier": "^2.1.2", "rollup": "^2.27.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.27.2", "ts-jest": "^26.3.0", "typescript": "^4.0.2", "urllib": "^2.36.1"}, "dependencies": {}}