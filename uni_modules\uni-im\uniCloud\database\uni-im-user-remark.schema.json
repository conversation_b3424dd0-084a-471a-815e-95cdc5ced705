{"bsonType": "object", "description": "对用户的备注信息", "required": ["user_id", "to_uid", "content"], "permission": {"read": "doc.user_id == auth.uid", "create": true, "update": "doc.user_id == auth.uid", "delete": "doc.user_id == auth.uid"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"description": "设置备注的用户ID", "bsonType": "string", "forceDefaultValue": {"$env": "uid"}}, "target_uid": {"description": "被设置备注的用户ID", "bsonType": "string"}, "content": {"description": "备注内容", "bsonType": "string"}, "create_time": {"description": "创建时间", "bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}, "update_time": {"description": "更新时间", "bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}}}