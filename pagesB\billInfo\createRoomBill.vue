<template>
	<view class="container">
		<uni-section title="账单信息" type="line"></uni-section>
		<view class="u_forms">
			<text>租客:{{info.building_name+" "+info.name+"房"}}</text>
			<text>{{time}}</text>
		</view>
		<lkj-collapse :collapse-data="contentData"></lkj-collapse>
		<view style="display: flex;justify-content: space-around;margin-top: 10rpx;">
			<button type="primary">一键生成账单</button>
			<button type="primary">自定义账单</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import uniCloud from '@dcloudio/uni-cloud';

	const api = uniCloud.importObject("ApiFuntion");
	const db = uniCloud.databaseForJQL();

	// 响应式数据
	const info = ref({});
	const mode = ref({});
	const room_id = ref('');
	const contentData = ref([]);
	const room = ref('10巷');
	const num = ref(101);
	const time = ref("2024年12月12日");

	// 获取 URL 参数
	const route = useRoute();
	const {
		uid
	} = route.query;

	// 页面加载时处理参数
	onMounted(() => {
		if (uid) {
			room_id.value = uid;
			getRoomInfo();
		}
	});

	// 获取房间信息
	const getRoomInfo = async () => {
		await api.getRoomInfo(room_id.value).then(res => {
			if (res.code) {
				info.value = res.data;
			}
		});
		await getMode();
	};

	// 获取价格模式
	const getMode = () => {
		if (info.value.mode_id) {
			db.collection("fangke_price_model").doc(info.value.mode_id).get().then(res => {
				mode.value = res.data[0];
			}).finally(res => {
				updateInfo();
			});
		}
	};

	// 更新费用信息
	const updateInfo = () => {
		console.log('getRoomInfo', info.value);
		console.log("getModel", mode.value);
		let real_ele = info.value.current_ele - info.value.electricity;

		contentData.value = [{
				title: "电费",
				list: true,
				bold: true,
				right: "￥66",
				content: {
					body: [{
							name: '上月电表',
							value: info.value.electricity / 100,
						},
						{
							name: '本月电表',
							value: info.value.current_ele / 100,
						},
						{
							name: '实用(单价/度:' + parseFloat(mode.value.ele_p / 100) + ")",
							value: (info.value.current_ele - info.value.electricity) / 100 + '度',
						},
						{
							name: '电力设备损耗(单价/度:' + parseFloat(mode.value.ele_p / 100) + ')',
							value: '2度',
						},
						{
							name: '合计',
							value: '￥66',
						},
					]
				}
			},
			{
				title: "水费",
				list: true,
				bold: true,
				right: "￥66",
				content: {
					body: [{
							name: '上月水表',
							value: info.value.water / 100,
						},
						{
							name: '本月水表',
							value: info.value.current_water / 100,
						},
						{
							name: '实用(单价/度:' + parseFloat(mode.value.water_p / 100) + ')',
							value: '42',
						},
						{
							name: '损耗(电力设备损耗:0.62)',
							value: '0.88',
						},
						{
							name: '合计',
							value: '￥66',
						},
					]
				}
			},
			{
				title: "房租",
				list: true,
				bold: true,
				right: "￥500",
				content: {
					body: [{
							name: '租房时间(2024/12/12-2025/1/12)',
							value: '1个月',
						},
						{
							name: '每月房租',
							value: '500',
						},
						{
							name: '合计',
							value: '￥500',
						},
					]
				}
			},
			{
				title: "其他费用",
				list: true,
				bold: true,
				right: "￥20",
				content: {
					body: [{
							name: '卫生费',
							value: '20',
						},
						{
							name: '合计',
							value: '￥20',
						},
					]
				}
			},
		];
	};
</script>

<style lang="scss">
	.container {
		box-sizing: border-box;
		padding: 20rpx;
		display: flex;
		flex-direction: column;

		.u_forms {
			display: flex;
			padding-left: 20rpx;
			padding-right: 20rpx;
			justify-content: space-between;
		}

	}
</style>