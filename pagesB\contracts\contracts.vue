<template>
	<view class="container">

		<!-- 状态筛选 -->
		<view class="status-tabs">
			<uni-data-select v-model="currentStatus" :localdata="statusSelectData" placeholder="请选择状态"
				@change="onStatusChange" class="status-select"></uni-data-select>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-bar">
			<view class="filter-item" @click="showRoomFilter">
				<text class="filter-text">{{ selectedRoom }}</text>
				<uni-icons type="down" size="12" color="#01B862"></uni-icons>
			</view>
			<view class="filter-item" @click="showTimeFilter">
				<text class="filter-text">{{ timeFilterText }}</text>
				<uni-icons type="down" size="12" color="#01B862"></uni-icons>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-bar">
			<view class="stat-item">
				<text class="stat-number">{{ totalCount }}</text>
				<text class="stat-label">全部（份）</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ expiringSoonCount }}</text>
				<text class="stat-label">即将到期（份）</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ electronicCount }}</text>
				<text class="stat-label">电子合同（份）</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ paperCount }}</text>
				<text class="stat-label">纸质合同（份）</text>
			</view>
		</view>
		
		<!-- 合同列表 -->
		<view class="contract-list">
			<text class="list-title">共{{ filteredContracts.length }}条记录</text>

			<view v-for="(contract, index) in filteredContracts" :key="index" class="contract-item">
				<view class="contract-header">
					<text class="contract-title">{{ contract.roomName }}</text>
					<view class="contract-status" :class="getStatusClass(contract.status)">
						{{ getStatusText(contract.status) }}
					</view>
				</view>
				<view class="contract-info">
					<text class="info-text">租期：{{ contract.startDate }} ~ {{ contract.endDate }}</text>
				</view>
				<view class="contract-info">
					<text class="info-text">签约人：{{ contract.tenant }}</text>
				</view>
			</view>
		</view>

		<!-- 房源筛选弹窗 -->
		<uni-popup ref="roomPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择房源</text>
					<view class="popup-close" @click="closeRoomFilter">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>
				<view class="room-list">
					<view v-for="(room, index) in roomList" :key="index" class="room-item"
						:class="{ selected: selectedRoomIndex === index }" @click="selectRoom(index)">
						{{ room }}
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 时间筛选组件 -->
		<DateRangeSelector ref="dateRangeSelector" :startDate="startDate" :endDate="endDate"
			@confirm="onDateRangeConfirm" @cancel="onDateRangeCancel" />
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		dayjs
	} from '../../utils/dayjs.min.js'
	import DateRangeSelector from '../components/DateRangeSelector/DateRangeSelector.vue'
	const buildingList = ref([])
	const building = ref([])	//房源对象
	const db = uniCloud.databaseForJQL()
	const contract_all = ref([])	//全部
	const contract_1 = ref([])	//待租客确认
	const contract_2 = ref([])	//签约成功
	const contract_3 = ref([])	//续租待确认
	const contract_4 = ref([])//即将到期
	const contract_5 = ref([])//已逾期
	onMounted(() => {
		console.log("onMounted");
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
				console.log('acceptDataFromOpenerPage', data)
				building.value = data
				data.forEach(item =>{
					buildingList.value.push(item.id)
				})
				getContract()
			})
	})
	// 状态列表
	const statusList = ref([{
			name: '全部状态',
			value: 'all'
		},
		{
			name: '待租客确认',
			value: 1
		},
		{
			name: '签约成功',
			value: 2
		},
		{
			name: '续租待确认',
			value: 3
		},
		{
			name: '即将到期',
			value: 4
		},
		{
			name: '已逾期',
			value: 5
		}
	])

	// uni-data-select 数据格式
	const statusSelectData = ref([{
			value: 0,
			text: '全部状态'
		},
		{
			value: 1,
			text: '待租客确认'
		},
		{
			value: 2,
			text: '签约成功'
		},
		{
			value: 3,
			text: '续租待确认'
		},
		{
			value: 4,
			text: '即将到期'
		},
		{
			value: 5,
			text: '已逾期'
		}
	])
	

	// 当前选中状态
	const currentStatus = ref(0)

	// 房源列表 - 使用building数组
	const roomList = computed(() => {
		const rooms = ['全部房源']
		// 从building数组中获取房源名称
		const buildingNames = building.value.map(item => item.name)
		return rooms.concat(buildingNames)
	})
	const selectedRoomIndex = ref(0)
	const selectedRoom = computed(() => roomList.value[selectedRoomIndex.value])

	// 获取选中房源的ID
	const selectedBuildingId = computed(() => {
		if (selectedRoomIndex.value === 0) {
			return null // 全部房源
		}
		return building.value[selectedRoomIndex.value - 1]?.id || null
	})

	// 时间筛选
	const startDate = ref('')
	const endDate = ref('')

	// 时间筛选显示文本
	const timeFilterText = computed(() => {
		if (startDate.value && endDate.value) {
			return `${startDate.value} ~ ${endDate.value}`
		}
		return '请选择租约起止日期'
	})

	// 弹窗引用
	const roomPopup = ref(null)
	const dateRangeSelector = ref(null)

	// 合同数据 - 使用真实数据，添加buildingId字段
	const contracts = computed(() => {
		return contract_all.value.map(contract => ({
			id: contract._id,
			roomName: contract.room_name,
			startDate: contract.start_time,
			endDate: contract.end_time,
			tenant: contract.tenant_id, // 这里可能需要根据实际情况获取租客姓名
			status: contract.status,
			rent: contract.rent,
			deposit: contract.deposit,
			payment: contract.payment,
			isPaper: contract.isPaper,
			handler: contract.handler,
			buildingId: contract.building_id // 添加房源ID字段
		}))
	})

	// 统计数据 - 根据真实数据计算
	const totalCount = computed(() => contract_all.value.length)
	const expiringSoonCount = computed(() => {
		const now = dayjs()
		const thirtyDaysLater = now.add(30, 'day')
		
		return contract_all.value.filter(contract => {
			const endDate = dayjs(contract.end_time)
			return endDate.isBetween(now, thirtyDaysLater, null, '[]') && contract.status === 2
		}).length
	})
	const electronicCount = computed(() => contract_all.value.filter(contract => !contract.isPaper).length)
	const paperCount = computed(() => contract_all.value.filter(contract => contract.isPaper).length)

	// 筛选后的合同列表
	const filteredContracts = computed(() => {
		let filtered = contracts.value

		// 根据状态筛选
		if (currentStatus.value > 0) {
			filtered = filtered.filter(contract => contract.status === currentStatus.value)
		}

		// 根据房源筛选
		if (selectedBuildingId.value) {
			filtered = filtered.filter(contract => contract.buildingId === selectedBuildingId.value)
		}

		// 根据时间筛选
		if (startDate.value && endDate.value) {
			filtered = filtered.filter(contract => {
				const contractStart = dayjs(contract.startDate)
				const contractEnd = dayjs(contract.endDate)
				const filterStart = dayjs(startDate.value)
				const filterEnd = dayjs(endDate.value)

				return contractStart.isBetween(filterStart, filterEnd, null, '[]') ||
					contractEnd.isBetween(filterStart, filterEnd, null, '[]')
			})
		}

		return filtered
	})


	// 状态选择器变化事件
	const onStatusChange = (value) => {
		currentStatus.value = value
		console.log("onStatusChange",value);
	}

	const showRoomFilter = () => {
		roomPopup.value.open()
	}

	const closeRoomFilter = () => {
		roomPopup.value.close()
	}

	const selectRoom = (index) => {
		selectedRoomIndex.value = index
		closeRoomFilter()
	}

	const showTimeFilter = () => {
		dateRangeSelector.value.open()
	}

	// 时间范围选择确认回调
	const onDateRangeConfirm = (result) => {
		startDate.value = result.startDate
		endDate.value = result.endDate
	}

	// 时间范围选择取消回调
	const onDateRangeCancel = () => {
		// 可以在这里处理取消操作
	}

	const getStatusClass = (status) => {
		const statusMap = {
			1: 'status-pending',      // 待租客确认
			2: 'status-signed',       // 签约成功
			3: 'status-renewal',      // 续租待确认
			4: 'status-expiring',     // 即将到期
			5: 'status-expired'       // 已逾期
		}
		return statusMap[status] || 'status-default'
	}

	const getStatusText = (status) => {
		const statusMap = {
			1: '待租客确认',
			2: '签约成功',
			3: '续租待确认',
			4: '即将到期',
			5: '已逾期'
		}
		return statusMap[status] || '未知状态'
	}

	onLoad((e) => {
		// 页面加载时的初始化
		if (e.uid) {
			
		}
		
	})
	
	const getContract = async()=>{
		console.log("buildingList.value",buildingList.value);
		await db.collection("fangke_contracts").where(
			`building_id in ['${buildingList.value.join("','")}']`).get().then(res => {
			console.log("获取所有合同", res.data);
			contract_all.value = res.data
			
			// 清空分类数组
			contract_1.value = []
			contract_2.value = []
			contract_3.value = []
			contract_4.value = []
			contract_5.value = []
			
			// 计算即将到期的合同
			const now = dayjs()
			const thirtyDaysLater = now.add(30, 'day')
			
			contract_all.value.forEach(item => {
				const endDate = dayjs(item.end_time)
				const isExpiringSoon = endDate.isBetween(now, thirtyDaysLater, null, '[]')
				const isExpired = endDate.isBefore(now)
				
				// 根据实际情况更新状态
				if (isExpired && item.status === 2) {
					// 如果合同已过期但状态还是签约成功，更新为已过期
					item.status = 5
				} else if (isExpiringSoon && item.status === 2) {
					// 如果合同即将到期但状态还是签约成功，更新为即将到期
					item.status = 4
				}
				
				// 分类存储
				switch(item.status) {
					case 1:
						contract_1.value.push(item)
						break
					case 2:
						contract_2.value.push(item)
						break
					case 3:
						contract_3.value.push(item)
						break
					case 4:
						contract_4.value.push(item)
						break
					case 5:
						contract_5.value.push(item)
						break
				}
			})
			
			console.log("分类后的合同数据:", {
				待租客确认: contract_1.value.length,
				签约成功: contract_2.value.length,
				续租待确认: contract_3.value.length,
				即将到期: contract_4.value.length,
				已逾期: contract_5.value.length
			})
		})
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f6f7;
		min-height: 100vh;

		.status-tabs {
			background-color: #fff;
			padding: 30rpx;

			.status-select {
				width: 100%;

				.uni-select {
					border: 1rpx solid #e5e5e5;
					border-radius: 12rpx;
					background-color: #fff;

					.uni-select__input-text {
						font-size: 28rpx;
						color: #333;
						padding: 20rpx 30rpx;
					}

					.uni-select__selector {
						padding: 20rpx 30rpx;
					}
				}

				.uni-select--active {
					border-color: #01B862;
				}
			}
		}

		.filter-bar {
			display: flex;
			padding: 30rpx;
			background-color: #fff;
			margin-top: 20rpx;

			.filter-item {
				display: flex;
				align-items: center;
				margin-right: 60rpx;

				.filter-text {
					font-size: 28rpx;
					color: #01B862;
					margin-right: 10rpx;
				}
			}
		}

		.stats-bar {
			display: flex;
			justify-content: space-around;
			padding: 40rpx 20rpx;
			background-color: #fff;
			margin-top: 20rpx;

			.stat-item {
				text-align: center;

				.stat-number {
					display: block;
					font-size: 48rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 10rpx;
				}

				.stat-label {
					font-size: 24rpx;
					color: #999;
				}
			}
		}

		.contract-list {
			padding: 30rpx;

			.list-title {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 30rpx;
			}

			.contract-item {
				background-color: #fff;
				border-radius: 16rpx;
				padding: 30rpx;
				margin-bottom: 20rpx;

				.contract-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.contract-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #333;
					}

					.contract-status {
						padding: 8rpx 16rpx;
						border-radius: 20rpx;
						font-size: 24rpx;

						&.status-pending {
							background-color: #e8f5e8;
							color: #01B862;
						}

						&.status-signed {
							background-color: #e3f2fd;
							color: #2196f3;
						}

						&.status-renewal {
							background-color: #fff3e0;
							color: #ff9800;
						}

						&.status-expiring {
							background-color: #ffebee;
							color: #f44336;
						}

						&.status-expired {
							background-color: #fce4ec;
							color: #e91e63;
						}
					}
				}

				.contract-info {
					margin-bottom: 10rpx;

					.info-text {
						font-size: 28rpx;
						color: #666;
					}
				}
			}
		}

		.popup-content {
			background-color: #fff;
			border-radius: 20rpx 20rpx 0 0;
			padding: 40rpx;
			max-height: 80vh;

			.popup-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 40rpx;

				.popup-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
				}
			}

			.room-list {
				.room-item {
					padding: 30rpx 0;
					border-bottom: 1rpx solid #eee;
					font-size: 28rpx;
					color: #333;

					&.selected {
						color: #01B862;
					}

					&:last-child {
						border-bottom: none;
					}
				}
			}
		}
	}
</style>