<template>
	<view class="layout">
		<view class="navbar" :class="{enableColor:isTransparent}">
			<view class="statusBar" :style="{height:getStatusBarHeight+'px'}"></view>
			<view class="titleBar" :style="{height:getTitleBarHeight+'px',marginRight:getLeftIconLeft+'px'}">
				<uni-icons type="back" size="20" v-if="hasBack" class="left" @click="handleLeft"></uni-icons>
				<view class="content">
					<view class="title">{{title}}</view>
					<view v-if="showRight" @click="handleRight" class="right">
						<uni-icons class="icon" :type="rightIconType" color="#888" size="16" style="margin-right: 10rpx;"></uni-icons>
						<text>{{rightText}}</text>
					</view>
				</view>
				
			</view>
		</view>
		<view class="fill" :style="{height:getNavBarHeight+'px'}">
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue';

	const props = defineProps({
		title: {
			type: String,
			default: ""
		},
		hasBack: {
			type: Boolean,
			default: false
		},
		showRight: {
			type: Boolean,
			default: false
		},
		rightIconType:String,
		rightText:String,
		isTransparent:false
	});
	const emit = defineEmits(['onLeft','onRight'])
	
	const handleLeft = ()=>{
		emit("onLeft")
	}
	
	const handleRight = ()=>{
		emit("onRight")
	}

	// 计算状态栏高度
	const getStatusBarHeight = computed(() =>  uni.getSystemInfoSync().statusBarHeight || 15);

	// 计算标题栏高度
	const getTitleBarHeight = computed(() => {
		if (uni.getMenuButtonBoundingClientRect) {
			const {
				top,
				height
			} = uni.getMenuButtonBoundingClientRect();
			return height + (top - getStatusBarHeight.value) * 2;
		} else {
			return 40;
		}
	});

	// 计算导航栏总高度
	const getNavBarHeight = computed(() => getStatusBarHeight.value + getTitleBarHeight.value);

	// 计算左侧图标位置（特定平台）
	const getLeftIconLeft = computed(() => {
		
		let menuButtonInfo = uni.getMenuButtonBoundingClientRect()
		console.log("menuButtonInfo",menuButtonInfo);
		return menuButtonInfo.width 
		
	});

	// 当前选中项索引
	const current = ref(0);

	// 生命周期钩子（如果需要在挂载后执行逻辑）
	onMounted(() => {
		// 初始化逻辑
	});
</script>
<style lang="scss" scoped>
	@import "../../common/style/base-style.scss";
	.layout {
		.navbar {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			z-index: 10;
			background:
				linear-gradient(to bottom, transparent, #fff 400rpx),
				linear-gradient(to right, #beecd8 20%, #F4E2D8);

			.statusBar {}

			.titleBar {
				display: flex;
				align-items: center;
				padding: 0 30rpx;
				justify-content: space-between;
				.left {
					margin-right: 20rpx;
				}
				.content{
					display: flex;
					flex: 1;
					.title {
						flex: 1;
						font-size: 30rpx;
						display: flex;
						justify-content: center;
						color: $text-font-color-1;
					}
					
					.right{
						font-size: 25rpx;
						display: flex;
						align-items: baseline;
					}
					
					.search {
						width: 220rpx;
						height: 50rpx;
						border-radius: 60rpx;
						background: rgba(255, 255, 255, 0.4);
						border: 1px solid #fff;
						margin-left: 30rpx;
						color: #999;
						font-size: 28rpx;
						display: flex;
						align-items: center;
					
						.icon {
							margin-left: 5rpx;
						}
					
						.text {
							padding-left: 10rpx;
						}
					}
				}
				
			}
		}
		
		.navbar.enableColor{
			background:transparent;
			background-color: transparent;
		}

		.fill {
		}
	}
</style>