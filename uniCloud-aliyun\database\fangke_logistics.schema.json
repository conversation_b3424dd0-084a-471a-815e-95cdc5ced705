// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "物流单",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"create_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"update_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"name":{
			"bsonType": "string",
			"label": "快递名称"
		},
		"munber":{
			"bsonType": "string",
			"label": "快递单号"
		}
		
	}
}