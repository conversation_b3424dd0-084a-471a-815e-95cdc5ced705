<template>
	<view class="container">
		<!-- 费用类型选择 -->
		<view class="section" v-if="feeTypeList.length >1">
			<view class="form-item" @click="showFeeTypeSelector">
				<text class="label">选择费用类型</text>
				<view class="value-container">
					<text class="value">{{selectedFeeType || '请选择'}}</text>
					<uni-icons type="down" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 收款方式 -->
		<view class="section">
			<view class="form-item" @click="showPaymentMethodSelector">
				<text class="label">收款方式</text>
				<view class="value-container">
					<text class="value">{{paymentMethod}}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 实收金额 -->
		<view class="section">
			<view class="form-item">
				<text class="label">实收金额</text>
				<view class="amount-input-container">
					<input 
						type="digit" 
						v-model="amount" 
						placeholder="0.00" 
						class="amount-input"
						@input="onAmountInput"
					/>
					<text class="currency">元</text>
				</view>
			</view>
		</view>
		
		<!-- 收款日期 -->
		<view class="section">
			<view class="form-item" @click="showDatePicker">
				<text class="label">收款日期</text>
				<view class="value-container">
					<!-- 日期选择器 -->
					<uni-datetime-picker 
						ref="datetimePicker"
						type="date" 
						v-model="paymentDate"
						@change="onDateChange"
						:border="false"
						:clearIcon = "false"
					></uni-datetime-picker>
					<!-- <text class="value">{{paymentDate}}</text> -->
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 上传凭证 -->
		<view class="section">
			<text class="label">上传凭证</text>
			<view class="upload-container">
				<view class="image-grid">
					<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
						<image :src="image" mode="aspectFill" class="uploaded-image" @click="previewImage(index)"></image>
						<view class="delete-btn" @click="deleteImage(index)">
							<uni-icons type="clear" size="12" color="#fff"></uni-icons>
						</view>
					</view>
					<view class="upload-btn" v-if="uploadedImages.length < 9" @click="chooseImage">
						<uni-icons type="plus" size="30" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 备注 -->
		<view class="section">
			<text class="label">备注</text>
			<view class="textarea-container">
				<textarea 
					v-model="remark" 
					placeholder="请输入备注" 
					class="remark-textarea"
					maxlength="200"
				></textarea>
			</view>
		</view>

		<!-- 底部确定按钮 -->
		<view class="footer">
			<button class="confirm-btn" @click="confirmPayment">确定</button>
		</view>

		<!-- 费用类型选择弹窗 -->
		<uni-popup ref="feeTypePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-cancel" @click="closeFeeTypePopup">取消</text>
					<text class="popup-title">选择费用类型</text>
					<text class="popup-confirm" @click="confirmFeeType">确定</text>
				</view>
				
				<!-- 全选选项 -->
				<view class="select-all-container">
					<view class="checkbox-item" @click="toggleSelectAll">
						<view class="checkbox" :class="{ checked: isAllSelected() }">
							<uni-icons v-if="isAllSelected()" type="checkmarkempty" size="16" color="#fff"></uni-icons>
						</view>
						<text class="checkbox-text">全选</text>
					</view>
				</view>
				
				<!-- 费用类型列表 -->
				<view class="checkbox-list">
					<view 
						class="checkbox-item" 
						v-for="(item, index) in feeTypeList" 
						:key="index"
						@click="toggleFeeType(item)"
					>
						<view class="checkbox" :class="{ checked: isFeeTypeSelected(item.name) }">
							<uni-icons v-if="isFeeTypeSelected(item.name)" type="checkmarkempty" size="16" color="#fff"></uni-icons>
						</view>
						<text class="checkbox-text">{{item.name}}</text>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 收款方式选择弹窗 -->
		<uni-popup ref="paymentMethodPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-cancel" @click="closePaymentMethodPopup">取消</text>
					<text class="popup-title">选择收款方式</text>
					<text class="popup-confirm" @click="confirmPaymentMethod">确定</text>
				</view>
				<picker-view class="picker-view" :value="paymentMethodPickerValue" @change="onPaymentMethodChange">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in paymentMethodList" :key="index">
							<text>{{item}}</text>
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>

		<!-- 确认收款弹窗 -->
		<uni-popup ref="confirmPaymentPopup" type="dialog">
			<view class="confirm-dialog">
				<view class="dialog-header">
					<text class="dialog-title">确认收款</text>
				</view>
				<view class="dialog-content">
					<text class="dialog-text">是否确认收取此笔费用？</text>
					<view class="payment-summary">
						<view class="summary-item">
							<text class="summary-label">费用类型：</text>
							<text class="summary-value">{{selectedFeeType}}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">收款金额：</text>
							<text class="summary-value amount">¥{{amount}}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">收款方式：</text>
							<text class="summary-value">{{paymentMethod}}</text>
						</view>
					</view>
				</view>
				<view class="dialog-footer">
					<button class="dialog-btn cancel-btn" @click="closeConfirmPaymentPopup">取消</button>
					<button class="dialog-btn confirm-btn" @click="handleConfirmPayment">确认收款</button>
				</view>
			</view>
		</uni-popup>

		
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	// 定义响应式数据
	import {
		store2
	} from '@/utils/js/store.js'
	const userInfo = computed(() => store2.userInfo)
	// 表单数据
	const selectedFeeType = ref('')
	const selectedFeeTypes = ref([])
	const paymentMethod = ref('微信转账')
	const amount = ref(0)
	const paymentDate = ref('')
	const uploadedImages = ref([])
	const remark = ref('')
	const bill_id = ref('')
	// 费用类型列表
	const feeTypeList = ref([])
	const tenantName = ref('')
	// 收款方式列表
	const paymentMethodList = ref([
		'微信转账',
		'支付宝转账',
		'银行卡转账',
		'现金',
		'POS刷卡',
		'其他'
	])

	// 选择器数值
	const feeTypePickerValue = ref([0])
	const paymentMethodPickerValue = ref([0])

	// 弹窗引用
	const feeTypePopup = ref()
	const paymentMethodPopup = ref()
	const confirmPaymentPopup = ref()
	const datetimePicker = ref()

	// 初始化日期为今天
	onMounted(() => {
		const today = new Date()
		const year = today.getFullYear()
		const month = String(today.getMonth() + 1).padStart(2, '0')
		const day = String(today.getDate()).padStart(2, '0')
		paymentDate.value = `${year}-${month}-${day}`
	})

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 显示费用类型选择器
	const showFeeTypeSelector = () => {
		feeTypePopup.value.open()
	}

	// 关闭费用类型选择器
	const closeFeeTypePopup = () => {
		feeTypePopup.value.close()
	}

	// 费用类型选择变化 - 修改为多选逻辑
	const toggleFeeType = (item) => {
		const index = selectedFeeTypes.value.indexOf(item.name)
		if (index > -1) {
			// 如果已选中，则取消选中
			selectedFeeTypes.value.splice(index, 1)
			amount.value = amount.value - item.value
		} else {
			// 如果未选中，则添加到选中列表
			selectedFeeTypes.value.push(item.name)
			amount.value = amount.value + item.value
		}
	}

	// 检查费用类型是否被选中
	const isFeeTypeSelected = (feeType) => {
		return selectedFeeTypes.value.includes(feeType)
	}

	// 全选/取消全选
	const toggleSelectAll = () => {
		if (selectedFeeTypes.value.length === feeTypeList.value.length) {
			// 如果已全选，则取消全选
			selectedFeeTypes.value = []
			amount.value = 0
		} else {
			// 否则全选
			feeTypeList.value.forEach(item =>{
				selectedFeeTypes.value.push(item.name)
				amount.value = amount.value + item.value
			})
			
		}
	}

	// 检查是否全选
	const isAllSelected = () => {
		return selectedFeeTypes.value.length === feeTypeList.value.length
	}

	// 确认费用类型选择 - 修改为多选逻辑
	const confirmFeeType = () => {
		if (selectedFeeTypes.value.length === 0) {
			uni.showToast({
				title: '请至少选择一种费用类型',
				icon: 'none'
			})
			return
		}
		
		// 更新显示文本
		if (selectedFeeTypes.value.length === 1) {
			selectedFeeType.value = selectedFeeTypes.value[0]
		} else {
			selectedFeeType.value = `已选择${selectedFeeTypes.value.length}项`
		}
		
		closeFeeTypePopup()
	}

	// 显示收款方式选择器
	const showPaymentMethodSelector = () => {
		paymentMethodPopup.value.open()
	}

	// 关闭收款方式选择器
	const closePaymentMethodPopup = () => {
		paymentMethodPopup.value.close()
	}

	// 收款方式选择变化
	const onPaymentMethodChange = (e) => {
		paymentMethodPickerValue.value = e.detail.value
	}

	// 确认收款方式选择
	const confirmPaymentMethod = () => {
		const index = paymentMethodPickerValue.value[0]
		paymentMethod.value = paymentMethodList.value[index]
		closePaymentMethodPopup()
	}

	// 金额输入处理
	const onAmountInput = (e) => {
		let value = e.detail.value
		// 限制只能输入数字和小数点
		value = value.replace(/[^\d.]/g, '')
		// 限制只能有一个小数点
		if (value.indexOf('.') !== value.lastIndexOf('.')) {
			value = value.substring(0, value.lastIndexOf('.'))
		}
		// 限制小数点后最多两位
		if (value.indexOf('.') !== -1) {
			const parts = value.split('.')
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2)
			}
		}
		amount.value = value
	}

	// 显示日期选择器
	const showDatePicker = () => {
		datetimePicker.value.show()
	}

	// 日期变化处理
	const onDateChange = (e) => {
		paymentDate.value = e
	}

	// 选择图片
	const chooseImage = () => {
		const remainingCount = 9 - uploadedImages.value.length
		uni.chooseImage({
			count: remainingCount,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				const tempFilePaths = res.tempFilePaths
				uploadedImages.value = uploadedImages.value.concat(tempFilePaths)
				uni.showToast({
					title: `已添加${tempFilePaths.length}张图片`,
					icon: 'success'
				})
			},
			fail: (err) => {
				console.log('选择图片失败:', err)
				uni.showToast({
					title: '选择图片失败',
					icon: 'none'
				})
			}
		})
	}

	// 预览图片
	const previewImage = (index) => {
		uni.previewImage({
			current: index,
			urls: uploadedImages.value
		})
	}

	// 删除图片
	const deleteImage = (index) => {
		uni.showModal({
			title: '提示',
			content: '确定要删除这张图片吗？',
			success: (res) => {
				if (res.confirm) {
					uploadedImages.value.splice(index, 1)
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 确认收款
	const confirmPayment = () => {
		// 表单验证
		if (selectedFeeTypes.value.length === 0) {
			uni.showToast({
				title: '请选择费用类型',
				icon: 'none'
			})
			return
		}

		if (!amount.value || parseFloat(amount.value) <= 0) {
			uni.showToast({
				title: '请输入正确的收款金额',
				icon: 'none'
			})
			return
		}

		// 显示确认弹窗
		confirmPaymentPopup.value.open()
	}

	// 关闭确认收款弹窗
	const closeConfirmPaymentPopup = () => {
		confirmPaymentPopup.value.close()
	}

	// 处理确认收款
	const handleConfirmPayment = async() => {
		// 关闭确认弹窗
		closeConfirmPaymentPopup()

		// 构建提交数据
		const paymentData = {
			detail: selectedFeeTypes.value,
			paymentMethod: paymentMethod.value,
			amount: parseFloat(amount.value)*100,
			paymentDate: paymentDate.value,
			images: uploadedImages.value,
			isPay:true,
			desc: remark.value,
			handler:userInfo.value.mobile,
			bill:bill_id.value,
			tenant_name:tenantName.value
		}
		console.log('收款数据:', paymentData)
		
		
		uni.showLoading({
			title: '提交中...'
		})
		
		let params = {
			uid:userInfo.value._id,
			data:paymentData
		}
		await uni.$lkj.api.commitPayment(params).then(res =>{
			console.log("收款成功",res);
			uni.showToast({
				title: '收款成功',
				icon: 'success',
			})
		}).finally(()=>{
			uni.hideLoading()
			setTimeout(() => {
				uni.reLaunch({
					url:"/pages/house/house"
				});
			}, 1000)
		})

	}

	onLoad((options) => {
		console.log('收款页面参数:', options)
		// 可以根据传入的参数预设一些数据
		if(options.bill_id){
			bill_id.value = options.bill_id
			let info = JSON.parse(decodeURIComponent(options.data))
			info.forEach(item =>{
				feeTypeList.value.push(item)
				amount.value = amount.value + item.value
			})
			feeTypeList.value.forEach(item =>{
				selectedFeeTypes.value.push(item.name)
			})
			tenantName.value = options.name
			// 更新显示文本
			if (selectedFeeTypes.value.length === 1) {
				selectedFeeType.value = selectedFeeTypes.value[0]
			} else {
				selectedFeeType.value = `已选择${selectedFeeTypes.value.length}项`
			}
		}
	})
</script>

<style>
	page {
		background-color: #F5F5F5;
	}

	.container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 120rpx;
	}


	/* 表单区域 */
	.section {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}

	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.label {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	.value-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.value {
		font-size: 16px;
		color: #666;
	}

	/* 金额输入 */
	.amount-input-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.amount-input {
		font-size: 24px;
		color: #333;
		text-align: right;
		min-width: 200rpx;
	}

	.currency {
		font-size: 16px;
		color: #666;
	}

	/* 上传区域 */
	.upload-container {
		padding: 30rpx 0;
	}

	.image-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.image-item {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.uploaded-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.delete-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 32rpx;
		height: 32rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.upload-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		height: 160rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		border: 2rpx dashed #CCCCCC;
	}

	/* 备注区域 */
	.textarea-container {
		padding: 30rpx 0;
	}

	.remark-textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		font-size: 16px;
		color: #333;
		line-height: 1.5;
	}

	/* 底部按钮 */
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		background-color: #fff;
		z-index: 10;
	}

	.confirm-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background-color: #00C8B3;
		color: #fff;
		font-size: 18px;
		border-radius: 44rpx;
		border: none;
	}

	.confirm-btn:active {
		background-color: #00B3A0;
	}

	/* 弹窗样式 */
	.popup-content {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		padding: 0 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.popup-cancel {
		font-size: 16px;
		color: #999;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.popup-confirm {
		font-size: 16px;
		color: #00C8B3;
	}

	.picker-view {
		height: 400rpx;
		padding: 0 30rpx;
	}

	.picker-item {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
	}

	.picker-item text {
		font-size: 16px;
		color: #333;
	}

	/* 全选选项 */
	.select-all-container {
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.checkbox-list {
		padding: 0 30rpx 40rpx;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		gap: 24rpx;
		padding: 24rpx 0;
		border-bottom: 1px solid #F8F8F8;
	}

	.checkbox-item:last-child {
		border-bottom: none;
	}

	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #CCCCCC;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.checkbox.checked {
		background-color: #00C8B3;
		border-color: #00C8B3;
	}

	.checkbox-text {
		font-size: 16px;
		color: #333;
		flex: 1;
	}

	.select-all-container .checkbox-text {
		font-weight: 500;
		color: #00C8B3;
	}

	/* 确认收款弹窗样式 */
	.confirm-dialog {
		background-color: #fff;
		border-radius: 16rpx;
		width: 600rpx;
		overflow: hidden;
	}

	.dialog-header {
		padding: 40rpx 30rpx 20rpx;
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
	}

	.dialog-title {
		font-size: 20px;
		font-weight: 600;
		color: #333;
	}

	.dialog-content {
		padding: 30rpx;
	}

	.dialog-text {
		font-size: 16px;
		color: #666;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.payment-summary {
		background-color: #F8F9FA;
		border-radius: 12rpx;
		padding: 24rpx;
	}

	.summary-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.summary-item:last-child {
		margin-bottom: 0;
	}

	.summary-label {
		font-size: 14px;
		color: #666;
	}

	.summary-value {
		font-size: 14px;
		color: #333;
	}

	.summary-value.amount {
		color: #00C8B3;
		font-size: 16px;
		font-weight: 600;
	}

	.dialog-footer {
		display: flex;
		border-top: 1px solid #F0F0F0;
	}

	.dialog-btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 16px;
		border: none;
		background-color: #fff;
	}

	.dialog-btn.cancel-btn {
		color: #666;
		border-right: 1px solid #F0F0F0;
	}

	.dialog-btn.confirm-btn {
		color: #00C8B3;
		font-weight: 600;
	}

	.dialog-btn:active {
		background-color: #F8F9FA;
	}
</style>
