<template>
	<view class="container">
		<uni-section title="尚未交租的记录" type="line" style="background-color: #eeeeee;"></uni-section>
		<text>一共拖欠{{money}}元</text>
		<view class="content">
			<uni-list style="background-color: white;">
				<uni-list-item v-for="(item , indedx) in list" :key="indedx" :title="item.year+'年'+item.month+'月'"
					:right-text="item.money/100" style="background-color: white;"></uni-list-item>
			</uni-list>
		</view>
		<view class="bottom">
			<text>-----------{{tip}}------------</text>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'

	const api = uniCloud.importObject("ApiFuntion")

	// 定义响应式数据
	const list = ref([])
	const tip = ref("已经到底了")
	const money = ref(0)

	// 生命周期钩子
	onMounted((e) => {
		console.log("timeout", e)
		getTimeoutList(e.id)
	})

	// 获取超时列表
	const getTimeoutList = (room_id) => {
		api.getTimeoutList(room_id).then(res => {
			console.log("getTimeoutList", res)
			list.value = res.data

			// 计算总金额
			money.value = 0
			list.value.forEach(item => {
				money.value += item.money / 100
			})
		})
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		background-color: #eeeeee;
		padding: 20rpx;
		height: 100vh;
		align-self: center;

		.content {
			background-color: white;
			border-radius: 10rpx;
		}

		.bottom {
			display: flex;
			justify-content: center;
			font-size: 20rpx;
			margin-top: 20rpx;
		}
	}
</style>