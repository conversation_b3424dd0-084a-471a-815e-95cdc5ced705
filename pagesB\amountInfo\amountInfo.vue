<template>
	<view class="container">
		<!-- 顶部金额显示区域 -->
		<view class="amount-header">
			<view class="amount-icon">
				<uni-icons type="wallet" size="32" color="#fff"></uni-icons>
			</view>
			<text class="amount-label">{{transactionInfo.billName}}</text>
			<view class="amount-display">
				<text class="amount-symbol">+</text>
				<text class="amount-value">{{formatAmount(transactionInfo.amount)}}</text>
			</view>
		</view>

		<!-- 交易详情信息 -->
		<view class="transaction-details">
			<view class="detail-item">
				<text class="detail-label">房源地址：</text>
				<text class="detail-value">{{transactionInfo.address}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">承租人：</text>
				<text class="detail-value">{{transactionInfo.tenantName}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">交易方式：</text>
				<text class="detail-value">{{transactionInfo.paymentMethod}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">经办人：</text>
				<text class="detail-value">{{transactionInfo.handler}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">交易时间：</text>
				<text class="detail-value">{{transactionInfo.transactionTime}}</text>
			</view>
		</view>

		<!-- 所属账单 -->
		<view class="bill-section">
			<view class="detail-item clickable" @click="viewBillDetail">
				<text class="detail-label">所属账单：</text>
				<view class="bill-value">
					<text class="detail-value">{{transactionInfo.billName}}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="footer-actions">
			<button class="action-btn delete-btn" @click="showDeleteConfirm">删除流水</button>
		</view>

		<!-- 删除确认弹窗 -->
		<uni-popup ref="deleteConfirmPopup" type="dialog">
			<view class="confirm-dialog">
				<view class="dialog-header">
					<text class="dialog-title">删除确认</text>
				</view>
				<view class="dialog-content">
					<text class="dialog-text">确定要删除这条流水记录吗？</text>
					<text class="dialog-warning">删除后将无法恢复</text>
				</view>
				<view class="dialog-footer">
					<button class="dialog-btn cancel-btn" @click="closeDeleteConfirm">取消</button>
					<button class="dialog-btn confirm-btn" @click="handleDeleteTransaction">确定删除</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, onMounted,getCurrentInstance } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	const db = uniCloud.databaseForJQL()
	// 交易信息数据
	const transactionInfo = ref({
		amount: 1000, // 金额（分）
		address: '公寓-301',
		tenantName: '倪敏',
		paymentMethod: '微信转账',
		handler: '13632244771',
		transactionTime: '2025-01-19 17:37:34',
		billName: '押金',
		transactionId: '',
		id: '' // id
	})

	// 弹窗引用
	const deleteConfirmPopup = ref()

	// 格式化金额显示
	const formatAmount = (amount) => {
		return (amount / 100).toFixed(2)
	}

	// 查看账单详情
	const viewBillDetail = () => {
		// 这里可以跳转到具体的账单详情页面
		uni.navigateTo({
			url: "/pagesB/roomInfoBill/roomInfoBill?id=" + transactionInfo.value.id
		})
	}

	// 显示删除确认弹窗
	const showDeleteConfirm = () => {
		deleteConfirmPopup.value.open()
	}

	// 关闭删除确认弹窗
	const closeDeleteConfirm = () => {
		deleteConfirmPopup.value.close()
	}

	// 处理删除交易
	const handleDeleteTransaction = () => {
		closeDeleteConfirm()
		
		uni.showLoading({
			title: '删除中...'
		})

		db.collection("fangke_room_account").doc(transactionInfo.value.transactionId).remove().then(res =>{
			console.log("删除成功",res);
			uni.hideLoading()
			uni.showToast({
				title: '删除成功',
				icon: 'success',
				duration: 2000
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 2000)
		})
	}
	
	onMounted(()=>{
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
		      console.log('acceptDataFromOpenerPage', data)
			  transactionInfo.value.transactionId = data.transactionId
			  transactionInfo.value.amount = data.amount
			  transactionInfo.value.address = data.room_name
			  transactionInfo.value.tenantName = data.tenant
			  transactionInfo.value.paymentMethod = data.payment
			  transactionInfo.value.handler = data.handler
			  transactionInfo.value.transactionTime = data.time
			  transactionInfo.value.billName = data.name
			  transactionInfo.value.id = data.bill_id
		    })
	})

	// 页面加载时获取数据
	onLoad((options) => {
		console.log('流水详情页面参数:', options)
		
		// 根据传入参数设置交易信息
		if (options.data) {
			let data = decodeURIComponent(options.data)
			
		}
	})

</script>

<style>
	page {
		background-color: #F5F5F5;
	}

	.container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 120rpx;
	}

	/* 顶部金额显示区域 */
	.amount-header {
		background: linear-gradient(135deg, #00C8B3 0%, #00B3A0 100%);
		padding: 60rpx 30rpx 80rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #fff;
		position: relative;
	}

	.amount-icon {
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.amount-label {
		font-size: 16px;
		color: rgba(255, 255, 255, 0.9);
		margin-bottom: 20rpx;
	}

	.amount-display {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
	}

	.amount-symbol {
		font-size: 32px;
		font-weight: 300;
	}

	.amount-value {
		font-size: 48px;
		font-weight: 300;
		letter-spacing: 2rpx;
	}

	/* 交易详情信息 */
	.transaction-details {
		background-color: #fff;
		margin-top: -40rpx;
		border-radius: 20rpx 20rpx 0 0;
		padding: 40rpx 30rpx 0;
		position: relative;
		z-index: 1;
	}

	.detail-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 0;
		border-bottom: 1px solid #F0F0F0;
	}

	.detail-item:last-child {
		border-bottom: none;
	}

	.detail-item.clickable {
		cursor: pointer;
	}

	.detail-item.clickable:active {
		background-color: #F8F9FA;
	}

	.detail-label {
		font-size: 16px;
		color: #333;
		font-weight: 400;
	}

	.detail-value {
		font-size: 16px;
		color: #666;
		text-align: right;
		flex: 1;
		margin-left: 40rpx;
	}

	.bill-value {
		display: flex;
		align-items: center;
		gap: 16rpx;
		flex: 1;
		justify-content: flex-end;
	}

	/* 所属账单区域 */
	.bill-section {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}

	/* 底部操作按钮 */
	.footer-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		background-color: #fff;
		z-index: 10;
		border-top: 1px solid #F0F0F0;
	}

	.action-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		font-size: 18px;
		border-radius: 44rpx;
		border: none;
	}

	.delete-btn {
		background-color: #FF4757;
		color: #fff;
	}

	.delete-btn:active {
		background-color: #FF3742;
	}

	/* 删除确认弹窗样式 */
	.confirm-dialog {
		background-color: #fff;
		border-radius: 16rpx;
		width: 600rpx;
		overflow: hidden;
	}

	.dialog-header {
		padding: 40rpx 30rpx 20rpx;
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
	}

	.dialog-title {
		font-size: 20px;
		font-weight: 600;
		color: #333;
	}

	.dialog-content {
		padding: 30rpx;
		text-align: center;
	}

	.dialog-text {
		font-size: 16px;
		color: #666;
		margin-bottom: 16rpx;
	}

	.dialog-warning {
		font-size: 14px;
		color: #FF4757;
	}

	.dialog-footer {
		display: flex;
		border-top: 1px solid #F0F0F0;
	}

	.dialog-btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 16px;
		border: none;
		background-color: #fff;
	}

	.dialog-btn.cancel-btn {
		color: #666;
		border-right: 1px solid #F0F0F0;
	}

	.dialog-btn.confirm-btn {
		color: #FF4757;
		font-weight: 600;
	}

	.dialog-btn:active {
		background-color: #F8F9FA;
	}
</style>
