<template>
	<view class="container">

		<uni-section title="任务列表" type="line">
			<template v-slot:right>
				<view> <button type="primary" size="mini" @click="addTask">添加任务</button></view>
			</template>
		</uni-section>
		<uni-list style="font-size: 80rpx;color: black;">
			<uni-list-item v-for="(item,index) in taskList" :key="index.update_time" :title="item.name"
				:note="item.building_name" :right-text="getTime(item.time)">
			</uni-list-item>

		</uni-list>

	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'

	const api = uniCloud.importObject("ApiFuntion")

	// 定义响应式数据
	const taskId = ref('')
	const taskList = ref([])
	const building_id = ref("")
	const houses = ref([])
	const uid = ref("")
	const time = ref("")

	// 生命周期钩子
	onMounted(async (e) => {
		console.log("taskInfo", e)

		if (e) {
			uid.value = e.uid
			await getBuilding()
			await getTaskList()
		}
	})

	// 获取建筑物列表
	const getBuilding = async () => {
		await api.getBuilding(uid.value).then(res => {
			console.log("getBuilding", res)

			for (let i = 0; i < res.data.length; i++) {
				houses.value.push(res.data[i]._id)
			}
		})
	}

	// 获取任务列表
	const getTaskList = async () => {
		console.log("getTaskList", houses.value)

		if (houses.value.length) {
			await api.getTask(houses.value).then(res => {
				console.log("获取任务：", res)

				if (res.code) {
					taskList.value = res.data
				}
			})
		}
	}

	// 格式化时间
	const getTime = (e) => {
		console.log("getTime", e)
		let res = "1"

		if (e) {
			const split = e.split("_")
			const time = split[1]
			const type = split[2]

			switch (type) {
				case "0":
					res = time + " 上午"
					break
				case "1":
					res = time + " 下午"
					break
				case "2":
					res = time + " 晚上"
					break
			}
		}

		return res
	}

	// 添加任务
	const addTask = () => {
		uni.navigateTo({
			url: `/pagesB/addTask/addTask?uid=${uid.value}`
		})
	}
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		box-sizing: border-box;


	}
</style>