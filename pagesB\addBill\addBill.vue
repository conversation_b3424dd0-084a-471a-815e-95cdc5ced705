<template>
	<view class="container">
		<view class="section">
			<view class="form-item" >
				<text class="label">房源信息</text>
				<view class="value-container">
					<text class="value">{{room_name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 费用类型选择 -->
		<view class="section" >
			<view class="form-item" @click="showFeeTypeSelector">
				<text class="label">选择费用类型</text>
				<view class="value-container">
					<text class="value">{{selectedFeeType || '请选择'}}</text>
					<uni-icons type="down" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		

		<!-- 收款方式 -->
		<view class="section">
			<view class="form-item" @click="showPeriodSelector">
				<text class="label">账单周期</text>
				<view class="value-container">
					<text class="value">{{period}}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 实收金额 -->
		<view class="section">
			<view class="form-item">
				<text class="label">账单金额</text>
				<view class="amount-input-container">
					<input 
						type="digit" 
						v-model="amount" 
						placeholder="输入金额" 
						class="amount-input"
						@input="onAmountInput"
					/>
				</view>
			</view>
		</view>
		
		<!-- 收款日期 -->
		<view class="section">
			<view class="form-item" @click="showDatePicker">
				<text class="label">收款日期</text>
				<view class="value-container">
					<!-- 日期选择器 -->
					<uni-datetime-picker 
						ref="datetimePicker"
						type="date" 
						v-model="paymentDate"
						@change="onDateChange"
						:border="false"
						:clearIcon = "false"
					></uni-datetime-picker>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 备注 -->
		<view class="section">
			<text class="label">备注</text>
			<view class="textarea-container">
				<textarea 
					v-model="remark" 
					placeholder="请输入备注" 
					class="remark-textarea"
					maxlength="200"
				></textarea>
			</view>
		</view>

		<!-- 底部确定按钮 -->
		<view class="footer">
			<button class="confirm-btn" @click="confirmPayment">确定</button>
		</view>

		<uni-popup ref="feeTypePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-cancel" @click="closeFeeTypePopup">取消</text>
					<text class="popup-title">选择费用类型</text>
					<text class="popup-confirm" @click="confirmFeeSelection">确定</text>
				</view>
				<picker-view class="picker-view" :value="feeTypePickerValue" @change="onFeePickerChange">
					<picker-view-column>
						<view class="picker-item" v-for="(category, index) in feeCategories" :key="index">
							{{ category.name }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in currentFeeChildren" :key="index">
							{{ item }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
		
		<uni-popup ref="periodPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-cancel" @click="closePeriodPopup">取消</text>
					<text class="popup-title">选择账单周期</text>
					<text class="popup-confirm" @click="confirmPeriod">确定</text>
				</view>
				<picker-view class="picker-view" :value="periodPickerValue" @change="onPeriodChange">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in periodList" :key="index">
							<text>{{item.name}}</text>
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>

		<!-- 确认收款弹窗 -->
		<uni-popup ref="confirmPopup" type="dialog">
			<view class="confirm-dialog">
				<view class="dialog-header">
					<text class="dialog-title">确认生成账单</text>
				</view>
				<view class="dialog-content">
					<text class="dialog-text">生成后账单里不能取消</text>
					<view class="payment-summary">
						<view class="summary-item">
							<text class="summary-label">费用类型：</text>
							<text class="summary-value">{{selectedFeeType}}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">收款金额：</text>
							<text class="summary-value amount">¥{{amount}}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">账单周期：</text>
							<text class="summary-value">{{period}}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">收款时间：</text>
							<text class="summary-value">{{paymentDate}}</text>
						</view>
					</view>
				</view>
				<view class="dialog-footer">
					<button class="dialog-btn confirm-btn" @click="handleConfirm">确认</button>
				</view>
			</view>
		</uni-popup>

		
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed,
		getCurrentInstance
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	// 定义响应式数据
	import {
		store2
	} from '@/utils/js/store.js'
import { dayjs } from '../../utils/dayjs.min'
	const userInfo = computed(() => store2.userInfo)
	// 表单数据
	const room_name = ref('')
	const selectedFeeType = ref('')
	const period = ref('')
	const amount = ref(0)
	const paymentDate = ref('')
	const remark = ref('')
	const bill_id = ref('')
	const feePickerValue = ref([0, 0]); // [分类索引, 子项索引]
	// 费用分类数据
	const feeCategories = ref([{
			"name": "租金",
			"children": [
				"租金补差",
				"租金涨价",
				"临时租金",
				"欠费补缴"
			]
		},
		{
			"name": "押金",
			"children": [
			"门卡押金",
			"钥匙押金",
			"家具押金",
			"家电押金"
		]
		},
		{
			"name": "水电燃",
			"children": [
				"水费",
				"电费",
				"电损耗费",
				"水损耗费"
			]
		},
		{
			"name": "维修费",
			"children": [
				"家电维修",
				"房屋维修",
				"公共区域维修"
			]
		},
		{
			"name": "服务费",
			"children": [
				"管理费",
				"物业费",
				"宽带费",
				"垃圾费",
				"搬家费",
				"保洁费"
			]
		}
	]);
	
	// 当前选中分类的子项
	const currentFeeChildren = computed(() => {
		const categoryIndex = feePickerValue.value[0];
		return feeCategories.value[categoryIndex]?.children || [];
	});
	// 周期列表
	const periodList = ref([])

	// 选择器数值
	const feeTypePickerValue = ref([0,0])
	const periodPickerValue = ref([0])

	// 弹窗引用
	const feeTypePopup = ref()
	const periodPopup = ref()
	const confirmPopup = ref()
	const datetimePicker = ref()

	// 初始化日期为今天
	onMounted(() => {
		const today = new Date()
		const year = today.getFullYear()
		const month = String(today.getMonth() + 1).padStart(2, '0')
		const day = String(today.getDate()).padStart(2, '0')
		paymentDate.value = `${year}-${month}-${day}`
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
		      console.log('acceptDataFromOpenerPage', data)
			  periodList.value = data.data
		    })
	})

	const onFeePickerChange = (e) => {
		console.log('费用选择变化:', e.detail.value);
		feePickerValue.value = e.detail.value;
	};
	
	const confirmFeeSelection = () => {
		const categoryIndex = feePickerValue.value[0];
		const childIndex = feePickerValue.value[1];
	
		const categoryName = feeCategories.value[categoryIndex]?.name;
		const childName = feeCategories.value[categoryIndex]?.children[childIndex];
	
		if (categoryName && childName) {
			selectedFeeType.value = childName;
		}
	
		closeFeeTypePopup();
	};
	// 显示费用类型选择器
	const showFeeTypeSelector = () => {
		feeTypePopup.value.open()
	}

	// 关闭费用类型选择器
	const closeFeeTypePopup = () => {
		feeTypePopup.value.close()
	}

	// 显示收款方式选择器
	const showPeriodSelector = () => {
		periodPopup.value.open()
	}

	// 关闭收款方式选择器
	const closePeriodPopup = () => {
		periodPopup.value.close()
	}
	

	// 收款方式选择变化
	const onPeriodChange = (e) => {
		periodPickerValue.value = e.detail.value
	}

	// 确认收款方式选择
	const confirmPeriod = () => {
		const index = periodPickerValue.value[0]
		console.log("confirmPeriod", periodList.value,index);
		period.value = periodList.value[index].name
		bill_id.value = periodList.value[index].id
		closePeriodPopup()
	}

	// 金额输入处理
	const onAmountInput = (e) => {
		let value = e.detail.value
		// 限制只能输入数字和小数点
		value = value.replace(/[^\d.]/g, '')
		// 限制只能有一个小数点
		if (value.indexOf('.') !== value.lastIndexOf('.')) {
			value = value.substring(0, value.lastIndexOf('.'))
		}
		// 限制小数点后最多两位
		if (value.indexOf('.') !== -1) {
			const parts = value.split('.')
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2)
			}
		}
		amount.value = value
	}

	// 显示日期选择器
	const showDatePicker = () => {
		datetimePicker.value.show()
	}

	// 日期变化处理
	const onDateChange = (e) => {
		paymentDate.value = e
	}

	// 确认收款
	const confirmPayment = () => {
		// 表单验证
		if (selectedFeeType.value === "") {
			uni.showToast({
				title: '请选择费用类型',
				icon: 'none'
			})
			return
		}
		
		if (period.value === "") {
			uni.showToast({
				title: '请选择账单周期',
				icon: 'none'
			})
			return
		}

		if (!amount.value || parseFloat(amount.value) <= 0) {
			uni.showToast({
				title: '请输入正确的收款金额',
				icon: 'none'
			})
			return
		}

		// 显示确认弹窗
		confirmPopup.value.open()
	}

	// 关闭确认收款弹窗
	const closeconfirmPopup = () => {
		confirmPopup.value.close()
	}

	// 处理确认收款
	const handleConfirm = async() => {
		// 关闭确认弹窗
		closeconfirmPopup()

		// 构建提交数据
		const billData = {
			text: selectedFeeType.value,
			value: parseFloat(amount.value)*100,
			status: 1,
			day: paymentDate.value,
			overday:0,
			is_month:false,
			unit:parseFloat(amount.value)*100,
			unit_name:"元",
			desc: remark.value,
			create_time:dayjs().valueOf()
		}
		console.log('收款数据:', billData)
		
		uni.showLoading({
			title: '提交中...'
		})
		
		let params = {
			uid:userInfo.value._id,
			data:billData,
			bill_id:bill_id.value
		}
		
		await uni.$lkj.api.billAdd(params).then(res =>{
			console.log("添加成功",res);
			uni.showToast({
				title: '添加成功',
				icon: 'success',
			})
		})
		uni.hideLoading()
		setTimeout(() => {
				uni.navigateBack();
			}, 1000)
	}

	onLoad((options) => {
		console.log('收款页面参数:', options)
		// 可以根据传入的参数预设一些数据
		if(options.room_name){
			room_name.value = options.room_name
		}
		
	})
</script>

<style>
	page {
		background-color: #F5F5F5;
	}

	.container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 120rpx;
	}


	/* 表单区域 */
	.section {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}

	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.label {
		font-size: 16px;
		color: #333;
		font-weight: 500;
	}

	.value-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.value {
		font-size: 16px;
		color: #666;
	}

	/* 金额输入 */
	.amount-input-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.amount-input {
		font-size: 24px;
		color: #333;
		text-align: right;
		min-width: 200rpx;
	}

	.currency {
		font-size: 16px;
		color: #666;
	}

	/* 上传区域 */
	.upload-container {
		padding: 30rpx 0;
	}

	.image-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.image-item {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.uploaded-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.delete-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 32rpx;
		height: 32rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.upload-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		height: 160rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		border: 2rpx dashed #CCCCCC;
	}

	/* 备注区域 */
	.textarea-container {
		padding: 30rpx 0;
	}

	.remark-textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		font-size: 16px;
		color: #333;
		line-height: 1.5;
	}

	/* 底部按钮 */
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		background-color: #fff;
		z-index: 10;
	}

	.confirm-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background-color: #00C8B3;
		color: #fff;
		font-size: 18px;
		border-radius: 44rpx;
		border: none;
	}

	.confirm-btn:active {
		background-color: #00B3A0;
	}

	/* 弹窗样式 */
	.popup-content {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100rpx;
		padding: 0 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.popup-cancel {
		font-size: 16px;
		color: #999;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.popup-confirm {
		font-size: 16px;
		color: #00C8B3;
	}

	.picker-view {
		height: 400rpx;
		padding: 0 30rpx;
	}

	.picker-item {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
	}

	.picker-item text {
		font-size: 16px;
		color: #333;
	}

	/* 全选选项 */
	.select-all-container {
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.checkbox-list {
		padding: 0 30rpx 40rpx;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		gap: 24rpx;
		padding: 24rpx 0;
		border-bottom: 1px solid #F8F8F8;
	}

	.checkbox-item:last-child {
		border-bottom: none;
	}

	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #CCCCCC;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.checkbox.checked {
		background-color: #00C8B3;
		border-color: #00C8B3;
	}

	.checkbox-text {
		font-size: 16px;
		color: #333;
		flex: 1;
	}

	.select-all-container .checkbox-text {
		font-weight: 500;
		color: #00C8B3;
	}

	/* 确认收款弹窗样式 */
	.confirm-dialog {
		background-color: #fff;
		border-radius: 16rpx;
		width: 650rpx;
		overflow: hidden;
	}

	.dialog-header {
		padding: 40rpx 30rpx 20rpx;
		text-align: center;
		border-bottom: 1px solid #F0F0F0;
	}

	.dialog-title {
		font-size: 20px;
		font-weight: 600;
		color: #333;
	}

	.dialog-content {
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.dialog-text {
		display: flex;
		font-size: 16px;
		color: red;
		text-align: center;
		margin-bottom: 20rpx;
		justify-content: center;
	}

	.payment-summary {
		background-color: #F8F9FA;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	.summary-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.summary-item:last-child {
		margin-bottom: 0;
	}

	.summary-label {
		font-size: 14px;
		color: #666;
	}

	.summary-value {
		font-size: 14px;
		color: #333;
	}

	.summary-value.amount {
		color: #00C8B3;
		font-size: 16px;
		font-weight: 600;
	}

	.dialog-footer {
		display: flex;
		border-top: 1px solid #F0F0F0;
	}

	.dialog-btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 16px;
		border: none;
		background-color: #fff;
	}

	.dialog-btn.cancel-btn {
		color: #666;
		border-right: 1px solid #F0F0F0;
	}

	.dialog-btn.confirm-btn {
		color: #00C8B3;
		font-weight: 600;
	}

	.dialog-btn:active {
		background-color: #F8F9FA;
	}
</style>
