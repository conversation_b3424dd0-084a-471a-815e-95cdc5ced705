<template>
  <view class="image-picker">
    <view class="photo-upload">
      <!-- 如果没有选择图片，显示自定义上传按钮 -->
      <view v-if="imageList.length === 0" class="custom-upload-area">
        <view class="upload-box" @click="selectImages">
          <uni-icons type="camera" size="24" color="#999"></uni-icons>
          <text class="upload-text">{{ uploadText }}</text>
        </view>
      </view>

      <!-- 显示已选择的图片 -->
      <view
        v-if="imageList.length > 0"
        class="selected-images"
        :class="gridClass"
      >
        <view
          v-for="(file, index) in imageList"
          :key="file.id || index"
          class="image-item"
          @click="handleImageClick(index)"
        >
          <image
            :src="file.url || file.path"
            class="image-preview"
            mode="aspectFill"
          >
          </image>

          <!-- 图片操作按钮 -->
          <view class="image-actions" v-if="showActions">
            <!-- 封面设置按钮 -->
            <view
              v-if="enableCover"
              class="cover-btn"
              :class="{ active: file.isCover }"
              @click.stop="setCover(index)"
            >
              <text>{{ file.isCover ? '封面' : '设为封面' }}</text>
            </view>

            <!-- 删除按钮 -->
            <view
              v-if="enableDelete"
              class="delete-btn"
              @click.stop="removeImage(index)"
            >
              <uni-icons type="close" size="16" color="#fff"></uni-icons>
            </view>
          </view>
        </view>

        <!-- 添加更多图片按钮 -->
        <view
          v-if="imageList.length < maxCount"
          class="upload-box small"
          @click="selectImages"
        >
          <uni-icons type="plus" size="20" color="#999"></uni-icons>
        </view>
      </view>

      <!-- 底部信息和提示 -->
      <view v-if="imageList.length > 0 || showTips" class="upload-actions">
        <text class="upload-info"
          >图片 ({{ imageList.length }}/{{ maxCount }})</text
        >
        <view v-if="showTips && tips.length > 0" class="tips-container">
          <text v-for="(tip, index) in tips" :key="index" class="tip-text">{{
            tip
          }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed, defineProps, defineEmits } from 'vue'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 9
  },
  autoUpload: {
    type: Boolean,
    default: false
  },
  // 是否启用封面功能
  enableCover: {
    type: Boolean,
    default: false
  },
  // 是否启用删除功能
  enableDelete: {
    type: Boolean,
    default: true
  },
  // 是否启用预览功能
  enablePreview: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 是否显示提示信息
  showTips: {
    type: Boolean,
    default: false
  },
  // 自定义提示信息
  tips: {
    type: Array,
    default: () => []
  },
  // 上传按钮文字
  uploadText: {
    type: String,
    default: '选择照片'
  },
  // 网格布局类型 (grid-2, grid-3, grid-4)
  gridType: {
    type: String,
    default: 'grid-3'
  },
  // 云存储路径前缀
  cloudPathPrefix: {
    type: String,
    default: 'images'
  },
  // 图片压缩类型
  sizeType: {
    type: Array,
    default: () => ['compressed']
  },
  // 图片来源类型
  sourceType: {
    type: Array,
    default: () => ['album', 'camera']
  }
})

// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'change',
  'upload-success',
  'upload-error',
  'preview',
  'cover-change',
  'delete'
])

// 内部图片列表
const imageList = ref([])

// 标记是否正在同步数据，防止循环更新
const isSyncing = ref(false)

// 计算属性
const gridClass = computed(() => {
  return `grid-layout ${props.gridType}`
})

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  newVal => {
    if (isSyncing.value) return

    const newImageList = newVal.map((item, index) => {
      if (typeof item === 'string') {
        return {
          url: item,
          cloudUrl: item,
          uploaded: true,
          name: item.split('/').pop() || 'image',
          extname: item.split('.').pop() || 'jpg',
          isCover: false,
          id: Date.now() + index
        }
      }
      return {
        ...item,
        id: item.id || Date.now() + index,
        isCover: item.isCover || false
      }
    })

    // 只有当数据真正变化时才更新
    if (JSON.stringify(newImageList) !== JSON.stringify(imageList.value)) {
      imageList.value = newImageList
    }
  },
  { immediate: true }
)

// 监听内部列表变化，同步到外部
watch(
  imageList,
  newVal => {
    isSyncing.value = true
    emit('update:modelValue', newVal)
    emit('change', newVal)
    // 使用 nextTick 确保数据更新完成后再重置标记
    setTimeout(() => {
      isSyncing.value = false
    }, 0)
  },
  { deep: true }
)

// 选择图片
const selectImages = () => {
  const remainingCount = props.maxCount - imageList.value.length
  uni.chooseImage({
    count: remainingCount,
    sizeType: props.sizeType,
    sourceType: props.sourceType,
    success: res => {
      console.log('选择图片成功:', res)
      const newImages = res.tempFilePaths.map((path, index) => ({
        url: path,
        path: path,
        name: `image_${Date.now()}_${index}.jpg`,
        extname: 'jpg',
        uploaded: false,
        cloudUrl: '',
        isCover:
          props.enableCover && imageList.value.length === 0 && index === 0, // 第一张图片设为封面
        id: Date.now() + index
      }))
      imageList.value = [...imageList.value, ...newImages]

      // 如果启用自动上传
      if (props.autoUpload) {
        uploadImages(newImages)
      }
    },
    fail: err => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 删除图片
const removeImage = index => {
  const deletedImage = imageList.value[index]
  imageList.value.splice(index, 1)

  // 如果删除的是封面，将第一张图片设为封面
  if (props.enableCover && deletedImage.isCover && imageList.value.length > 0) {
    imageList.value[0].isCover = true
    emit('cover-change', imageList.value[0], 0)
  }

  emit('delete', deletedImage, index)
}

// 设置封面
const setCover = index => {
  if (!props.enableCover) return

  // 取消所有图片的封面状态
  imageList.value.forEach(img => {
    img.isCover = false
  })

  // 设置选中图片为封面
  imageList.value[index].isCover = true

  emit('cover-change', imageList.value[index], index)

  uni.showToast({
    title: '封面设置成功',
    icon: 'success'
  })
}

// 处理图片点击事件
const handleImageClick = index => {
  if (props.enablePreview) {
    previewImage(index)
  }
  emit('preview', imageList.value[index], index)
}

// 预览图片
const previewImage = index => {
  const urls = imageList.value.map(img => img.url || img.path)
  uni.previewImage({
    current: index,
    urls: urls
  })
}

// 上传图片到云存储
const uploadImages = async images => {
  if (!images || images.length === 0) return []

  const uploadPromises = images.map(async image => {
    if (image.uploaded) return image

    try {
      const uploadResult = await uniCloud.uploadFile({
        filePath: image.path,
        cloudPath: `${props.cloudPathPrefix}/${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 9)}.${image.extname}`,
        cloudPathAsRealPath: true
      })

      if (uploadResult.fileID) {
        console.log('上传的结果：', uploadResult.fileID)
        const updatedImage = {
          ...image,
          uploaded: true,
          cloudUrl: uploadResult.fileID
        }

        // 更新本地列表中的图片信息
        const imageIndex = imageList.value.findIndex(
          img => img.path === image.path
        )
        if (imageIndex !== -1) {
          imageList.value[imageIndex] = updatedImage
        }

        return updatedImage
      } else {
        throw new Error('上传失败，未返回文件ID')
      }
    } catch (error) {
      console.error('图片上传失败:', error)
      emit('upload-error', error)
      throw error
    }
  })

  try {
    const results = await Promise.all(uploadPromises)
    emit('upload-success', results)
    return results
  } catch (error) {
    throw error
  }
}

// 获取未上传的图片
const getUnUploadedImages = () => {
  return imageList.value.filter(img => !img.uploaded)
}

// 获取已上传的图片URL列表
const getUploadedUrls = () => {
  return imageList.value
    .filter(img => img.uploaded)
    .map(img => img.cloudUrl || img.url)
    .filter(url => url)
}

// 获取封面图片
const getCoverImage = () => {
  return imageList.value.find(img => img.isCover) || null
}

// 获取所有图片的完整信息
const getAllImages = () => {
  return imageList.value
}

// 清空所有图片
const clearImages = () => {
  imageList.value = []
}

// 暴露方法给父组件
defineExpose({
  uploadImages,
  getUnUploadedImages,
  getUploadedUrls,
  getCoverImage,
  getAllImages,
  clearImages,
  setCover,
  previewImage,
  imageList
})
</script>

<style scoped>
.image-picker {
  width: 100%;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.custom-upload-area {
  width: 100%;
}

.upload-box {
  width: 200rpx;
  height: 200rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d0d0d0;
  transition: all 0.2s ease;
}

.upload-box:active {
  transform: scale(0.95);
  border-color: #07c160;
  background-color: #f0f9f0;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.selected-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  width: 100%;
}

/* 网格布局样式 */
.grid-layout.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.grid-layout.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.grid-layout.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
}

/* 图片操作按钮容器 */
.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8rpx;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-actions,
.image-item:active .image-actions {
  opacity: 1;
}

/* 封面按钮 */
.cover-btn {
  align-self: flex-start;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  transition: all 0.2s ease;
}

.cover-btn.active {
  background: #07c160;
}

.cover-btn:active {
  transform: scale(0.95);
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
}

.delete-btn {
  align-self: flex-end;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;
}

.delete-btn:active {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(0.9);
}

.upload-box.small {
  width: 200rpx;
  height: 200rpx;
  background-color: #f8f9fa;
  border: 2px dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.upload-box.small:active {
  transform: scale(0.95);
  border-color: #07c160;
  background-color: #f0f9f0;
}

.upload-actions {
  margin-top: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.upload-info {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 16rpx;
}

/* 提示信息样式 */
.tips-container {
  background-color: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #07c160;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 4rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .grid-layout.grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 600rpx) {
  .grid-layout.grid-3,
  .grid-layout.grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
