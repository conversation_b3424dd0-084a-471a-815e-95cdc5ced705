<template>
	<view class="page">
	

		<!-- 双日期筛选区域 -->
		<view class="date-filter">
			<view class="date-section">
				<view class="date-item" @click="showReceiveDateSelector">
					<text class="date-label">实收日期</text>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<text class="date-value">{{ receiveDateText }}</text>
						<uni-icons type="bottom" size="12" color="#07c160" />
					</view>
					
				</view>
			</view>
			<view class="date-section">
				<view class="date-item" @click="showDueDateSelector">
					<text class="date-label">应收日期</text>
					<view style="display: flex;justify-content: space-between;align-items: center;"><text class="date-value">{{ dueDateText }}</text>
					<uni-icons type="bottom" size="12" color="#07c160" /></view>
					
				</view>
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-section">
			<view class="filter-item" @click="showBillStatusFilter">
				<text class="filter-text">{{ selectedBillStatus }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showTransactionTypeFilter">
				<text class="filter-text">{{ selectedTransactionType }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showRoomFilter">
				<text class="filter-text">{{ selectedRoom }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
		</view>

		<!-- 总收益统计 -->
		<view class="total-revenue">
			<text class="total-amount">{{ formatAmount(totalRevenue) }}</text>
			<text class="total-label">总收益(元)</text>
		</view>

		<!-- 收入支出统计 -->
		<view class="income-expense">
			<view class="income-section">
				<text class="income-amount">+{{ formatAmount(totalIncome) }}</text>
				<text class="income-label">收入(元)</text>
			</view>
			<view class="divider"></view>
			<view class="expense-section">
				<text class="expense-amount">{{ formatAmount(totalExpense) }}</text>
				<text class="expense-label">支出(元)</text>
			</view>
		</view>

		<!-- 流水记录列表 -->
		<view class="flow-list">
			<view class="flow-item" v-for="(record, index) in filteredRecords" :key="index" @click="viewRecordDetail(record)">
				<view class="flow-icon">
					<view class="icon-wrapper" :class="record.type">
						<uni-icons :type="getIconType(record.category)" size="20" color="#fff" />
					</view>
				</view>
				<view class="flow-content">
					<view class="flow-info">
						<text class="flow-title">{{ record.title }}</text>
						<text class="flow-room">{{ record.room }}</text>
					</view>
					<view class="flow-time">
						<text class="time-text">{{ record.time }}</text>
						<text class="payment-method">({{ record.paymentMethod }})</text>
					</view>
				</view>
				<view class="flow-amount">
					<text class="amount-text" :class="record.type">{{ formatAmountWithSign(record.amount, record.type) }}</text>
				</view>
			</view>
		</view>

		<!-- DateRangeSelector 组件 - 实收日期 -->
		<DateRangeSelector 
			ref="receiveDateSelector" 
			:startDate="receiveStartDate" 
			:endDate="receiveEndDate" 
			:customTimeTags="customTimeTags"
			@confirm="onReceiveDateConfirm" 
			@cancel="onReceiveDateCancel" 
		/>

		<!-- DateRangeSelector 组件 - 应收日期 -->
		<DateRangeSelector 
			ref="dueDateSelector" 
			:startDate="dueStartDate" 
			:endDate="dueEndDate" 
			:customTimeTags="customTimeTags"
			@confirm="onDueDateConfirm" 
			@cancel="onDueDateCancel" 
		/>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom" background-color="#fff">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeFilterPopup">取消</text>
					<text class="popup-title">{{ filterTitle }}</text>
					<text class="confirm-btn" @click="confirmFilter">确定</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" 
						v-for="(option, index) in currentFilterOptions" 
						:key="index"
						:class="{ active: selectedFilterIndex === index }"
						@click="selectFilterOption(index)">
						<text class="option-text">{{ option.text }}</text>
						<uni-icons v-if="selectedFilterIndex === index" type="checkmarkempty" size="16" color="#07c160" />
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted,getCurrentInstance } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import DateRangeSelector from '../components/DateRangeSelector/DateRangeSelector.vue'
	import { dayjs } from '@/utils/dayjs.min.js'
	const db = uniCloud.databaseForJQL()

	// 当前选中的标签页
	// const currentTab = ref('flow')

	// 实收日期相关
	const receiveStartDate = ref('')
	const receiveEndDate = ref('')
	const receiveDateSelector = ref(null)

	// 应收日期相关
	const dueStartDate = ref('')
	const dueEndDate = ref('')
	const dueDateSelector = ref(null)
	const building = ref([])
	// 自定义时间标签
	const customTimeTags = ref([
		{ name: '本月', days: 0, type: 'current_month' },
		{ name: '上月', days: 0, type: 'last_month' },
		{ name: '近3个月', days: 90, type: 'past' },
		{ name: '近6个月', days: 180, type: 'past' }
	])

	// 日期范围显示文本
	const receiveDateText = computed(() => {
		if (receiveStartDate.value && receiveEndDate.value) {
			return `${receiveStartDate.value}~${receiveEndDate.value}`
		}
		return '全部'
	})

	const dueDateText = computed(() => {
		if (dueStartDate.value && dueEndDate.value) {
			return `${dueStartDate.value}~${dueEndDate.value}`
		}
		return '全部'
	})

	// 筛选相关
	const selectedBillStatus = ref('全部类型')
	const selectedTransactionType = ref('全部收款方式')
	const selectedRoom = ref('全部楼房')

	const filterPopup = ref(null)
	const filterTitle = ref('')
	const currentFilterOptions = ref([])
	const selectedFilterIndex = ref(0)
	const currentFilterType = ref('')

	// 筛选选项
	const billStatusOptions = [
		{ text: '全部类型', value: 'all' },
		{ text: '押金', value: 1 },
		{ text: '账单', value: 2 },
		{ text: '退租', value: 3 },
		{ text: '换房', value: 4 },
		{ text: '维修', value: 5 },
		{ text: '保洁', value: 6 },
		{ text: '其他', value: 7 }
	]

	const transactionTypeOptions = [
		{ text: '全部收款方式', value: 'all' },
		{ text: '网上缴费', value: 0 },
		{ text: '微信转账', value: 1 },
		{ text: '支付宝转账', value: 2 },
		{ text: '银行卡转账', value: 3 },
		{ text: '现金', value: 4 },
		{ text: 'POS刷卡', value: 5 },
		{ text: '其他', value: 6 }
	]

	// 动态生成房源选项
	const roomOptions = computed(() => {
		const rooms = [{ text: '全部楼房', value: 'all' }]
		// 从building数组中获取房源名称和ID
		building.value.forEach(item => {
			rooms.push({ text: item.name, value: item.id })
		})
		return rooms
	})

	// 获取选中房源的ID
	const selectedRoomId = computed(() => {
		if (selectedRoom.value === '全部楼房') {
			return null // 全部楼房
		}
		const selectedOption = roomOptions.value.find(option => option.text === selectedRoom.value)
		return selectedOption?.value || null
	})

	// 统计数据 - 根据真实数据计算
	const totalRevenue = computed(() => {
		const filtered = getFilteredRecords()
		return filtered.reduce((sum, record) => {
			return record.isPay ? sum + record.amount : sum - record.amount
		}, 0)
	})
	
	const totalIncome = computed(() => {
		const filtered = getFilteredRecords()
		return filtered.reduce((sum, record) => record.isPay ? sum + record.amount : sum, 0)
	})
	
	const totalExpense = computed(() => {
		const filtered = getFilteredRecords()
		return filtered.reduce((sum, record) => !record.isPay ? sum + record.amount : sum, 0)
	})

	// 流水记录数据
	const records = ref([])
	const buildingList = ref([])

	// 获取筛选后的流水记录
	const getFilteredRecords = () => {
		let filtered = records.value

		// 根据流水类型筛选
		if (selectedBillStatus.value !== '全部类型') {
			const typeValue = billStatusOptions.find(option => option.text === selectedBillStatus.value)?.value
			if (typeValue !== 'all') {
				filtered = filtered.filter(record => record.type === typeValue)
			}
		}

		// 根据收款方式筛选
		if (selectedTransactionType.value !== '全部收款方式') {
			const paymentValue = transactionTypeOptions.find(option => option.text === selectedTransactionType.value)?.value
			if (paymentValue !== 'all') {
				const paymentMap = {
					0: '网上缴费',
					1: '微信转账', 
					2: '支付宝转账',
					3: '银行卡转账',
					4: '现金',
					5: 'POS刷卡',
					6: '其他'
				}
				filtered = filtered.filter(record => record.paymentMethod === paymentMap[paymentValue])
			}
		}

		// 根据房源筛选
		if (selectedRoomId.value) {
			filtered = filtered.filter(record => record.building_id === selectedRoomId.value)
		}

		// 根据实收日期范围筛选
		if (receiveStartDate.value && receiveEndDate.value) {
			const start = dayjs(receiveStartDate.value)
			const end = dayjs(receiveEndDate.value)
			filtered = filtered.filter(record => {
				const recordDate = dayjs(record.paymentDate)
				return recordDate.isBetween(start, end, null, '[]')
			})
		}

		// 根据应收日期范围筛选 (基于账单创建时间)
		if (dueStartDate.value && dueEndDate.value) {
			const start = dayjs(dueStartDate.value)
			const end = dayjs(dueEndDate.value)
			filtered = filtered.filter(record => {
				const createDate = dayjs(record.create_time)
				return createDate.isBetween(start, end, null, '[]')
			})
		}

		return filtered
	}

	// 筛选后的记录
	const filteredRecords = computed(() => {
		return getFilteredRecords().map(record => ({
			id: record._id,
			title: record.name,
			room: record.room_name,
			time: dayjs(record.create_time).format('MM-DD HH:mm'),
			paymentMethod: record.paymentMethod,
			amount: record.amount,
			type: record.isPay ? 'income' : 'expense',
			category: getRecordCategory(record.type),
			rawData: record
		}))
	})

	// 获取记录分类
	const getRecordCategory = (type) => {
		const categoryMap = {
			1: 'deposit',  // 押金
			2: 'rent',     // 账单/租金
			3: 'refund',   // 退租
			4: 'change',   // 换房
			5: 'repair',   // 维修
			6: 'clean',    // 保洁
			7: 'other'     // 其他
		}
		return categoryMap[type] || 'other'
	}

	onMounted(() => {
		// 初始化当前月份
		const now = dayjs()
		receiveStartDate.value = now.startOf('month').format('YYYY.MM.DD')
		receiveEndDate.value = now.endOf('month').format('YYYY.MM.DD')
		dueStartDate.value = ''
		dueEndDate.value = ''
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromRevenue', function(data) {
				console.log('acceptDataFromRevenue', data)
				building.value = data
				data.forEach(item =>{
					buildingList.value.push(item.id)
				})
				getRevenue()
			})
	})
	
	const getRevenue = async()=>{
		console.log("getRevenue",buildingList.value);
		try {
			// 获取流水记录
			const revenueRes = await db.collection('fangke_room_account').where(`building_id in ['${buildingList.value.join("','")}']`).get()
			console.log("获取所有流水单", revenueRes.data)
			
			records.value = revenueRes.data.map(item => ({
				...item,
				// 确保必要字段存在
				room_name: item.room_name || '',
				paymentMethod: item.paymentMethod || '',
				tenant_name: item.tenant_name || '',
				paymentDate: item.paymentDate || dayjs(item.create_time).format('YYYY-MM-DD')
			}))
			
			console.log("处理后的流水单", records.value)
			
			// 获取房源信息
			// const buildingRes = await db.collection("fangke_building").where(`_id in ['${buildingList.value.join("','")}']`).get()
			// console.log("获取全部楼房", buildingRes.data)
			
		} catch (error) {
			console.error("获取流水数据失败:", error)
			uni.showToast({
				title: '获取数据失败',
				icon: 'none'
			})
		}
	}

	onLoad((e) => {
		// 页面加载时的初始化
		if(e.uid){
			
		}
	})

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 切换标签页
	// const switchTab = (tab) => {
	// 	currentTab.value = tab
	// 	if (tab === 'summary') {
	// 		// 跳转到汇总页面
	// 		uni.navigateTo({
	// 			url: '/pagesB/bill/bill'
	// 		})
	// 	}
	// }

	// 显示实收日期选择器
	const showReceiveDateSelector = () => {
		receiveDateSelector.value?.open()
	}

	// 显示应收日期选择器
	const showDueDateSelector = () => {
		dueDateSelector.value?.open()
	}

	// 实收日期选择确认回调
	const onReceiveDateConfirm = (result) => {
		receiveStartDate.value = result.startDate
		receiveEndDate.value = result.endDate
		console.log('选择的实收日期范围:', result)
	}

	// 实收日期选择取消回调
	const onReceiveDateCancel = () => {
		console.log('取消选择实收日期范围')
	}

	// 应收日期选择确认回调
	const onDueDateConfirm = (result) => {
		dueStartDate.value = result.startDate
		dueEndDate.value = result.endDate
		console.log('选择的应收日期范围:', result)
	}

	// 应收日期选择取消回调
	const onDueDateCancel = () => {
		console.log('取消选择应收日期范围')
	}

	// 显示账单状态筛选
	const showBillStatusFilter = () => {
		filterTitle.value = '流水类型'
		currentFilterOptions.value = billStatusOptions
		currentFilterType.value = 'billStatus'
		selectedFilterIndex.value = billStatusOptions.findIndex(option => option.text === selectedBillStatus.value)
		filterPopup.value?.open()
	}

	// 显示交易类型筛选
	const showTransactionTypeFilter = () => {
		filterTitle.value = '收款方式'
		currentFilterOptions.value = transactionTypeOptions
		currentFilterType.value = 'transactionType'
		selectedFilterIndex.value = transactionTypeOptions.findIndex(option => option.text === selectedTransactionType.value)
		filterPopup.value?.open()
	}

	// 显示房源筛选
	const showRoomFilter = () => {
		filterTitle.value = '楼房筛选'
		currentFilterOptions.value = roomOptions.value
		currentFilterType.value = 'room'
		selectedFilterIndex.value = roomOptions.value.findIndex(option => option.text === selectedRoom.value)
		filterPopup.value?.open()
	}

	// 选择筛选选项
	const selectFilterOption = (index) => {
		selectedFilterIndex.value = index
	}

	// 确认筛选
	const confirmFilter = () => {
		const selectedOption = currentFilterOptions.value[selectedFilterIndex.value]
		
		switch (currentFilterType.value) {
			case 'billStatus':
				selectedBillStatus.value = selectedOption.text
				break
			case 'transactionType':
				selectedTransactionType.value = selectedOption.text
				break
			case 'room':
				selectedRoom.value = selectedOption.text
				break
		}
		
		closeFilterPopup()
	}

	// 关闭筛选弹窗
	const closeFilterPopup = () => {
		filterPopup.value?.close()
	}

	// 格式化金额显示
	const formatAmount = (amount) => {
		return (amount / 100).toFixed(2)
	}

	// 格式化金额显示（带符号）
	const formatAmountWithSign = (amount, type) => {
		const formattedAmount = (amount / 100).toFixed(2)
		return type === 'income' ? `+${formattedAmount}` : formattedAmount
	}

	// 获取图标类型
	const getIconType = (category) => {
		switch (category) {
			case 'rent':
				return 'home'
			case 'deposit':
				return 'wallet'
			case 'refund':
				return 'undo'
			case 'change':
				return 'loop'
			case 'repair':
				return 'gear'
			case 'clean':
				return 'star'
			default:
				return 'home'
		}
	}

	// 查看记录详情
	const viewRecordDetail = (record) => {
		console.log('查看记录详情:', record)
		// 这里可以跳转到记录详情页面
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	

	.title-text {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
	}

	/* 双日期筛选区域 */
	.date-filter {
		background: #e8f5e8;
		padding: 20rpx 30rpx;
		display: flex;
		gap: 40rpx;
	}

	.date-section {
		flex: 1;
	}

	.date-item {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.date-label {
		font-size: 24rpx;
		color: #07c160;
	}

	.date-value {
		font-size: 25rpx;
		color: #07c160;
		font-weight: 500;
	}

	/* 筛选区域 */
	.filter-section {
		display: flex;
		background: #fff;
		padding: 20rpx 30rpx;
		gap: 40rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.filter-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
		flex: 1;
	}

	.filter-text {
		font-size: 28rpx;
		color: #333;
	}

	/* 总收益统计 */
	.total-revenue {
		background: #fff;
		padding: 40rpx 30rpx 20rpx;
		text-align: center;
	}

	.total-amount {
		display: block;
		font-size: 48rpx;
		font-weight: 600;
		color: #07c160;
		margin-bottom: 10rpx;
	}

	.total-label {
		font-size: 24rpx;
		color: #999;
	}

	/* 收入支出统计 */
	.income-expense {
		background: #fff;
		padding: 20rpx 30rpx 40rpx;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.income-section, .expense-section {
		flex: 1;
		text-align: center;
	}

	.income-amount {
		display: block;
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
	}

	.expense-amount {
		display: block;
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
	}

	.income-label, .expense-label {
		font-size: 24rpx;
		color: #999;
	}

	.divider {
		width: 1px;
		height: 60rpx;
		background: #f0f0f0;
		margin: 0 40rpx;
	}

	/* 流水记录列表 */
	.flow-list {
		padding: 0 30rpx;
	}

	.flow-item {
		background: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		gap: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.flow-icon {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon-wrapper {
		width: 60rpx;
		height: 60rpx;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon-wrapper.income {
		background: #ff6b6b;
	}

	.icon-wrapper.expense {
		background: #07c160;
	}

	.flow-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.flow-info {
		display: flex;
		flex-direction: column;
		gap: 4rpx;
	}

	.flow-title {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.flow-room {
		font-size: 24rpx;
		color: #666;
	}

	.flow-time {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.time-text {
		font-size: 24rpx;
		color: #999;
	}

	.payment-method {
		font-size: 24rpx;
		color: #999;
	}

	.flow-amount {
		text-align: right;
	}

	.amount-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #07c160;
	}

	.amount-text.expense {
		color: #ff6b6b;
	}

	/* 筛选弹窗样式 */
	.filter-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #EBEDF0;
	}

	.cancel-btn {
		color: #666;
		font-size: 28rpx;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.confirm-btn {
		color: #07c160;
		font-weight: 500;
		font-size: 28rpx;
	}

	.filter-options {
		padding: 20rpx 0;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.filter-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.filter-option:last-child {
		border-bottom: none;
	}

	.filter-option.active {
		background: #f0f9ff;
	}

	.option-text {
		font-size: 28rpx;
		color: #333;
	}

	.filter-option.active .option-text {
		color: #07c160;
	}
</style>

