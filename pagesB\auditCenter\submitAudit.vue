<template>
	<view class="submit-audit">
		<view class="form-section">
			<uni-forms ref="auditForm" :modelValue="formData" :rules="rules">
				<uni-forms-item label="审核类型" required name="type">
					<uni-data-select 
						v-model="formData.type" 
						:localdata="auditTypes" 
						placeholder="请选择审核类型"
						@change="onTypeChange"
					></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="申请标题" required name="title">
					<uni-easyinput 
						v-model="formData.title" 
						placeholder="请输入申请标题"
						maxlength="100"
					/>
				</uni-forms-item>
				
				<uni-forms-item label="申请描述" required name="description">
					<uni-easyinput 
						v-model="formData.description" 
						type="textarea" 
						placeholder="请详细描述您的申请内容"
						maxlength="500"
					/>
				</uni-forms-item>
				
				<uni-forms-item label="房间选择" required name="room_id">
					<view class="room-selector" @click="showRoomPicker">
						<text class="room-text">{{ selectedRoomText }}</text>
						<uni-icons type="right" size="16" color="#999" />
					</view>
				</uni-forms-item>
				
				<uni-forms-item label="优先级" name="priority">
					<uni-data-select 
						v-model="formData.priority" 
						:localdata="priorityOptions" 
						placeholder="请选择优先级"
					></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="联系电话" required name="phone">
					<uni-easyinput 
						v-model="formData.phone" 
						placeholder="请输入联系电话"
						type="number"
					/>
				</uni-forms-item>
				
				<!-- 根据不同类型显示特定字段 -->
				<view v-if="formData.type === 'maintenance'" class="type-specific">
					<uni-forms-item label="故障位置" name="fault_location">
						<uni-easyinput 
							v-model="formData.fault_location" 
							placeholder="如：卫生间、厨房、客厅等"
						/>
					</uni-forms-item>
					<uni-forms-item label="故障描述" name="fault_description">
						<uni-easyinput 
							v-model="formData.fault_description" 
							type="textarea"
							placeholder="请详细描述故障情况"
						/>
					</uni-forms-item>
				</view>
				
				<view v-if="formData.type === 'cleaning'" class="type-specific">
					<uni-forms-item label="保洁类型" name="cleaning_type">
						<uni-data-select 
							v-model="formData.cleaning_type" 
							:localdata="cleaningTypes" 
							placeholder="请选择保洁类型"
						></uni-data-select>
					</uni-forms-item>
					<uni-forms-item label="预约时间" name="appointment_time">
						<uni-datetime-picker 
							v-model="formData.appointment_time"
							type="datetime"
							placeholder="请选择预约时间"
						/>
					</uni-forms-item>
				</view>
				
				<view v-if="formData.type === 'checkin'" class="type-specific">
					<uni-forms-item label="入住人数" name="checkin_count">
						<uni-number-box 
							v-model="formData.checkin_count" 
							:min="1" 
							:max="10"
						></uni-number-box>
					</uni-forms-item>
					<uni-forms-item label="预计入住时间" name="checkin_date">
						<uni-datetime-picker 
							v-model="formData.checkin_date"
							type="date"
							placeholder="请选择入住时间"
						/>
					</uni-forms-item>
				</view>
				
				<view v-if="formData.type === 'checkout'" class="type-specific">
					<uni-forms-item label="退租原因" name="checkout_reason">
						<uni-easyinput 
							v-model="formData.checkout_reason" 
							type="textarea"
							placeholder="请说明退租原因"
						/>
					</uni-forms-item>
					<uni-forms-item label="预计退租时间" name="checkout_date">
						<uni-datetime-picker 
							v-model="formData.checkout_date"
							type="date"
							placeholder="请选择退租时间"
						/>
					</uni-forms-item>
				</view>
				
				<uni-forms-item label="相关附件">
					<view class="attachment-section">
						<view class="attachment-list">
							<view 
								v-for="(file, index) in attachments" 
								:key="index"
								class="attachment-item"
							>
								<image 
									v-if="file.type === 'image'" 
									:src="file.url" 
									class="attachment-image"
									@click="previewImage(file.url)"
								/>
								<view v-else class="attachment-file">
									<uni-icons type="paperclip" size="20" color="#01B862" />
									<text class="file-name">{{ file.name }}</text>
								</view>
								<view class="attachment-remove" @click="removeAttachment(index)">
									<uni-icons type="clear" size="16" color="#ff4d4f" />
								</view>
							</view>
						</view>
						<view class="attachment-actions">
							<button class="attachment-btn" @click="chooseImage">
								<uni-icons type="camera" size="16" color="#01B862" />
								<text>添加图片</text>
							</button>
							<button class="attachment-btn" @click="chooseFile">
								<uni-icons type="paperclip" size="16" color="#01B862" />
								<text>添加文件</text>
							</button>
						</view>
					</view>
				</uni-forms-item>
			</uni-forms>
		</view>
		
		<view class="submit-section">
			<button class="submit-btn" @click="submitAudit">提交申请</button>
		</view>
		
		<!-- 房间选择弹窗 -->
		<uni-popup ref="roomPopup" type="bottom" border-radius="20rpx 20rpx 0 0">
			<view class="room-popup">
				<view class="popup-header">
					<text class="popup-title">选择房间</text>
					<view class="popup-close" @click="closeRoomPopup">
						<uni-icons type="close" size="18" color="#666" />
					</view>
				</view>
				<view class="room-list">
					<view 
						v-for="room in roomList" 
						:key="room._id"
						class="room-item"
						:class="{ selected: selectedRoom?._id === room._id }"
						@click="selectRoom(room)"
					>
						<view class="room-info">
							<text class="room-name">{{ room.name }}</text>
							<text class="room-address">{{ room.building_name }}</text>
						</view>
						<uni-icons 
							v-if="selectedRoom?._id === room._id" 
							type="checkmarkempty" 
							size="16" 
							color="#01B862" 
						/>
					</view>
				</view>
				<view class="popup-actions">
					<button class="popup-btn confirm-btn" @click="confirmRoomSelection">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	
	const db = uniCloud.databaseForJQL()
	
	// 响应式数据
	const auditForm = ref(null)
	const roomPopup = ref(null)
	const formData = ref({
		type: '',
		title: '',
		description: '',
		room_id: '',
		priority: 2,
		phone: '',
		// 维修相关
		fault_location: '',
		fault_description: '',
		// 保洁相关
		cleaning_type: '',
		appointment_time: '',
		// 入住相关
		checkin_count: 1,
		checkin_date: '',
		// 退租相关
		checkout_reason: '',
		checkout_date: ''
	})
	
	const attachments = ref([])
	const roomList = ref([])
	const selectedRoom = ref(null)
	const userId = ref('')
	
	// 审核类型选项
	const auditTypes = [
		{ value: 'maintenance', text: '维修申请' },
		{ value: 'cleaning', text: '保洁申请' },
		{ value: 'checkin', text: '入住申请' },
		{ value: 'checkout', text: '退租申请' },
		{ value: 'renewal', text: '续租申请' },
		{ value: 'transfer', text: '换房申请' }
	]
	
	// 优先级选项
	const priorityOptions = [
		{ value: 1, text: '低优先级' },
		{ value: 2, text: '中优先级' },
		{ value: 3, text: '高优先级' }
	]
	
	// 保洁类型选项
	const cleaningTypes = [
		{ value: 'daily', text: '日常保洁' },
		{ value: 'deep', text: '深度保洁' },
		{ value: 'checkout', text: '退租保洁' },
		{ value: 'maintenance', text: '维修后保洁' }
	]
	
	// 表单验证规则
	const rules = ref({
		type: {
			rules: [{ required: true, errorMessage: '请选择审核类型' }]
		},
		title: {
			rules: [{ required: true, errorMessage: '请输入申请标题' }]
		},
		description: {
			rules: [{ required: true, errorMessage: '请输入申请描述' }]
		},
		room_id: {
			rules: [{ required: true, errorMessage: '请选择房间' }]
		},
		phone: {
			rules: [
				{ required: true, errorMessage: '请输入联系电话' },
				{ pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号' }
			]
		}
	})
	
	// 计算属性
	const selectedRoomText = computed(() => {
		return selectedRoom.value ? 
			`${selectedRoom.value.name} - ${selectedRoom.value.building_name}` : 
			'请选择房间'
	})
	
	// 页面加载
	onLoad((e) => {
		userId.value = uniCloud.getCurrentUserInfo().uid
		if (!userId.value) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
			return
		}
		
		// 获取用户信息，填充手机号
		getUserInfo()
		// 获取房间列表
		getRoomList()
	})
	
	// 获取用户信息
	const getUserInfo = async () => {
		try {
			const userInfo = uniCloud.getCurrentUserInfo()
			if (userInfo.mobile) {
				formData.value.phone = userInfo.mobile
			}
		} catch (error) {
			console.error('获取用户信息失败:', error)
		}
	}
	
	// 获取房间列表
	const getRoomList = async () => {
		try {
			// 这里应该根据用户权限获取可申请的房间
			// 租客只能看到自己租住的房间，房东可以看到所有房间
			const res = await db.collection("fangke_room")
				.where(`uid=="${userId.value}"`)
				.field("_id,name,building_name,building_id")
				.get()
			
			roomList.value = res.data
		} catch (error) {
			console.error('获取房间列表失败:', error)
			uni.showToast({
				title: '获取房间列表失败',
				icon: 'none'
			})
		}
	}
	
	// 类型变化处理
	const onTypeChange = (value) => {
		// 根据类型设置默认标题
		const typeMap = {
			maintenance: '维修申请',
			cleaning: '保洁申请',
			checkin: '入住申请',
			checkout: '退租申请',
			renewal: '续租申请',
			transfer: '换房申请'
		}
		
		if (!formData.value.title || auditTypes.some(type => formData.value.title === type.text)) {
			formData.value.title = typeMap[value] || ''
		}
	}
	
	// 显示房间选择器
	const showRoomPicker = () => {
		if (roomList.value.length === 0) {
			uni.showToast({
				title: '暂无可选房间',
				icon: 'none'
			})
			return
		}
		roomPopup.value?.open()
	}
	
	// 选择房间
	const selectRoom = (room) => {
		selectedRoom.value = room
	}
	
	// 确认房间选择
	const confirmRoomSelection = () => {
		if (selectedRoom.value) {
			formData.value.room_id = selectedRoom.value._id
			closeRoomPopup()
		}
	}
	
	// 关闭房间选择弹窗
	const closeRoomPopup = () => {
		roomPopup.value?.close()
	}
	
	// 选择图片
	const chooseImage = () => {
		uni.chooseImage({
			count: 5,
			sizeType: ['compressed'],
			sourceType: ['camera', 'album'],
			success: (res) => {
				res.tempFilePaths.forEach((filePath, index) => {
					attachments.value.push({
						type: 'image',
						url: filePath,
						name: `图片${attachments.value.length + index + 1}.jpg`
					})
				})
			}
		})
	}
	
	// 选择文件（H5平台）
	const chooseFile = () => {
		// #ifdef H5
		const input = document.createElement('input')
		input.type = 'file'
		input.multiple = true
		input.accept = '.pdf,.doc,.docx,.txt'
		input.onchange = (e) => {
			const files = e.target.files
			for (let i = 0; i < files.length; i++) {
				const file = files[i]
				attachments.value.push({
					type: 'file',
					url: URL.createObjectURL(file),
					name: file.name,
					file: file
				})
			}
		}
		input.click()
		// #endif
		
		// #ifndef H5
		uni.showToast({
			title: '当前平台不支持文件选择',
			icon: 'none'
		})
		// #endif
	}
	
	// 预览图片
	const previewImage = (url) => {
		const imageUrls = attachments.value
			.filter(item => item.type === 'image')
			.map(item => item.url)
		
		uni.previewImage({
			urls: imageUrls,
			current: url
		})
	}
	
	// 移除附件
	const removeAttachment = (index) => {
		attachments.value.splice(index, 1)
	}
	
	// 提交申请
	const submitAudit = async () => {
		try {
			// 表单验证
			await auditForm.value.validate()
			
			uni.showLoading({
				title: '提交中...'
			})
			
			// 构建提交数据
			const auditData = {
				type: formData.value.type,
				title: formData.value.title,
				description: formData.value.description,
				applicant_id: userId.value,
				applicant_name: uniCloud.getCurrentUserInfo().nickname || '用户',
				applicant_phone: formData.value.phone,
				room_id: formData.value.room_id,
				room_info: selectedRoomText.value,
				priority: formData.value.priority,
				attachments: attachments.value,
				related_data: buildRelatedData()
			}
			
			// 调用云函数提交
			const auditObj = uniCloud.importObject('audit')
			const res = await auditObj.createAuditRecord(auditData)
			
			uni.hideLoading()
			
			if (res.errCode === 0) {
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} else {
				throw new Error(res.errMsg)
			}
		} catch (error) {
			uni.hideLoading()
			console.error('提交申请失败:', error)
			uni.showToast({
				title: error.message || '提交失败',
				icon: 'none'
			})
		}
	}
	
	// 构建相关数据
	const buildRelatedData = () => {
		const related = {}
		
		switch (formData.value.type) {
			case 'maintenance':
				related.maintenance_info = {
					fault_location: formData.value.fault_location,
					fault_description: formData.value.fault_description
				}
				break
			case 'cleaning':
				related.cleaning_info = {
					cleaning_type: formData.value.cleaning_type,
					appointment_time: formData.value.appointment_time
				}
				break
			case 'checkin':
				related.checkin_info = {
					checkin_count: formData.value.checkin_count,
					checkin_date: formData.value.checkin_date
				}
				break
			case 'checkout':
				related.checkout_info = {
					checkout_reason: formData.value.checkout_reason,
					checkout_date: formData.value.checkout_date
				}
				break
		}
		
		return related
	}
</script>

<style lang="scss" scoped>
.submit-audit {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.form-section {
	background-color: #fff;
	margin: 20rpx;
	border-radius: 12rpx;
	padding: 30rpx;
}

.room-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #e0e0e0;
	
	.room-text {
		font-size: 28rpx;
		color: #333;
	}
}

.type-specific {
	margin-top: 30rpx;
	padding-top: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.attachment-section {
	.attachment-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-bottom: 30rpx;
		
		.attachment-item {
			position: relative;
			
			.attachment-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 8rpx;
			}
			
			.attachment-file {
				display: flex;
				align-items: center;
				padding: 20rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				min-width: 200rpx;
				
				.file-name {
					font-size: 24rpx;
					color: #333;
					margin-left: 8rpx;
				}
			}
			
			.attachment-remove {
				position: absolute;
				top: -10rpx;
				right: -10rpx;
				width: 32rpx;
				height: 32rpx;
				background-color: #fff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			}
		}
	}
	
	.attachment-actions {
		display: flex;
		gap: 20rpx;
		
		.attachment-btn {
			display: flex;
			align-items: center;
			padding: 20rpx 30rpx;
			background-color: #f8f8f8;
			border: 1rpx dashed #01B862;
			border-radius: 8rpx;
			font-size: 24rpx;
			color: #01B862;
			
			text {
				margin-left: 8rpx;
			}
		}
	}
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	background-color: #fff;
	border-top: 1rpx solid #e0e0e0;
	
	.submit-btn {
		width: 100%;
		padding: 24rpx;
		background-color: #01B862;
		color: #fff;
		border-radius: 8rpx;
		font-size: 28rpx;
		border: none;
	}
}

.room-popup {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.popup-close {
			padding: 10rpx;
		}
	}
	
	.room-list {
		max-height: 60vh;
		overflow-y: auto;
		
		.room-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #f8f8f8;
			
			&.selected {
				background-color: #f6ffed;
			}
			
			.room-info {
				.room-name {
					display: block;
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.room-address {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	.popup-actions {
		padding: 30rpx;
		border-top: 1rpx solid #f0f0f0;
		
		.popup-btn {
			width: 100%;
			padding: 24rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			border: none;
			
			&.confirm-btn {
				background-color: #01B862;
				color: #fff;
			}
		}
	}
}
</style>
