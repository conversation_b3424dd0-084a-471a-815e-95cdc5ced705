<template>
	<view class="container">
		<scroll-view class="content" scroll-y>
			<view class="form-section">
				<view class="form-item">
					<text class="label required">户型名称</text>
					<input class="input" type="text" placeholder="请输入户型名称" v-model="formData.name" />
				</view>
				<view class="form-item">
					<text class="label required">面积(㎡)</text>
					<input class="input" type="digit" placeholder="请输入面积" v-model="formData.area" @input="onAreaInput" />
				</view>
				<view class="form-item">
					<text class="label required">付款方式</text>
					<view class="payment-select" @click="showPaymentPopup">
						<text class="select-text">{{ formData.paymentType || '请选择付款方式' }}</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-item">
					<text class="label required">租金（元/月）</text>
					<input class="input" type="digit" placeholder="请输入租金（元/月）" v-model="formData.rent" @input="onRentInput" />
				</view>
				
				<view class="form-item" @click="selectHouseType">
					<text class="label">实际户型</text>
					<view class="house-type">
						<text>{{ formData.houseType || '1室1厅0卫' }}</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-item description-item">
					<text class="label">房型描述</text>
					<textarea class="textarea" placeholder="请输入房型描述信息" v-model="formData.description"></textarea>
				</view>
			</view>
			<view class="facilities-section">
				<text class="section-title">房间配置</text>
				<view class="facilities-grid">
					<view v-for="(item, index) in facilities" :key="index" class="facility-item"
						:class="{ active: selectedFacilities.includes(item.value) }" @click="toggleFacility(item.value)">
						{{ item.name }}
					</view>
				</view>
			</view>
			<view class="photos-section" >
				<text class="section-title">房型照片</text>
				<image-picker 
					ref="imagePicker" 
					v-model="imageFiles" 
					:max-count="9"
					@change="onImageChange"
				></image-picker>
			</view>
		</scroll-view>
		<view class="footer">
			<button class="submit-btn" @click="submitForm">保存</button>
		</view>


		<!-- 户型选择弹窗 -->
		<uni-popup ref="houseTypePopup" type="bottom" background-color="#fff" border-radius="20px 20px 0 0">
			<view class="house-type-popup">
				<view class="popup-header">
					<view class="cancel-btn" @click="closeHouseTypePopup">取消</view>
					<text class="popup-title">选择户型</text>
					<view class="confirm-house-btn" @click="confirmHouseTypeSelection">确认</view>
				</view>
				<view class="house-picker-container">
					<picker-view 
						class="house-picker" 
						:value="houseTypeValue" 
						@change="onHouseTypePickerChange"
						:indicator-style="indicatorStyle">
						<picker-view-column>
							<view v-for="(room, index) in roomOptions" :key="index" class="picker-item">
								<text class="picker-text">{{ room }}室</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view v-for="(hall, index) in hallOptions" :key="index" class="picker-item">
								<text class="picker-text">{{ hall }}厅</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view v-for="(toilet, index) in toiletOptions" :key="index" class="picker-item">
								<text class="picker-text">{{ toilet }}卫</text>
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</uni-popup>

	
		
		
	</view>
</template>
<script lang="ts" setup>
	import { ref ,computed, onMounted,
    getCurrentInstance} from 'vue';
	import {onLoad } from '@dcloudio/uni-app'
	import ImagePicker from '@/components/image-picker/image-picker.vue'
	const db = uniCloud.databaseForJQL()
	// 弹窗引用
	const templatePopup = ref(null);
	const houseTypePopup = ref(null);
	const rentDatePopup = ref(null);
	const imagePicker = ref(null);
	
	const formData = ref({
		name: '',
		area: '',
		paymentType: '',
		rent: '',
		houseType: '1室1厅0卫',
		description: ''
	});
	const selectedFacilities = ref([]);
	const imageFiles = ref([]);
	
	// 图片样式配置
	const imageStyles = {
		width: '200rpx',
		height: '200rpx',
		border: {
			radius: '12rpx'
		}
	};
	
	// 滑动选择器相关
	const indicatorStyle = 'height: 76rpx; background-color: rgba(7, 193, 96, 0.1); border-top: 2px solid #07c160; border-bottom: 2px solid #07c160;';
	const uid = ref("")
	const _id = ref("")
	const model = ref({})
	// 户型选择器相关
	const houseTypeValue = ref([0, 0, 0]); // [房间索引, 厅索引, 卫生间索引]
	const roomOptions = [0, 1, 2, 3, 4, 5]; // 0-5室
	const hallOptions = [0, 1, 2, 3]; // 0-3厅
	const toiletOptions = [0, 1, 2, 3]; // 0-3卫
	
	// 价格模板数据（模拟数据，实际应该从后端获取）
	const priceTemplates = ref([

	]);
	let eventChannel ;
	// 交租日期选择器相关
	const selectedRentDateIndex = ref(0); // 默认选择1号（索引0）
	const rentDateOptions = Array.from({length: 31}, (_, i) => i + 1); // 1-31号
	
	const facilities = [
		{ name: '智能锁', value: 1 }, 
		{ name: '床', value: 2 }, 
		{ name: '衣柜', value: 3 }, 
		{ name: '书桌椅', value: 4 },
		{ name: '暖气', value: 5 }, 
		{ name: '天然气', value: 6 }, 
		{ name: '宽带', value: 7 }, 
		{ name: '电视', value: 8 },
		{ name: '冰箱', value: 9 }, 
		{ name: '洗衣机', value: 10 }, 
		{ name: '空调', value: 11 }, 
		{ name: '热水器', value: 12 },
		{ name: '微波炉', value: 13 }, 
		{ name: '油烟机', value: 14 }, 
		{ name: '电磁炉', value: 15 }, 
		{ name: '阳台', value: 16 },
		{ name: '可做饭', value: 17 }, 
		{ name: '独立卫生间', value: 18 }, 
		{ name: '沙发', value: 19 }, 
		{ name: '电梯', value: 20 }
	];
	
	onMounted(()=>{
		 const instance = getCurrentInstance().proxy
		eventChannel = instance.getOpenerEventChannel();
		    
	})
	
	onLoad((e)=>{
		uid.value = uniCloud.getCurrentUserInfo().uid
		console.log("layout",e);
		if(e.id){
			_id.value = e.id
			db.collection("fangke_room_model").doc(_id.value).get().then(res =>{
				console.log("获取户型",res.data[0]);
				if(res.errCode == 0){
					updateModel(res.data[0])
				}
			})
			uni.setNavigationBarTitle({
				title:'编辑房型'
			})
		}
	})
	
	const updateModel = (data) =>{
		formData.value.name = data.name || ''
		formData.value.area = parseFloat(data.area)/100 +''|| ''
		formData.value.paymentType = data.payment || ''
		formData.value.rent = parseFloat(data.rent)/100 +'' || ''
		formData.value.description = data.desc || ''
		formData.value.houseType  = data.layout || ''
		selectedFacilities.value = data.devices || []
		// 处理已有图片数据
		if (data.images && data.images.length > 0) {
			imageFiles.value = data.images
		}
	}
	
	// 图片变化回调
	const onImageChange = (images) => {
		// 可以在这里处理图片变化的逻辑
		console.log('图片列表变化:', images);
	}
	

	const closeTemplatePopup = () => {
		templatePopup.value?.close();
	};



	const selectTemplate = (template: any) => {
		// 保留原有方法以防其他地方调用
		formData.value.rent = template.rent.toString();
		formData.value.paymentType = template.paymentType;
		
		closeTemplatePopup();
		
		uni.showToast({
			title: '模板已应用',
			icon: 'success',
			duration: 1500
		});
	};

	const createNewTemplate = () => {
		closeTemplatePopup();
		// 跳转到新建模板页面
		uni.navigateTo({
			url:'/pagesB/priceModel/priceModel'
		})
	};

	const showPaymentPopup = () => {
		uni.showActionSheet({
			itemList: ['押一付一', '押一付三', '押二付一', '押三付一', '半年付', '年付'],
			success: (res) => {
				const types = ['押一付一', '押一付三', '押二付一', '押三付一', '半年付', '年付'];
				formData.value.paymentType = types[res.tapIndex];
			}
		});
	};
	const selectHouseType = () => {
		// 选择户型 - 显示滑动选择器弹窗
		console.log("选择户型");
		houseTypePopup.value?.open();
	};

	// 户型选择器相关方法
	const closeHouseTypePopup = () => {
		houseTypePopup.value?.close();
	};

	const onHouseTypePickerChange = (e: any) => {
		console.log('户型选择变化:', e.detail.value);
		houseTypeValue.value = e.detail.value;
	};

	const confirmHouseTypeSelection = () => {
		const roomIndex = houseTypeValue.value[0];
		const hallIndex = houseTypeValue.value[1];
		const toiletIndex = houseTypeValue.value[2];
		
		const roomNum = roomOptions[roomIndex];
		const hallNum = hallOptions[hallIndex];
		const toiletNum = toiletOptions[toiletIndex];
		
		// 特殊处理单间情况
		if (roomNum === 0 || (roomNum === 1 && hallNum === 0 )) {
			formData.value.houseType = '单间';
		} else {
			formData.value.houseType = `${roomNum}室${hallNum}厅${toiletNum}卫`;
		}
		
		closeHouseTypePopup();
		
		uni.showToast({
			title: '户型已选择',
			icon: 'success',
			duration: 1500
		});
	};

	const toggleFacility = (facilityValue: number) => {
		const index = selectedFacilities.value.indexOf(facilityValue);
		if (index === -1) {
			selectedFacilities.value.push(facilityValue);
		} else {
			selectedFacilities.value.splice(index, 1);
		}
	};

	const submitForm = async () => {
		// 表单验证
		if (!formData.value.name) {
			uni.showToast({
				title: '请输入户型名称',
				icon: 'none'
			});
			return;
		}
		if (!formData.value.area) {
			uni.showToast({
				title: '请输入面积',
				icon: 'none'
			});
			return;
		}
		if (!formData.value.paymentType) {
			uni.showToast({
				title: '请选择付款方式',
				icon: 'none'
			});
			return;
		}
		if (!formData.value.rent) {
			uni.showToast({
				title: '请输入租金',
				icon: 'none'
			});
			return;
		}
		
		// 先上传图片
		const unUploadedImages = imagePicker.value.getUnUploadedImages();
		if (unUploadedImages.length > 0) {
			uni.showLoading({
				title: '正在上传图片...'
			});
			
			try {
				await imagePicker.value.uploadImages(unUploadedImages);
				uni.hideLoading();
			} catch (error) {
				uni.hideLoading();
				console.error('图片上传失败:', error);
				uni.showToast({
					title: '图片上传失败',
					icon: 'none'
				});
				return;
			}
		}
		
		// 保存表单数据
		uni.showLoading({
			title: '正在保存...'
		});
		
		let params = {
			uid:uid.value,
			name:formData.value.name,
			area:parseFloat(formData.value.area)*100,
			payment:formData.value.paymentType,
			rent:parseFloat(formData.value.rent)*100,
			layout:formData.value.houseType,
			desc:formData.value.description,
			devices:selectedFacilities.value,
			images:imagePicker.value.getUploadedUrls(), // 使用组件方法获取已上传的图片URL
		}
		console.log("参数",params);
		
		try {
			if(_id.value == ""){
				await uni.$lkj.api.addRoomModel(params).then(res =>{
					console.log("添加户型",res);
					if(res.errCode == 0){
						eventChannel.emit('updateLayout', {
						  data: 'test'
						});
						uni.hideLoading();
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				})
			}else{
				params._id = _id.value;
				await uni.$lkj.api.updateRoomModel(params).then(res =>{
					console.log("修改户型",res);
					if(res.errCode == 0){
						eventChannel.emit('updateLayout', {
						  data: 'test'
						});
						uni.hideLoading();
						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				})
			}
		} catch (error) {
			uni.hideLoading();
			console.error('保存失败:', error);
			uni.showToast({
				title: '保存失败',
				icon: 'none'
			});
		}
	};

	const onAreaInput = (e: any) => {
		// 只允许数字和小数点
		const value = e.detail.value.replace(/[^\d.]/g, '');
		formData.value.area = value;
	};

	const onRentInput = (e: any) => {
		// 只允许数字
		const value = e.detail.value.replace(/[^\d]/g, '');
		formData.value.rent = value;
	};


	// const showRentDatePopup = () => {
	// 	// 实现显示交租日期选择弹窗的逻辑
	// 	console.log("显示交租日期选择弹窗");
	// 	rentDatePopup.value?.open();
	// };

	// const closeRentDatePopup = () => {
	// 	rentDatePopup.value?.close();
	// };

	// const onRentDatePickerChange = (e: any) => {
	// 	console.log('选择交租日期:', e.detail.value[0]);
	// 	selectedRentDateIndex.value = e.detail.value[0];
	// };

	// const confirmRentDateSelection = () => {
	// 	const selectedDate = rentDateOptions[selectedRentDateIndex.value];
	// 	formData.value.rentDate = selectedDate;
	// 	closeRentDatePopup();
	// 	uni.showToast({
	// 		title: '交租日期已选择',
	// 		icon: 'success',
	// 		duration: 1500
	// 	});
	// };

	
</script>
<style>
	page {
		height: 100%;
		background-color: #f5f7fa;
	}

	.container {
		height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #f5f7fa;
	}

	.content {
		flex: 1;
		overflow: auto;
		padding-bottom: 140rpx;
	}

	.form-section {
		background-color: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 24rpx;
		border-bottom: 1px solid #f0f0f0;
		min-height: 100rpx;
		box-sizing: border-box;
	}

	.form-item:last-child {
		border-bottom: none;
		border-radius: 0 0 16rpx 16rpx;
	}

	.form-item:first-child {
		border-radius: 16rpx 16rpx 0 0;
	}

	.description-item {
		align-items: flex-start;
		flex-direction: column;
		min-height: auto;
	}

	.label {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		flex-shrink: 0;
		min-width: 160rpx;
	}

	.required::before {
		content: '*';
		color: #ff4757;
		margin-right: 8rpx;
		font-weight: bold;
	}

	.input {
		flex: 1;
		height: 80rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		color: #333;
		border: 1px solid #e8e8e8;
		margin-left: 24rpx;
		box-sizing: border-box;
	}

	.input:focus {
		background-color: #fff;
		border-color: #07c160;
	}

	.textarea {
		width: 100%;
		min-height: 160rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 24rpx;
		font-size: 28rpx;
		color: #333;
		border: 1px solid #e8e8e8;
		margin-top: 20rpx;
		box-sizing: border-box;
		resize: none;
	}

	.textarea:focus {
		background-color: #fff;
		border-color: #07c160;
	}

	.template-container {
		flex: 1;
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-left: 24rpx;
	}

	.select {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 0 24rpx;
		border: 1px solid #e8e8e8;
		box-sizing: border-box;
	}

	.select:active {
		background-color: #f0f0f0;
	}

	.select-text {
		font-size: 28rpx;
		color: #666;
	}

	.create-btn {
		width: 120rpx !important;
		height: 80rpx !important;
		line-height: 80rpx !important;
		text-align: center;
		background-color: #07c160 !important;
		color: #fff !important;
		border-radius: 12rpx !important;
		font-size: 26rpx !important;
		margin: 0 !important;
		padding: 0 !important;
		border: none !important;
		flex-shrink: 0;
	}

	.create-btn:active {
		background-color: #06a84e !important;
	}

	.house-type,
	.payment-select {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		color: #333;
		border: 1px solid #e8e8e8;
		margin-left: 24rpx;
		box-sizing: border-box;
	}

	.house-type:active,
	.payment-select:active {
		background-color: #f0f0f0;
	}

	.rent-date-select {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		color: #333;
		border: 1px solid #e8e8e8;
		margin-left: 24rpx;
		box-sizing: border-box;
	}

	.rent-date-select:active {
		background-color: #f0f0f0;
	}

	.facilities-section {
		background-color: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 32rpx 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.section-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 32rpx;
		display: block;
	}

	.facilities-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 16rpx;
	}

	.facility-item {
		height: 76rpx;
		line-height: 76rpx;
		text-align: center;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		font-size: 26rpx;
		color: #666;
		border: 1px solid #e8e8e8;
		transition: all 0.2s ease;
	}

	.facility-item:active {
		transform: scale(0.95);
	}

	.facility-item.active {
		background-color: #e8f5e8;
		color: #07c160;
		border-color: #07c160;
		font-weight: 500;
	}

	.photos-section {
		background-color: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 32rpx 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.photo-upload {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	/* uni-file-picker 组件样式 */
	.photo-upload :deep(.uni-file-picker) {
		width: 100%;
	}

	.photo-upload :deep(.uni-file-picker__container) {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}

	.photo-upload :deep(.uni-file-picker__files) {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}

	.photo-upload :deep(.uni-file-picker__file) {
		width: 200rpx;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.photo-upload :deep(.uni-file-picker__file-image) {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.photo-upload :deep(.uni-file-picker__choose) {
		width: 200rpx;
		height: 200rpx;
		background-color: #f8f9fa;
		border: 2px dashed #d0d0d0;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
	}

	.photo-upload :deep(.uni-file-picker__choose:hover) {
		border-color: #07c160;
		background-color: #f0f9f0;
	}

	.photo-upload :deep(.uni-file-picker__choose-text) {
		font-size: 24rpx;
		color: #999;
		margin-top: 8rpx;
	}

	.upload-box {
		width: 200rpx;
		height: 200rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 2px dashed #d0d0d0;
		transition: all 0.2s ease;
	}

	.upload-box:active {
		transform: scale(0.95);
		border-color: #07c160;
		background-color: #f0f9f0;
	}

	.upload-text {
		font-size: 24rpx;
		color: #999;
		margin-top: 16rpx;
	}

	.upload-actions {
		margin-top: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.upload-status {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
	}

	.upload-text {
		font-size: 26rpx;
		color: #07c160;
		font-weight: 500;
	}

	.upload-progress {
		width: 400rpx;
		height: 16rpx;
		border-radius: 8rpx;
		background-color: #f0f0f0;
	}

	.upload-info {
		font-size: 26rpx;
		color: #666;
		text-align: center;
	}

	.upload-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		z-index: 5;
	}

	.uploading-text {
		font-size: 24rpx;
		color: #07c160;
		font-weight: 500;
	}

	.pending-text {
		font-size: 24rpx;
		color: #fff;
		font-weight: 500;
	}

	/* 自定义上传区域样式 */
	.custom-upload-area {
		width: 100%;
	}

	.selected-images {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		width: 100%;
	}

	.image-item {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.image-preview {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 12rpx;
	}

	.delete-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 32rpx;
		height: 32rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.delete-btn:active {
		background-color: rgba(0, 0, 0, 0.8);
		transform: scale(0.9);
	}

	.upload-box.small {
		width: 200rpx;
		height: 200rpx;
		background-color: #f8f9fa;
		border: 2px dashed #d0d0d0;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
	}

	.upload-box.small:active {
		transform: scale(0.95);
		border-color: #07c160;
		background-color: #f0f9f0;
	}

	.footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 24rpx 20rpx;
		background-color: #fff;
		border-top: 1px solid #e8e8e8;
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.submit-btn {
		width: 100% !important;
		height: 96rpx !important;
		line-height: 96rpx !important;
		text-align: center;
		background-color: #07c160 !important;
		color: #fff !important;
		border-radius: 16rpx !important;
		font-size: 32rpx !important;
		font-weight: 600 !important;
		margin: 0 !important;
		border: none !important;
		box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
		transition: all 0.2s ease;
	}

	.submit-btn:active {
		background-color: #06a84e !important;
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
	}

	/* 价格模板弹窗样式 */
	.template-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #f0f0f0;
		position: relative;
	}

	.cancel-btn,
	.create-template-btn {
		color: #666 !important;
		font-size: 28rpx !important;
	}

	.create-template-btn {
		color: #07c160 !important;
		font-weight: 500 !important;
	}

	.popup-title {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.template-picker-container {
		padding: 24rpx;
	}

	.template-picker {
		width: 100%;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.picker-item {
		height: 76rpx;
		line-height: 76rpx;
		text-align: center;
		background-color: #f8f9fa;
		border-bottom: 1px solid #f0f0f0;
	}

	.picker-item:last-child {
		border-bottom: none;
	}

	.picker-item:active {
		background-color: #f0f0f0;
	}

	.picker-template-name {
		display: block;
		font-size: 30rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 8rpx;
	}

	.picker-template-desc {
		display: block;
		font-size: 26rpx;
		color: #666;
	}

	.picker-actions {
		display: flex;
		justify-content: center;
		padding: 24rpx;
		border-top: 1px solid #f0f0f0;
	}

	.confirm-btn {
		width: 100%;
		height: 80rpx !important;
		line-height: 80rpx !important;
		text-align: center;
		background-color: #07c160 !important;
		color: #fff !important;
		border-radius: 12rpx !important;
		font-size: 26rpx !important;
		margin: 0 !important;
		padding: 0 !important;
		border: none !important;
		flex-shrink: 0;
	}

	.confirm-btn:active {
		background-color: #06a84e !important;
	}

	.empty-template {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 32rpx;
		text-align: center;
	}

	.empty-text {
		font-size: 30rpx;
		color: #999;
		margin: 24rpx 0 12rpx;
	}

	.empty-desc {
		font-size: 26rpx;
		color: #ccc;
	}

	/* 户型选择弹窗样式 */
	.house-type-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.house-picker-container {
		padding: 24rpx;
	}

	.house-picker {
		width: 100%;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.picker-text {
		display: block;
		font-size: 30rpx;
		color: #333;
		text-align: center;
		line-height: 76rpx;
	}

	.confirm-house-btn {
		color: #07c160 !important;
		font-weight: 500 !important;
		font-size: 28rpx !important;
	}

	/* 交租日期选择弹窗样式 */
	.rent-date-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.rent-picker-container {
		padding: 24rpx;
	}

	.rent-picker {
		width: 100%;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.picker-item {
		height: 76rpx;
		line-height: 76rpx;
		text-align: center;
		background-color: #f8f9fa;
		border-bottom: 1px solid #f0f0f0;
	}

	.picker-item:last-child {
		border-bottom: none;
	}

	.picker-item:active {
		background-color: #f0f0f0;
	}

	.picker-text {
		display: block;
		font-size: 30rpx;
		color: #333;
		text-align: center;
		line-height: 76rpx;
	}

	.confirm-rent-btn {
		color: #07c160 !important;
		font-weight: 500 !important;
		font-size: 28rpx !important;
	}
</style>