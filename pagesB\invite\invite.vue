<template>
	<view class="container">

		<uni-section title="您的邀请码" type="line">
			<view class="layout">
				<text class="content">{{invit_code}}</text>
				<button class="btn" @click="copy(invit_code)" size="mini" type="primary"
					style="width: 150rpx;">复制</button>
			</view>
		</uni-section>

		<uni-section title="添加邀请码" type="line">
			<view class="layout">
				<uni-easyinput placeholder="请输入对方邀请码" style="margin-right: 20rpx;" v-model="input"></uni-easyinput>
				<button class="btn" @click="add" size="mini" type="primary">添加</button>
			</view>
		</uni-section>

		<uni-section title="删除邀请码" type="line">
			<view class="layout">
				<uni-easyinput placeholder="不能删除自己的邀请码" style="margin-right: 20rpx;" v-model="input2"></uni-easyinput>
				<button class="btn" @click="del(input2)" size="mini" type="primary">删除</button>
			</view>
		</uni-section>

		<uni-section title="已添加的邀请码" type="line">
			<view class="layout">
				<uni-list>
					<view v-for="(item,index) in list" :key="index" @click="copy(list[item])">
						<uni-list-item :title="item" right-text="点击可复制"></uni-list-item>
					</view>
				</uni-list>
			</view>
		</uni-section>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';

	const api = uniCloud.importObject("ApiFuntion");

	// 定义组件状态
	const invit_code = ref('');
	const id = ref('');
	const input = ref('');
	const input2 = ref('');
	const list = ref([]);

	// 生命周期钩子 - 页面加载时
	onMounted(() => {
		id.value = uniCloud.getCurrentUserInfo().uid;
		console.log('invit_code', id.value);
		invit_code.value = id.value + "_building";
		getCode();
	});

	// 定义方法
	const copy = (code) => {
		console.log('copy', code);
		uni.setClipboardData({
			data: code,
			success: () => {
				console.log('复制成功', code);
				uni.showToast({
					title: '复制成功',
					icon: 'success',
					duration: 2000
				});
			},
			fail: () => {
				console.log('复制失败');
				// 可以添加错误处理或用户友好的提示
			}
		});
	};

	const add = () => {
		if (!input.value.length) {
			uni.showToast({
				title: '邀请码不能为空',
				icon: 'error'
			});
			return;
		}

		api.addInviteCode(id.value, input.value).then(res => {
			console.log("add", res);
			if (res.code) {
				uni.showToast({
					title: res.msg,
					icon: 'success'
				});
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'error'
				});
			}
		});
	};

	const del = (code) => {
		if (code === invit_code.value) {
			console.log("验证码是自己的");
			uni.showToast({
				title: '不能删除自己的验证码',
				icon: 'none'
			});
			return;
		}

		api.delInviteCode(id.value, code).then(res => {
			if (res.code === 1) {
				uni.showToast({
					title: '删除成功',
					icon: 'success',
					duration: 2000
				});
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none',
					duration: 2000
				});
			}
		});
	};

	const getCode = () => {
		api.getInviteCode(id.value).then(res => {
			console.log("getInviteCode", res);
			list.value = res.data;
		});
	};
</script>

<style lang="scss">
	.container {
		padding: 20rpx;

		.layout {
			display: flex;
			align-items: center;
			justify-content: space-between;
			text-align: center;

			text {
				width: 100vw;
			}

			button {
				width: 130rpx;
			}
		}

	}
</style>