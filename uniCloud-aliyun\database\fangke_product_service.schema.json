// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "增值服务商品表",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"create_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"update_time":{
			"bsonType": "timestamp",
			"forceDefaultValue":{
				"$env": "now"
			}
		},
		"product_id":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "商品ID(唯一)",
			"description": "商品ID"
		},
		"name":{
			"bsonType": "string",
			"label": "商品名称",
			"defaultValue":""
		},
		"during":{
			"bsonType": "int",
			"label": "月份",
			"defaultValue":0
		},
		"status":{
			"bsonType": "int",
			"defaultValue": 0,
			"label": "套餐状态",
			"description": "套餐状态:0.使用中 1.过期 2.待使用 3.已退款 4.已发货"
		},
		"start_time":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "开始时间",
			"description": "时间格式为YYYY-MM-DD"
		},
		"end_time":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "结束时间",
			"description": "时间格式为YYYY-MM-DD"
		},
		"price": {
			"bsonType": "int",
			"label": "价格（分）"
		},
		
		"num":{
			"bsonType": "int",
			"label": "库存数量"
		},
		"type":{
			"bsonType": "int",
			"label": "订单类型",
			"description": "订单类型（1.增加房间 2.电子合同 3.短信充值 4.群发短信 5.巡房管理 6.维修服务 7.保洁服务 8.智能门锁）",
			"defaultValue":0
		},
		"unit":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "单位"
		},
		"rule":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "使用规则"
		},
		"detail":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "详情"
		},
		"tip":{
			"bsonType": "string",
			"defaultValue": "",
			"label": "温馨提示"
		},
		"desc": {
			"bsonType": "string",
			"label": "商品描述",
			"description": "商品描述"
		}
		
	}
}