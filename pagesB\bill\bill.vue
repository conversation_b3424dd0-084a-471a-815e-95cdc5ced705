<template>
	<view class="page">
		
		<!-- 日期筛选区域 -->
		<view class="date-filter">
			<view class="date-filter-item" @click="showDateRangeSelector">
				<uni-icons type="calendar" size="16" color="#07c160" />
				<text class="date-text">账单开始日期：{{ dateRangeText }}</text>
				<uni-icons type="bottom" size="12" color="#07c160" />
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-section">
			<view class="filter-item" @click="showBillStatusFilter">
				<text class="filter-text">{{ selectedBillStatus }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showPaymentStatusFilter">
				<text class="filter-text">{{ selectedPaymentStatus }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
			<view class="filter-item" @click="showRoomFilter">
				<text class="filter-text">{{ selectedRoom }}</text>
				<uni-icons type="bottom" size="12" color="#666" />
			</view>
		</view>

		<!-- 统计数据区域 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="stat-value">{{ formatAmount(totalAmount) }}</text>
				<text class="stat-label">应收(元)</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ formatAmount(receivedAmount) }}</text>
				<text class="stat-label">应收已收(元)</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ formatAmount(pendingAmount) }}</text>
				<text class="stat-label">应收待收(元)</text>
			</view>
		</view>

		<!-- 账单列表 -->
		<view class="bill-list">
			<view class="bill-item" v-for="(bill, index) in filteredBills" :key="index" @click="viewBillDetail(bill)">
				<view class="bill-header">
					<view class="room-info">
						<text class="room-name">{{ bill.roomName }}</text>
						<view class="overdue-tag" v-if="bill.isOverdue">
							<text class="overdue-text">逾期{{ bill.overdueDays }}天</text>
						</view>
					</view>
					<view class="bill-amount">
						<text class="amount">{{ formatAmount(bill.amount) }}</text>
					</view>
				</view>
				<view class="bill-content">
					<view class="bill-info">
						<text class="bill-type">{{ bill.type }}</text>
						<text class="bill-period">{{ bill.period }}</text>
					</view>
					<view class="due-date">
						<text class="due-date-text">{{ bill.dueDate }}</text>
					</view>
				</view>
				<view class="bill-footer">
					<text class="due-label">应收款日</text>
					<text class="due-date-value">{{ bill.dueDate }}</text>
				</view>
			</view>
		</view>

		<!-- 底部催缴按钮 -->
		<view class="footer">
			<button class="reminder-btn" @click="sendReminder">
				一键催缴（本月剩余{{ remainingReminders }}次）
			</button>
		</view>

		<!-- DateRangeSelector 组件 -->
		<DateRangeSelector 
			ref="dateRangeSelector" 
			:startDate="startDate" 
			:endDate="endDate" 
			:customTimeTags="customTimeTags"
			@confirm="onDateRangeConfirm" 
			@cancel="onDateRangeCancel" 
		/>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom" background-color="#fff">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="closeFilterPopup">取消</text>
					<text class="popup-title">{{ filterTitle }}</text>
					<text class="confirm-btn" @click="confirmFilter">确定</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" 
						v-for="(option, index) in currentFilterOptions" 
						:key="index"
						:class="{ active: selectedFilterIndex === index }"
						@click="selectFilterOption(index)">
						<text class="option-text">{{ option.text }}</text>
						<uni-icons v-if="selectedFilterIndex === index" type="checkmarkempty" size="16" color="#07c160" />
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted ,getCurrentInstance} from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import DateRangeSelector from '../components/DateRangeSelector/DateRangeSelector.vue'
	import { dayjs } from '@/utils/dayjs.min.js'
	const db = uniCloud.databaseForJQL()
	
	// 日期相关
	const startDate = ref('')
	const endDate = ref('')
	const dateRangeSelector = ref(null)

	// 自定义时间标签
	const customTimeTags = ref([
		{ name: '本月', days: 0, type: 'current_month' },
		{ name: '上月', days: 0, type: 'last_month' },
		{ name: '近3个月', days: 90, type: 'past' },
		{ name: '近6个月', days: 180, type: 'past' }
	])

	// 日期范围显示文本
	let dateRangeText = computed(() => {
		if (startDate.value && endDate.value) {
			return `${startDate.value} ~ ${endDate.value}`
		}
		return '全部'
	})
	
	// 筛选相关
	const selectedBillStatus = ref('全部类型')
	const selectedPaymentStatus = ref('全部状态')
	const selectedRoom = ref('全部楼房')
	const building = ref([])
	const buildingList = ref([])
	const jumpType = ref(0) // 跳转类型：0.全部 1.本月待收 2.未来7天 3.逾期

	const filterPopup = ref(null)
	const filterTitle = ref('')
	const currentFilterOptions = ref([])
	const selectedFilterIndex = ref(0)
	const currentFilterType = ref('')
	
	// 筛选选项 - 基于订单类型
	const billStatusOptions = [
		{ text: '全部类型', value: 'all' },
		{ text: '押金', value: 1 },
		{ text: '账单', value: 2 },
		{ text: '退租', value: 3 },
		{ text: '换房', value: 4 },
		{ text: '维修', value: 5 },
		{ text: '保洁', value: 6 },
		{ text: '其他', value: 7 }
	]

	// 基于账单状态
	const paymentStatusOptions = [
		{ text: '全部状态', value: 'all' },
		{ text: '已支付', value: 0 },
		{ text: '待支付', value: 1 },
		{ text: '已逾期', value: 2 }
	]

	// 动态生成房源选项 - 使用building数组
	const roomOptions = computed(() => {
		const rooms = [{ text: '全部楼房', value: 'all' }]
		// 从building数组中获取楼房名称
		building.value.forEach(item => {
			rooms.push({ text: item.name, value: item.id })
		})
		return rooms
	})

	// 获取选中楼房的ID
	const selectedBuildingId = computed(() => {
		if (selectedRoom.value === '全部楼房') {
			return null // 全部楼房
		}
		const selectedOption = roomOptions.value.find(option => option.text === selectedRoom.value)
		return selectedOption?.value || null
	})

	// 账单数据存储
	const billsAll = ref([])	      // 全部订单
	const billsCurrentMonth = ref([]) // 本月待收订单
	const billsNext7Days = ref([])    // 未来7天订单
	const billsOverdue = ref([])      // 逾期订单

	// 统计数据 - 根据真实数据计算
	const totalAmount = computed(() => {
		const filtered = getFilteredBills()
		return filtered.reduce((sum, bill) => sum + bill.sum, 0)
	})
	
	const receivedAmount = computed(() => {
		const filtered = getFilteredBills()
		return filtered.reduce((sum, bill) => sum + bill.money, 0)
	})
	
	const pendingAmount = computed(() => {
		const filtered = getFilteredBills()
		return filtered.reduce((sum, bill) => sum + (bill.sum - bill.money), 0)
	})
	
	const remainingReminders = ref(3)

	// 当前显示的账单数据 - 根据跳转类型显示
	const bills = computed(() => {
		switch(jumpType.value) {
			case 1:
				return billsCurrentMonth.value
			case 2:
				return billsNext7Days.value
			case 3:
				return billsOverdue.value
			default:
				return billsAll.value
		}
	})

	// 获取筛选后的账单数据
	const getFilteredBills = () => {
		let filtered = bills.value

		// 根据订单类型筛选
		if (selectedBillStatus.value !== '全部类型') {
			const typeValue = billStatusOptions.find(option => option.text === selectedBillStatus.value)?.value
			if (typeValue !== 'all') {
				filtered = filtered.filter(bill => bill.type === typeValue)
			}
		}

		// 根据付款状态筛选
		if (selectedPaymentStatus.value !== '全部状态') {
			const statusValue = paymentStatusOptions.find(option => option.text === selectedPaymentStatus.value)?.value
			if (statusValue !== 'all') {
				filtered = filtered.filter(bill => bill.status === statusValue)
			}
		}

		// 根据楼房筛选
		if (selectedBuildingId.value) {
			filtered = filtered.filter(bill => bill.building_id === selectedBuildingId.value)
		}

		// 根据日期范围筛选
		if (startDate.value && endDate.value) {
			const start = dayjs(startDate.value)
			const end = dayjs(endDate.value).add(1,"day")
			filtered = filtered.filter(bill => {
				const billDate = dayjs(bill.day)
				return billDate.isBetween(start, end, "day",'[]')
			})
		}

		return filtered
	}

	// 筛选后的账单列表
	const filteredBills = computed(() => {
		return getFilteredBills().map(bill => ({
			id: bill._id,
			roomName: bill.room_name,
			type: getTypeText(bill.type, bill.name),
			period: getPeriod(bill),
			amount: bill.sum, // 应收金额
			dueDate: bill.day,
			isOverdue: bill.status === 2,
			overdueDays: bill.dueDay || 0,
			status: getPaymentStatus(bill.status),
			rawData: bill
		}))
	})

	// 获取类型文本
	const getTypeText = (type, name) => {
		const typeMap = {
			1: '押金',
			2: '账单',
			3: '退租',
			4: '换房',
			5: '维修',
			6: '保洁',
			7: '其他'
		}
		return name || typeMap[type] || '其他'
	}

	// 获取账单期间
	const getPeriod = (bill) => {
		if (bill.startTime && bill.endTime) {
			return `${bill.startTime} ~ ${bill.endTime}`
		}
		return bill.day || ''
	}

	// 获取付款状态
	const getPaymentStatus = (status) => {
		const statusMap = {
			0: 'received',  // 已支付
			1: 'pending',   // 待支付
			2: 'pending'    // 已逾期（也算待收款）
		}
		return statusMap[status] || 'pending'
	}

	onMounted(() => {
		// 初始化当前月份
		const now = dayjs()
		startDate.value = now.startOf('month').format('YYYY-MM-DD')
		endDate.value = now.endOf('month').format('YYYY-MM-DD')
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		eventChannel.on('acceptDataFromBill', function(data) {
				console.log('acceptDataFromBill', data)
				building.value = data
				data.forEach(item =>{
					buildingList.value.push(item.id)
				})
				getBill()
			})
	})

	const getBill = async() => {
		console.log("getBill - jumpType:", jumpType.value)
		try {
			const res = await db.collection("fangke_room_bill").where(
				`building_id in ['${buildingList.value.join("','")}']`
			).get()
			
			console.log("获取所有订单", res.data)
			billsAll.value = res.data
			
			// 清空分类数组
			billsCurrentMonth.value = []
			billsNext7Days.value = []
			billsOverdue.value = []
			
			const now = dayjs()
			const currentMonthStart = now.startOf('month')
			const currentMonthEnd = now.endOf('month')
			const next7Days = now.add(7, 'day')
			
			console.log("当前时间:", now.format('YYYY-MM-DD'))
			console.log("本月开始:", currentMonthStart.format('YYYY-MM-DD'))
			console.log("本月结束:", currentMonthEnd.format('YYYY-MM-DD'))
			
			// 先处理逾期状态更新
			res.data.forEach(item => {
				const billDate = dayjs(item.day)
				// 计算逾期天数 - 只要是待支付状态且到期日已过就是逾期
				if (item.status === 1 && billDate.isBefore(now, 'day')) {
					item.dueDay = now.diff(billDate, 'day')
					item.status = 2 // 更新为逾期状态
					console.log(`订单 ${item.name} 逾期 ${item.dueDay} 天，状态更新为逾期`)
				}
			})
			
			// 然后进行分类
			res.data.forEach(item => {
				const billDate = dayjs(item.day)
				console.log(`分类订单: ${item.name}, 到期日: ${item.day}, 状态: ${item.status}, 金额: ${item.sum}`)
				
				// 逾期订单（status=2的所有订单）
				if (item.status === 2) {
					billsOverdue.value.push(item)
					console.log(`${item.name} -> 逾期订单`)
				}
				
				// 未来7天订单（7天内到期的待支付订单）
				if (billDate.isBetween(now, next7Days, null, '[]') && item.status === 1) {
					billsNext7Days.value.push(item)
					console.log(`${item.name} -> 未来7天`)
				}
				
				// 本月待收逻辑：
				// 1. 本月内到期的待支付订单 (status=1 且 到期日在本月)
				// 2. 所有逾期订单 (status=2，不论到期日)
				const isCurrentMonthDue = billDate.isBetween(currentMonthStart, currentMonthEnd, null, '[]')
				const isPending = item.status === 1
				const isOverdue = item.status === 2
				
				if ((isCurrentMonthDue && isPending) || isOverdue) {
					billsCurrentMonth.value.push(item)
					console.log(`${item.name} -> 本月待收 (原因: ${isOverdue ? '逾期' : '本月到期待支付'})`)
				}
			})
			
			console.log("=== 最终分类结果 ===")
			console.log("全部订单:", billsAll.value.length)
			console.log("本月待收:", billsCurrentMonth.value.length)
			console.log("未来7天:", billsNext7Days.value.length) 
			console.log("逾期订单:", billsOverdue.value.length)
			
			console.log("本月待收详情:", billsCurrentMonth.value.map(item => ({
				name: item.name,
				day: item.day,
				status: item.status === 1 ? '待支付' : item.status === 2 ? '逾期' : '其他',
				amount: item.sum,
				room: item.room_name
			})))
			
		} catch (error) {
			console.error("获取账单数据失败:", error)
			uni.showToast({
				title: '获取数据失败',
				icon: 'none'
			})
		}
	}

	onLoad((e) => {
		// 页面加载时的初始化
		if(e.uid && e.type !== undefined) {
			jumpType.value = parseInt(e.type) || 0
			console.log("页面加载 - jumpType:", jumpType.value)
			
			// 根据类型设置默认筛选条件
			switch(jumpType.value) {
				case 1: // 本月待收
					selectedPaymentStatus.value = '待支付'
					break
				case 2: // 未来7天
					startDate.value = dayjs().format('YYYY-MM-DD')
					endDate.value = dayjs().add(7,"day").format('YYYY-MM-DD')
					dateRangeText = `${startDate.value} ~ ${endDate.value}`
					console.log("dateRangeText",dateRangeText);
					selectedPaymentStatus.value = '待支付'
					break
				case 3: // 逾期订单
					selectedPaymentStatus.value = '已逾期'
					break
				default: // 全部订单
					selectedBillStatus.value = '全部类型'
					selectedPaymentStatus.value = '全部状态'
					selectedRoom.value = '全部楼房'
					break
			}
		}
	})

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 显示日期范围选择器
	const showDateRangeSelector = () => {
		dateRangeSelector.value?.open()
	}

	// 日期范围选择确认回调
	const onDateRangeConfirm = (result) => {
		startDate.value = result.startDate
		endDate.value = result.endDate
		console.log('选择的日期范围:', result)
	}

	// 日期范围选择取消回调
	const onDateRangeCancel = () => {
		console.log('取消选择日期范围')
	}

	// 显示账单状态筛选
	const showBillStatusFilter = () => {
		filterTitle.value = '订单类型'
		currentFilterOptions.value = billStatusOptions
		currentFilterType.value = 'billStatus'
		selectedFilterIndex.value = billStatusOptions.findIndex(option => option.text === selectedBillStatus.value)
		filterPopup.value?.open()
	}

	// 显示付款状态筛选
	const showPaymentStatusFilter = () => {
		filterTitle.value = '账单状态'
		currentFilterOptions.value = paymentStatusOptions
		currentFilterType.value = 'paymentStatus'
		selectedFilterIndex.value = paymentStatusOptions.findIndex(option => option.text === selectedPaymentStatus.value)
		filterPopup.value?.open()
	}

	// 显示房源筛选
	const showRoomFilter = () => {
		filterTitle.value = '楼房筛选'
		currentFilterOptions.value = roomOptions.value
		currentFilterType.value = 'room'
		selectedFilterIndex.value = roomOptions.value.findIndex(option => option.text === selectedRoom.value)
		filterPopup.value?.open()
	}

	// 选择筛选选项
	const selectFilterOption = (index) => {
		selectedFilterIndex.value = index
	}

	// 确认筛选
	const confirmFilter = () => {
		const selectedOption = currentFilterOptions.value[selectedFilterIndex.value]
		
		switch (currentFilterType.value) {
			case 'billStatus':
				selectedBillStatus.value = selectedOption.text
				break
			case 'paymentStatus':
				selectedPaymentStatus.value = selectedOption.text
				break
			case 'room':
				selectedRoom.value = selectedOption.text
				break
		}
		
		closeFilterPopup()
	}

	// 关闭筛选弹窗
	const closeFilterPopup = () => {
		filterPopup.value?.close()
	}

	// 格式化金额显示
	const formatAmount = (amount) => {
		return (amount / 100).toFixed(2)
	}

	// 查看账单详情
	const viewBillDetail = (bill) => {
		console.log('查看账单详情:', bill)
		// 这里可以跳转到账单详情页面
	}

	// 发送催缴
	const sendReminder = () => {
		if (remainingReminders.value > 0) {
			uni.showModal({
				title: '确认催缴',
				content: `确定要发送催缴通知吗？本月还剩余${remainingReminders.value}次机会。`,
				success: (res) => {
					if (res.confirm) {
						remainingReminders.value--
						uni.showToast({
							title: '催缴通知已发送',
							icon: 'success'
						})
					}
				}
			})
		} else {
			uni.showToast({
				title: '本月催缴次数已用完',
				icon: 'none'
			})
		}
	}
</script>

<style scoped>
	.page {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}

	/* 日期筛选区域 */
	.date-filter {
		background: #e8f5e8;
		padding: 20rpx 30rpx;
		flex-shrink: 0;
	}

	.date-filter-item {
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.date-text {
		font-size: 28rpx;
		color: #07c160;
		flex: 1;
	}

	/* 筛选区域 */
	.filter-section {
		display: flex;
		background: #fff;
		padding: 20rpx 30rpx;
		gap: 40rpx;
		border-bottom: 1px solid #f0f0f0;
		flex-shrink: 0;
	}

	.filter-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
		flex: 1;
	}

	.filter-text {
		font-size: 28rpx;
		color: #333;
	}

	/* 统计数据区域 */
	.stats-section {
		display: flex;
		background: #fff;
		padding: 40rpx 30rpx;
		margin-bottom: 20rpx;
		flex-shrink: 0;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
	}

	.stat-value {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
	}

	.stat-item:nth-child(2) .stat-value {
		color: #07c160;
	}

	.stat-item:nth-child(3) .stat-value {
		color: #ff6b6b;
	}

	.stat-label {
		font-size: 24rpx;
		color: #999;
	}

	/* 账单列表 */
	.bill-list {
		flex: 1;
		overflow-y: auto;
		padding: 0 30rpx 140rpx 30rpx;
	}

	.bill-item {
		background: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.bill-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 20rpx;
	}

	.room-info {
		display: flex;
		align-items: center;
		gap: 10rpx;
		flex: 1;
	}

	.room-name {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.overdue-tag {
		background: #fff2f0;
		border: 1px solid #ffccc7;
		border-radius: 8rpx;
		padding: 4rpx 8rpx;
	}

	.overdue-text {
		font-size: 20rpx;
		color: #ff4d4f;
	}

	.bill-amount {
		text-align: right;
	}

	.amount {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}

	.bill-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.bill-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.bill-type {
		font-size: 26rpx;
		color: #333;
	}

	.bill-period {
		font-size: 24rpx;
		color: #999;
	}

	.due-date {
		text-align: right;
	}

	.due-date-text {
		font-size: 24rpx;
		color: #999;
	}

	.bill-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 20rpx;
		border-top: 1px solid #f0f0f0;
	}

	.due-label {
		font-size: 24rpx;
		color: #999;
	}

	.due-date-value {
		font-size: 24rpx;
		color: #999;
	}

	/* 底部催缴按钮 */
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 30rpx;
		background: #fff;
		border-top: 1px solid #f0f0f0;
		z-index: 10;
	}

	.reminder-btn {
		width: 100%;
		height: 80rpx;
		background: #07c160;
		color: #fff;
		border: none;
		border-radius: 8rpx;
		font-size: 32rpx;
		font-weight: 500;
	}

	/* 筛选弹窗样式 */
	.filter-popup {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 60vh;
		overflow: hidden;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #EBEDF0;
	}

	.cancel-btn {
		color: #666;
		font-size: 28rpx;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.confirm-btn {
		color: #07c160;
		font-weight: 500;
		font-size: 28rpx;
	}

	.filter-options {
		padding: 20rpx 0;
		max-height: 400rpx;
		overflow-y: auto;
	}

	.filter-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.filter-option:last-child {
		border-bottom: none;
	}

	.filter-option.active {
		background: #f0f9ff;
	}

	.option-text {
		font-size: 28rpx;
		color: #333;
	}

	.filter-option.active .option-text {
		color: #07c160;
	}
</style>
