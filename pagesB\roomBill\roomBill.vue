<template>
	<view class="container">
		<view class="title">账单</view>
		<view v-for="(item,index) in roomList" :key="index" class="contain">
			<billList :building_list="item" :sum="sum" :isRoom="true"></billList>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import billList from '../../pagesB/components/billList/billList.vue'

	const api = uniCloud.importObject("ApiFuntion")

	// 定义响应式数据
	const building_id = ref("")
	const month = ref("")
	const year = ref("")
	const sum = ref(0)
	const roomList = ref([])

	// 生命周期钩子
	onMounted((e) => {
		console.log("roomBill", e)
		building_id.value = e.building_id
		month.value = e.month
		year.value = e.year
		sum.value = Number(e.sum)

		uni.setNavigationBarTitle({
			title: e.name || ""
		})

		getRoomBill()
	})

	// 获取房间账单
	const getRoomBill = () => {
		api.getRoomBill(building_id.value, year.value, month.value).then(res => {
			console.log("getRoomBill", res)
			roomList.value.push(res.data)
		})
	}
</script>

<style lang="scss">
	.container {
		box-sizing: border-box;
		background-color: #efefef;
		height: 100vh;

		.title {
			display: flex;
			justify-content: center;
			padding: 20rpx;
		}

	}
</style>