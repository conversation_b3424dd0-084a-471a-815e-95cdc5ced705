<template>
	<view class="info">
		<view class="row">
			<view class="item">
				<uni-easyinput type="digit" v-model="last_ele" :clearable="false" @change="getData(info)"
					@input="getLastEle" :errorMessage="isWorring1"></uni-easyinput>
			</view>
			<view class="item">
				<uni-easyinput type="digit" v-model="current_ele" :clearable="false" @change="getData(info)"
					@input="getEle" :errorMessage="isWorring2"></uni-easyinput>
			</view>
		</view>
		<view class="row">
			<view class="item">
				<uni-easyinput type="digit" v-model="last_water" :clearable="false" @change="getData(info)"
					@input="getLastWater" :errorMessage="isWorring3"></uni-easyinput>
			</view>
			<view class="item">
				<uni-easyinput type="digit" v-model="current_water" :clearable="false" @change="getData(info)"
					@input="getWater" :errorMessage="isWorring4"></uni-easyinput>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		watch
	} from 'vue';

	const props = defineProps({
		info: {
			type: Object,
			required: true
		},
		type: { // 1 代表是上个月的输入框  2代表这个月的输入框
			type: Number,
			default: 0
		},
		lastNum: {
			type: Number,
			default: 0
		},
		currentNum: {
			type: Number,
			default: 0
		},
		inputType: { // 1代表电表  2代表水表
			type: Number,
			default: 0
		},
		index: {
			type: Number,
			default: 0
		},
		id: {
			type: String,
			default: ""
		}
	});

	const emit = defineEmits(['input']);

	// 响应式数据
	const last_ele = ref(props.info.electricity / 100 || 0);
	const current_ele = ref(props.info.current_ele / 100 || 0);
	const last_water = ref(props.info.water / 100 || 0);
	const current_water = ref(props.info.current_water / 100 || 0);
	const isWorring1 = ref(false);
	const isWorring2 = ref(false);
	const isWorring3 = ref(false);
	const isWorring4 = ref(false);

	// 输入验证方法
	const getLastEle = (e) => {
		console.log("input", last_ele.value);
		if (last_ele.value > current_ele.value && current_ele.value > 0 || e === "") {
			isWorring1.value = true;
		} else {
			isWorring1.value = false;
		}
	};

	const getEle = (e) => {
		console.log("input", current_ele.value);
		if (last_ele.value > current_ele.value || e === "") {
			isWorring2.value = true;
		} else {
			isWorring2.value = false;
		}
	};

	const getLastWater = (e) => {
		console.log("input", last_water.value);
		if (last_water.value > current_water.value && current_water.value > 0 || e === "") {
			isWorring3.value = true;
		} else {
			isWorring3.value = false;
		}
	};

	const getWater = (e) => {
		console.log("input", current_water.value);
		if (last_water.value > current_water.value || e === "") {
			isWorring4.value = true;
		} else {
			isWorring4.value = false;
		}
	};

	// 获取并验证数据
	const getData = (info) => {
		console.log("last_ele", last_ele.value);
		console.log("current_ele", current_ele.value);
		console.log("last_water", last_water.value);
		console.log("current_water", current_water.value);

		if (last_ele.value === "" || current_ele.value === "" || last_water.value === "" || current_water.value ===
			"") {
			uni.showToast({
				title: "水电表数值不能为空",
				icon: 'none'
			});
			return;
		}

		if (last_ele.value > current_ele.value) {
			uni.showToast({
				title: "上个月的电表数值不能大于最近读数",
				icon: 'none'
			});
			return;
		}

		if (last_water.value > current_water.value) {
			uni.showToast({
				title: "上个月的水表数值不能大于最近读数",
				icon: 'none'
			});
			return;
		}

		info.electricity = Number(last_ele.value) * 100;
		info.current_ele = Number(current_ele.value) * 100;
		info.water = Number(last_water.value) * 100;
		info.current_water = Number(current_water.value) * 100;

		emit("input", [props.index, info]);
	};

	// 设置警告状态
	const setWorring = () => {
		isWorring1.value = true;
	};

	// 监听props变化，更新本地数据
	watch(() => props.info, (newInfo) => {
		last_ele.value = newInfo.electricity / 100 || 0;
		current_ele.value = newInfo.current_ele / 100 || 0;
		last_water.value = newInfo.water / 100 || 0;
		current_water.value = newInfo.current_water / 100 || 0;
	}, {
		deep: true
	});
</script>

<style lang="scss" scoped>
	.info {
		display: flex;
		flex-direction: column;

		.row {
			display: flex;

			.item {
				margin: 5rpx;
			}
		}
	}
</style>