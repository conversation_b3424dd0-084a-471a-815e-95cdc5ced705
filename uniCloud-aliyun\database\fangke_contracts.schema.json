// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "租赁合同表",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": "auth.uid != null",
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"room_id": {
			"bsonType": "string",
			"description": "房间ID",
			"foreignKey": "fangke_room._id"
		},
		"create_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"tenant_id": {
			"bsonType": "string",
			"label": "租客ID",
			"foreignKey": "fangke_tenants._id"
		},
		"start_time": {
			"bsonType": "string",
			"label": "入住时间"
		},
		"end_time": {
			"bsonType": "string",
			"label": "到期时间"
		},
		"deposit": {
			"bsonType": "array",
			"label": "押金列表",
			"arrayType": "object",
			"description": ""
		},
		"payment": {
			"bsonType": "string",
			"label": "付款方式"
		},
		"rent": {
			"bsonType": "int",
			"label": "租金"
		},
		"contract_file": {
			"bsonType": "file",
			"label": "合同文件"
		},
		"status": {
			"bsonType": "int",
			"description": "合同状态 0.已退房 1.签约成功 2.已过期",
			"label": "合同状态",
			"defaultValue":1,
			"enum": [{
					"text": "已退房",
					"value": 0
				},
				{
					"text": "待租客确定",
					"value": 1
				},
				{
					"text": "签约成功",
					"value": 2
				},
				{
					"text": "续租待确认",
					"value": 3
				},
				{
					"text": "即将到期",
					"value": 4
				},
				{
					"text": "已过期",
					"value": 5
				}
			]
		},
		"desc": {
			"bsonType": "string",
			"label": "备注",
			"defaultValue":""
		},
		"late_fee":{
			"bsonType": "int",
			"label": "滞纳金的类型",
			"description": "逾期后根据滞纳金类型计算租金，类型有：0.没有滞纳金，1.每天固定，2.每天百分比,3.单次固定,4.单次百分比",
			"defaultValue":0
		},
		"late_fee_num":{
			"bsonType": "int",
			"label": "滞纳金/百分比",
			"defaultValue":0
		},
		"day":{
			"bsonType": "int",
			"label": "收租日",
			"description": "收租日",
			"defaultValue":1
		},
		"ext_fee":{
			"bsonType": "array",
			"arrayType": "object",
			"label": "其他月费用",
			"description": "其他每月固定费用"
		},
		"ele_price":{
			"bsonType": "int",
			"label": "电费价格(分)",
			"defaultValue":0
		},
		"water_price":{
			"bsonType": "int",
			"label": "水费价格(分)"
			
		},
		"sign_time":{
			"bsonType": "timestamp",
			"label": "签约时间",
			"defaultValue":{
				"$env": "now"
			}
		},
		"uid":{
			"bsonType": "string",
			"label": "房东ID",
			"foreignKey": "uni-id-users_id"
		},
		"handler":{
			"bsonType": "string",
			"label": "经办人",
			"defaultValue":""
		},
		"building_id":{
			"bsonType": "string",
			"label": "楼房ID",
			"foreignKey": "fangke_building._id"
		},
		"goods_list":{
			"bsonType": "array",
			"arrayType": "object",
			"label": "物品列表"
		},
		"isPaper":{
			"bsonType": "bool",
			"defaultValue":true,
			"label": "是否纸质版合同"
		},
		"number":{
			"bsonType": "string",
			"label": "合同编号",
			"defaultValue":""
		},
		"current_ele":{
			"bsonType": "int",
			"defaultValue":0,
			"label": "当前电表数"
		},
		"current_water":{
			"bsonType": "int",
			"defaultValue":0,
			"label": "当前水表数"
		},
		"room_name":{
			"bsonType": "string",
			"label": "房间名",
			"defaultValue":""
		},
		"address":{
			"bsonType": "string",
			"label": "地址",
			"defaultValue":""
		},
		"room_mate":{
			"bsonType": "array",
			"arrayType": "string",
			"label": "同住人列表"
		},
		"checkout":{
			"bsonType": "timestamp",
			"label": "退租时间"
		}
		
	}
}