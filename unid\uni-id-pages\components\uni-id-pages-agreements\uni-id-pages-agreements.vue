<template>
	<view class="root" v-if="agreements.length">
		<template v-if="needAgreements">
			<checkbox-group @change="setAgree">
				<label class="checkbox-box">
					<checkbox :checked="isAgree" style="transform: scale(0.5);margin-right: -6px;" />
					<text class="text">同意</text>
				</label>
			</checkbox-group>
			<view class="content">
				<view class="item" v-for="(agreement,index) in agreements" :key="index">
					<text class="agreement text" @click="navigateTo(agreement)">{{agreement.title}}</text>
					<text class="text and" v-if="hasAnd(agreements,index)" space="nbsp"> 和 </text>
				</view>
			</view>
		</template>
		<!-- 弹出式 -->
		<uni-popup v-if="needAgreements||needPopupAgreements" ref="popupAgreement" type="center">
			<uni-popup-dialog confirmText="同意" @confirm="popupConfirm">
				<view class="content">
					<text class="text">请先阅读并同意</text>
					<view class="item" v-for="(agreement,index) in agreements" :key="index">
						<text class="agreement text" @click="navigateTo(agreement)">{{agreement.title}}</text>
						<text class="text and" v-if="hasAnd(agreements,index)" space="nbsp"> 和 </text>
					</view>
				</view>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		defineProps,
		defineEmits,
		nextTick,
	} from 'vue'
	import config from '@/unid/uni-id-pages/config.js'
	const popupAgreement = ref(null)
	// 定义 props
	const props = defineProps({
		scope: {
			type: String,
			default: 'register'
		}
	})

	// 定义 emits
	const emit = defineEmits(['setAgree'])

	// 响应式数据
	const isAgree = ref(false)
	const needAgreements = ref(true)
	const needPopupAgreements = ref(false)
	let retryFun = () => console.log('未定义')

	// 计算属性
	const agreements = computed(() => {
		if (!config.agreements) {
			return []
		}
		let {
			serviceUrl,
			privacyUrl
		} = config.agreements
		return [{
				url: serviceUrl,
				title: "用户服务协议"
			},
			{
				url: privacyUrl,
				title: "隐私政策条款"
			}
		]
	})

	// 生命周期钩子
	onMounted(() => {
		needAgreements.value = (config?.agreements?.scope || []).includes(props.scope)
	})

	// 方法
	const popupConfirm = () => {
		isAgree.value = true
		retryFun()
		// emit('popupConfirm')
	}

	const popup = (Fun) => {
		needPopupAgreements.value = true
		// needAgreements.value = true
		nextTick(() => {
			if (Fun) {
			      retryFun = Fun
			    }
			popupAgreement.value.open()
		})
	}

	const navigateTo = ({
		url,
		title
	}) => {
		uni.navigateTo({
			url: '/unid/uni-id-pages/pages/common/webview/webview?url=' + url + '&title=' + title,
			success: res => {},
			fail: () => {},
			complete: () => {}
		})
	}

	const hasAnd = (agreements, index) => {
		return agreements.length - 1 > index
	}

	const setAgree = (e) => {
		isAgree.value = !isAgree.value
		emit('setAgree', isAgree.value)
	}
	
	defineExpose({
		popup,
		isAgree
	})
</script>

<style lang="scss" scoped>
	/* #ifndef APP-NVUE */
	view {
		display: flex;
		box-sizing: border-box;
		flex-direction: column;
	}

	/* #endif */
	.root {
		flex-direction: row;
		align-items: center;
		font-size: 12px;
		color: #8a8f8b;
	}

	.checkbox-box,
	.uni-label-pointer {
		align-items: center;
		display: flex;
		flex-direction: row;
	}

	.item {
		flex-direction: row;
	}

	.text {
		line-height: 26px;
	}

	.agreement {
		color: #04498c;
		cursor: pointer;
	}

	.checkbox-box ::v-deep .uni-checkbox-input {
		border-radius: 100%;
	}

	.checkbox-box ::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
		border-color: $uni-color-primary;
		color: #FFFFFF !important;
		background-color: $uni-color-primary;
	}

	.content {
		flex-wrap: wrap;
		flex-direction: row;
	}

	.root ::v-deep .uni-popup__error {
		color: #333333;
	}
</style>