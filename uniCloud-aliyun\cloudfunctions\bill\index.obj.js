const {
	result
} = require('result');
let dbJQL;
const dayjs = require('dayjs')
var utc = require('dayjs/plugin/utc')
const customParseFormat = require('dayjs/plugin/customParseFormat');
var timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)
dayjs.tz.setDefault("Asia/Shanghai")
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function () { // 通用预处理器
		// this.params = this.getParams()[0]
		this.startTime = Date.now();
		this.params = this.getHttpInfo()
		console.log("httpInfo", this.params);
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		let info = this.getClientInfo()
		console.log("this.getClientInfo", info);
		if (this.params != undefined) {
			if (this.params.httpMethod == "POST") {
				//post请求
				// queryStringParameters: { uid: 'ddaadw' },	//参数在form-data时
				let body = this.getHttpInfo().body;
				if (!body) throw result(400, "required");
				this.params = JSON.parse(this.getHttpInfo().body)
			} else {
				//get请求
				this.params = this.getParams()[0]
			}
		}

	},

	async get() {
		console.log("get", this.params);

		return null
	},


	async add() {
		console.log("add", this.params);
		let {
			uid,
			data,
			bill_id
		} = this.params
		dbJQL.setUser({ //设置用户状态
			uid: uid
		})
		let bill = {}
		await dbJQL.collection("fangke_room_bill").doc(bill_id).get().then(res => {
			console.log("获取订单", res);
			if (res.errCode == 0) {
				bill = res.data[0]
			}
		})
		bill.detail.push(data)
		bill.sum = bill.sum + data.value
		console.log("订单", bill);
		let isSuccess = await dbJQL.collection("fangke_room_bill").doc(bill_id).update({
			detail: bill.detail,
			status: 1,
			sum: bill.sum
		}).then(res => {
			console.log("更新订单成功", res);
			if (res.errCode == 0) {
				return true
			} else {
				return false
			}
		})
		if (isSuccess) {
			return result(0, "success", "添加成功")
		} else {
			return result(1001, "fail", "添加失败")
		}

	},

	/**
	 * 处理水电记录生成账单
	 * @param {Object} params 参数
	 * @param {String} params.uid 用户ID
	 * @param {Array} params.utility_records 水电记录数组
	 */
	async processUtilityBills(data) {
		console.log("processUtilityBills", data);
		let { uid, utility_records } = data;

		if (!uid || !utility_records || !Array.isArray(utility_records)) {
			return result(400, "fail", "参数错误");
		}

		dbJQL.setUser({ uid: uid });

		try {
			// 检查房间租赁状态并分类处理
			const rentedRecords = [];
			const unrentedRecords = [];

			// 获取所有涉及的房间ID
			const roomIds = [...new Set(utility_records.map(record => record.room_id))];

			// 批量查询房间租赁状态
			const roomsResult = await dbJQL.collection("fangke_room")
				.where(`_id in ["${roomIds.join('","')}"]`)
				.field("_id,rent_status,tenant_id,contract,day")
				.get();

			const roomStatusMap = new Map();
			const roomInfoMap = new Map();
			if (roomsResult.data) {
				roomsResult.data.forEach(room => {
					// rent_status: 0 未出租 1.预约看房 2.预约过期 3.已出租 4.租约过期 5.租约快到期
					// 只有租赁状态为3、5且有租客ID才需要生成账单
					const isRented = (room.rent_status === 3 || room.rent_status === 5) && room.tenant_id;
					roomStatusMap.set(room._id, isRented);
					roomInfoMap.set(room._id, room);
				});
			}

			// 根据房间状态分类记录
			for (const record of utility_records) {
				const isRented = roomStatusMap.get(record.room_id) || false;

				if (isRented) {
					rentedRecords.push(record);
				} else {
					unrentedRecords.push(record);
				}
			}

			console.log(`房间状态分类: 已出租房间记录 ${rentedRecords.length} 条, 未出租房间记录 ${unrentedRecords.length} 条`);

			const processResults = [];
			let processedBills = 0;

			// 处理已出租房间的记录（生成账单）
			if (rentedRecords.length > 0) {
				// 按房间和月份分组
				const groupedRecords = new Map();
				for (const record of rentedRecords) {
					const key = `${record.room_id}_${record.date}_${record.type}`; // room_id + 月份
					console.log("key",key);
					if (!groupedRecords.has(key)) {
						groupedRecords.set(key, {
							room_id: record.room_id,
							room_name: record.room_name,
							building_id: record.building_id,
							building_name: record.building_name,
							date: record.date, // YYYY-MM 格式
							type: record.type,
							records: []
						});
					}
					groupedRecords.get(key).records.push(record);
				}

				// 处理每个分组
				for (const group of groupedRecords.values()) {
					const { room_id, room_name, building_id, date, records ,type} = group;
					console.log("获取记录列表",group);
					console.log("key2=",room_id+'_'+date+'_'+type);
					const roomInfo = roomInfoMap.get(room_id);
					console.log("roomInfo",roomInfo);
					try {
						// 1. 查找或创建当月账单
						const billDate = dayjs(`${date}-'${roomInfo?.day || 1}'`).format('YYYY-MM-DD'); // 转换为 YYYY-MM-01 格式
						const monthEndDate = dayjs(billDate).add(1,'month').format('YYYY-MM-DD');
						let yearMonth = dayjs.tz().format("YYYY-MM")
						const escapedYearMonth = yearMonth.replace(/-/g, '\\-'); // 转义输入中的特殊字符（如 -）
						// 先查找是否已存在当月账单
						const existingBill = await dbJQL.collection("fangke_room_bill")
							.where(`room_id == "${room_id}" && ${new RegExp(`^${escapedYearMonth}-\\d{2}$`)}.test(day) && type == 2`)
							.get();

						let bill;
						if (existingBill.data && existingBill.data.length > 0) {
							bill = existingBill.data[0];
						} else {
							// 创建新账单
							const newBill = {
								name: `${dayjs(billDate).format('YYYY年MM月')} 月度账单`,
								room_id: room_id,
								room_name: room_name,
								building_id: building_id,
								day: monthEndDate,
								startTime: billDate,
								endTime: monthEndDate,
								money: 0, // 已收金额
								sum: 0,   // 应收金额
								arrears: 0, // 拖欠金额
								detail: [], // 费用列表
								status: 1,  // 待支付
								type: 2,    // 账单类型
								tenant: roomInfo?.tenant_id || "",
								dueDay: 0,
								account: [],
								contract: roomInfo?.contract || "",
							};

							const createResult = await dbJQL.collection("fangke_room_bill").add(newBill);
							bill = {
								_id: createResult.id,
								...newBill
							};
						}

						// 2. 获取房间价格信息
						let priceInfo = {
							water_price: 500, // 默认5元/吨
							ele_price: 150    // 默认1.5元/度
						};

						if (roomInfo && roomInfo.contract) {
							try {
								console.log("房间合同id",roomInfo.contract);
								const contractResult = await dbJQL.collection("fangke_contracts")
									.doc(roomInfo.contract)
									.field("ele_price,water_price")
									.get().then(res =>{
										console.log("获取房间合同：",res);
										return res
									});

								if (contractResult.data && contractResult.data.length > 0) {
									const contract = contractResult.data[0];
									priceInfo = {
										water_price: contract.water_price || 300,
										ele_price: contract.ele_price || 100
									};
									console.log("priceINFO ==",priceInfo);
								}
							} catch (error) {
								console.error("获取合同价格信息失败:", error);
							}
						}

						// 3. 生成水电费用明细
						const utilityFees = [];

						for (const record of records) {
							// 获取设备名称
							const deviceMap = {
								1: '冷水',
								2: '电',
								3: '燃气'
							};
							const deviceName = deviceMap[record.type] || '未知设备';

							// 获取设备单位
							const unitMap = {
								1: '元/吨',
								2: '元/度',
								3: '元/立方米'
							};
							const deviceUnit = unitMap[record.type] || '单位';

							const usage = record.usage / 100; // 转换为实际用量

							// 根据设备类型获取单价
							let unitPrice = 0;
							switch (record.type) {
								case 1: // 水表
									unitPrice = priceInfo.water_price; // 分
									break;
								case 2: // 电表
									unitPrice = priceInfo.ele_price; // 分
									break;
								case 3: // 燃气
									unitPrice = 3.0; // 默认3元/立方米
									break;
								default:
									unitPrice = 1.0;
							}
							console.log("单价",unitPrice);
							const amount = Math.round(usage * unitPrice); // 计算金额（分）
							
							utilityFees.push({
								text: `${deviceName}费`,
								value: amount, // 金额（分）
								status: 1, // 待支付
								overday: 0,
								day: monthEndDate,
								is_month: false,
								desc: `用量: ${usage.toFixed(2)} ${deviceUnit}, 单价: ${Math.round(unitPrice/100).toFixed(2)}元(${billDate + " - " + monthEndDate})`,
								unit: Math.round(unitPrice), // 单价（分）
								unit_name:deviceUnit,
								late_fee: 0,
								late_type: 0,
								late_num: 0,
								usage_amount: usage, // 用量
								create_time: dayjs.tz().valueOf() // 创建时间
							});
						}

						// 4. 更新账单费用列表
						let updateResult = null;
						if (utilityFees.length > 0) {
								console.log("utilityFees 数据：",utilityFees);
								// 更新费用列表和总金额
								const updatedDetail = [...bill.detail, ...utilityFees];
								const additionalAmount = utilityFees.reduce((sum, fee) => sum + fee.value, 0);
								const newSum = bill.sum + additionalAmount;
								const newArrears = newSum - bill.money;

								console.log(`更新账单 ${bill._id}：添加 ${utilityFees.length} 项水电费用，金额 ${additionalAmount} 分`);

								await dbJQL.collection("fangke_room_bill").doc(bill._id).update({
									detail: updatedDetail,
									sum: newSum,
									arrears: newArrears,
									status: newSum > bill.money ? 1 : 0, // 如果有未付金额则为待支付
									update_time: dayjs.tz().valueOf()
								}).then(res =>{
									console.log("更新账单列表",res);
								});

								updateResult = {
									added_fees: utilityFees.length,
									added_amount: additionalAmount,
									new_total: newSum
								};
						}

						// 记录处理结果
						processResults.push({
							bill_id: bill._id,
							room_name: room_name,
							date: date,
							utility_records: records.length,
							generated_fees: utilityFees.length,
							added_fees: updateResult ? updateResult.added_fees : 0,
							added_amount: updateResult ? updateResult.added_amount : 0,
							total_amount: utilityFees.reduce((sum, fee) => sum + fee.value, 0)
						});

						processedBills++;

					} catch (error) {
						console.error(`处理房间 ${room_id} 的账单失败:`, error);
						// 继续处理其他房间，不中断整个流程
					}
				}
			}

			// 更新所有水电记录状态（包括未出租房间的记录）
			const currentTime = dayjs.tz().valueOf();

			// 按房间和月份分组所有记录，用于检查和生成水电表数据
			const allRecordsGrouped = new Map();
			for (const record of utility_records) {
				const key = `${record.room_id}_${record.date}_${record.type}`;
				let { _id , ...obj} = record
				console.log("查看去掉_id水电的对象",obj);
				if (!allRecordsGrouped.has(key)) {
					allRecordsGrouped.set(key, {
						id:_id,
						obj
					}
					);
				}
				// allRecordsGrouped.get(key).records.push(record);
			}

			// 处理每个房间的当月水电表数据
			for (const group of allRecordsGrouped.values()) {
				const { room_id, room_name, building_id, building_name, date, records ,type} = group.obj;
				console.log("遍历每个水电表的集合",room_id,date,type);
				try {
					// 检查当月是否已有水电表记录
					const existingRecode = await dbJQL.collection("fangke_utility_records")
						.where(`room_id == "${room_id}" && date == "${date}" && type == ${type}`)
						.get().then(res =>{
							console.log("查询当月是否有水电表记录",res);
							if(!res.data || res.data.length === 0){
								return false
							}else{
								return true
							}
						});
						console.log("结果",existingRecode);
					if (existingRecode) {
						// 更新水电表记录
						await dbJQL.collection("fangke_utility_records")
							.doc(group.id)
							.update({
								is_submit: true,
								update_time: currentTime
							});
						
						console.log(`更新房间 ${room_name} ${date} 月水电表数据`);
					} else {
						// 创建水电表记录
						group.obj.is_submit = true
						await dbJQL.collection("fangke_utility_records").add(group.obj);
						console.log(`创建房间 ${room_name} ${date} 月水电表数据`);
					}

				} catch (error) {
					// 继续处理其他房间，不中断整个流程
					console.error(`处理房间 ${group.obj} 的水电表数据失败:`, error);
					// if(error.errMsg === "未找到集合[fangke_utility_recoder]对应的schema"){
					// 	//没有找到对应表
					// 	group.obj.is_submit = true
					// 	await dbJQL.collection("fangke_utility_records").add(group.obj);
					// 	console.log(`创建房间 ${room_name} ${date} 月水电表数据`);
					// }
				}
			}


			return result(0, "success", {
				message: "处理成功",
				processed_bills: processedBills,
				total_records: utility_records.length,
				rented_records: rentedRecords.length,
				unrented_records: unrentedRecords.length,
				details: processResults
			});

		} catch (error) {
			console.error("处理水电账单失败:", error);
			return result(500, "fail", "处理失败: " + error.message);
		}
	},













	_after: function (error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}

}