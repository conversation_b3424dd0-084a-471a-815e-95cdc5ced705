<template>
	<view class="container">
		<uni-section title="任务名称" type="line">
			<view style="padding-left: 20rpx;padding-right: 20rpx;">
				<uni-easyinput v-model="task_name" placeholder="输入任务名称,方便区分任务"></uni-easyinput>
			</view>
		</uni-section>
		<uni-section title="选择楼房" type="line">
			<view style="padding-left: 20rpx;padding-right: 20rpx;">
				<uni-data-select v-model="building_id" :localdata="houses"
					@change="houseChange">
					
				</uni-data-select>
			</view>
		</uni-section>
		<uni-section title="选择发布时间" type="line"></uni-section>
		<dateSelect @select="onSelect"></dateSelect>
		<uni-section title="任务日期" type="line">
			<text style="padding-left: 20rpx;padding-right: 20rpx;">当前选择 ： {{time}}</text>
		</uni-section>
		<uni-section title="选择时间段" type="line">
			<radio-group @change="radioChange">
				<label class="time" v-for="(item, index) in items" :key="item.value">
					<view>
						<radio :value="item.value" :checked="index === current" />
					</view>
					<view>{{item.name}}</view>
				</label>
			</radio-group>
		</uni-section>
		<button type="warn" style="margin: 20rpx;" @click="commint">发布任务</button>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import uniCloud from '@dcloudio/uni-cloud';
import dateSelect from '../../pagesB/components/dateSelect/dateSelect.vue';

const api = uniCloud.importObject("ApiFuntion");

// 响应式数据
const time = ref(new Date().toLocaleDateString());
const items = ref([
  {
    value: '1',
    name: '上午',
    checked: 'true'
  },
  {
    value: '2',
    name: '下午'
  },
  {
    value: '3',
    name: '晚上'
  }
]);
const current = ref(0);
const building_id = ref("");
const houses = ref([]);
const uid = ref("");
const task_name = ref("");
const building_name = ref("");

// 获取 URL 参数
const route = useRoute();
const { uid: routeUid } = route.query;

// 页面加载时处理参数
onMounted(() => {
  console.log("taskInfo", routeUid);
  if (routeUid) {
    uid.value = routeUid;
    getBuilding();
  }
});

// 获取楼房信息
const getBuilding = async () => {
  await api.getBuilding(uid.value).then(res => {
    console.log("getBuilding", res);
    for (let i = 0; i < res.data.length; i++) {
      let item = {
        value: res.data[i]._id,
        text: res.data[i].name,
      };
      houses.value.push(item);
    }
  });
};

// 日期选择回调
const onSelect = (e) => {
  let now = new Date();
  time.value = now.getFullYear() + '/' + (now.getMonth() + 1) + '/' + e;
  uni.showToast({
    title: "选择了" + time.value,
    icon: 'none'
  });
};

// 单选按钮变化
const radioChange = (e) => {
  console.log("radioChange");
  for (let i = 0; i < items.value.length; i++) {
    if (items.value[i].value === e.detail.value) {
      current.value = i;
      break;
    }
  }
};

// 楼房选择变化
const houseChange = (e) => {
  console.log("选择房子", e);
  if (e) {
    building_id.value = e;
    let item = houses.value.find((item) => {
      if (item.value === e) {
        return item.text;
      }
    });
    building_name.value = item.text;
    console.log("building_name", building_name.value);
  } else {
    building_id.value = '';
  }
};

// 提交任务
const commint = () => {
  if (building_id.value.length) {
    if (task_name.value != '') {
      let timeVal = building_id.value + "_" + time.value + "_" + current.value;
      let task = {
        user_id: uid.value,
        name: task_name.value,
        building_id: building_id.value,
        update_time: Date.now(),
        time: timeVal,
        building_name: building_name.value
      };
      api.addTask(task).then(res => {
        uni.showToast({
          title: res.msg,
          icon: 'none'
        });
        if (res.code) {
          setTimeout(() => {
            uni.navigateBack();
          }, 800);
        }
      });
    } else {
      uni.showToast({
        title: '任务名称不能为空',
        icon: 'none'
      });
    }
  } else {
    uni.showToast({
      title: '请选择相应楼房',
      icon: 'none'
    });
  }
};
</script>

<style lang="scss">
	.container{
		box-sizing: border-box;
		.time{
			display: flex;
			padding: 20rpx;
		}
	}
	
</style>