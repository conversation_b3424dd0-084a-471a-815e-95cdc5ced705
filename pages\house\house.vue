<template>

	<view class="page">
		<z-paging ref="paging" v-model="currentDataList" @query="queryList">
			<template #top>
				<!-- #ifdef MP-WEIXIN -->
				<custom-nav-bar logo="智小居" placeholder="搜索目的地/攻略/好物" :showLeftIcon="true" showRightIcon
					rightIconType="plus" :showSearch="true" @onRight="handleRightEvent"></custom-nav-bar>
				<!-- #endif -->
				<!-- 筛选区域 -->
				<view class="filter-container">
					<view class="tab-container">
						<view :class="['tab', activeTab === '集中式' ? 'active' : '']" @click="handleTabChange('集中式')">集中式
						</view>
						<view :class="['tab', activeTab === '分散式' ? 'active' : '']" @click="handleTabChange('分散式')">分散式
						</view>
					</view>
					<view class="dropdown-container">
						<uni-data-select class="dropdown-item" :placeholder="'全部房源'" :localdata="
                    activeTab === '集中式' ? houseOptions : houseOptions2
                  " v-model="houseValue" @change="houseChange" />
						<!--选择房源-->
						<uni-data-select class="dropdown-item" :placeholder="'出租状态'" :localdata="rentStatusOptions"
							v-model="rentValue" @change="rentChange" />
						<!--选择出租状态-->
						<uni-data-select class="dropdown-item" :placeholder="'房型筛选'" :localdata="advancedOptions"
							v-model="advancedValue" @change="selectChange" />
						<!--房型筛选-->
					</view>
					<view class="status-tabs">
						<view :class="[
                    'status-tab',
                    tagCurrentIndex == index ? 'active' : ''
                  ]" v-for="(item, index) in tagStatus" @click="handleTag(index)" :key="index">
							{{ item.text }} ({{ item.number }})
						</view>
					</view>
				</view>
			</template>

			<template #empty>
				<!-- 空状态 -->
				<view class="empty-state">
					<image class="empty-image" :src="emptyImageUrl" mode="aspectFit" />
					<text class="empty-text">暂无房源信息</text>
					<text class="empty-desc">点击按钮添加您的第一个房源</text>
					<button type="primary" class="add-button" @click="handleAddBuilding">
						添加楼房
					</button>
				</view>
			</template>

			<!-- 房源列表区域 -->
			<view class="room-list">
				<template v-if="activeTab === '集中式'">
					<view v-for="(building, buildingIndex) in roomList" :key="buildingIndex" class="floor-section">
						<view class="building-header">
							<text class="building-title">{{ building.name }}</text>
							<view class="building-right">
								<view class="building-count" @click="jumpToSetting(building.id)">管理房源</view>
							</view>
						</view>
						<view v-for="(floorItem, floorIndex) in building.rooms" :key="floorIndex"
							v-if="building.rooms.length">
							<view class="floor-title">
								<text>{{ floorItem.floor }}楼</text>
							</view>
							<view class="room-grid">
								<view v-for="(room, roomIndex) in building.rooms[floorIndex].rooms" :key="room._id"
									@click="jumpToRoom(room._id)">
									<view :class="['room-card', getRoomStatus(room)]">
										<view class="room-number">{{ room.name }}</view>
										<view class="room-info">{{ room.layout }}
											{{ room.area ? room.area / 100 : 0 }}m²
										</view>
										<view class="room-price">¥ {{ room.rent ? room.rent / 100 : 0 }}</view>
										<view class="room-status">{{getRoomStatusText(room.rent_status)}}</view>
										<view class="status-tag">
											<view v-for="(status,index) in room.room_status" :key="index"
												class="status-item" :class="getStatusColor(status)">
												<view class="status-left">{{ getRentStatus(status) }}</view>
												<view v-if="getStatusNum(room,index) !== ''" class="status-middle">
												</view>
												<view v-if="getStatusNum(room,index) !== ''" class="status-right">
													{{ getStatusNum(room,index)}}
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view v-else>
							<view class="floor-title">
								<text>{{ building.name }}</text>
								<text class="manage-text" @click="jumpToSetting(building.id)">管理房源</text>
							</view>
						</view>
					</view>
				</template>
				<template v-else>
					<!-- 分散式房源 - 按楼栋分组显示 -->
					<view v-for="(building, buildingIndex) in roomList2" :key="buildingIndex">
						<view class="floor-title">
							<text>{{ building.name || '分散式房源' }}</text>
							<view class="manage-text" @click="jumpToSetting(building.id)">管理房源</view>
						</view>
						<view class="room-grid">
							<view v-for="(room, index) in building.rooms" :key="room._id || index"
								@click="jumpToRoom(room._id)">
								<view :class="['room-card', getRoomStatus(room)]">
									<view class="room-number">{{
                        room.name || room.number
                      }}</view>
									<view class="room-info">{{ room.layout }}
										{{ room.area ? room.area / 100 : 0 }}m²
									</view>
									<view class="room-price">¥ {{ room.rent ? room.rent / 100 : 0 }}</view>
									<view class="room-status">{{
                        getRoomStatusText(room.rent_status)
                      }}</view>
									<view class="status-tag">
										<view v-for="(status,index) in room.room_status" :key="index"
											class="status-item" :class="getStatusColor(status)">
											<view class="status-left">{{ getRentStatus(status) }}</view>
											<view v-if="getStatusNum(room,index) !== ''" class="status-middle">
											</view>
											<view v-if="getStatusNum(room,index) !== ''" class="status-right">
												{{ getStatusNum(room,index)}}
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>

			<uni-popup ref="event" backgroundColor="#fff" borderRadius="20px 20px 0 0"
				style="display: flex; margin-bottom: 0">
				<view class="event">
					<view class="event_bg" v-for="(item, index) in eventOptions" @click="handleEvent(index)"
						:key="index">
						<view class="event_icon">
							<image class="event_image" :src="item.image"></image>
						</view>
						<view class="event_text">{{ item.text }}</view>
					</view>
				</view>
			</uni-popup>
		</z-paging>
	</view>

</template>
<script lang="ts" setup>
	import { ref, computed, nextTick } from 'vue'
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { store2 } from '@/utils/js/store.js'
	const isLogin = computed(() => store2.hasLogin)
	const userInfo = computed(() => store2.userInfo)
	const event = ref(null)
	const hasRooms = ref(false)
	const activeTab = ref('集中式')
	const handleTabChange = (tab : string) => {
		activeTab.value = tab
		// 清空筛选条件
		houseValue.value = ''
		rentValue.value = ''
		advancedValue.value = '' // 清空房型筛选
		tagCurrentIndex.value = 0 // 重置为"全部"
		// 重新筛选数据
		filterRooms()
		// 重新计算标签数量
		calculateTagNumbers()
	}
	const tagStatus = ref([
		{ text: '全部', value: 0, number: 0 },
		{ text: '已预定', value: 1, number: 0 },
		{ text: '预约过期', value: 2, number: 0 },
		{ text: '欠款', value: 3, number: 0 },
		{ text: '即将到期', value: 3, number: 0 },
		{ text: '租约过期', value: 4, number: 0 }
	])
	const paging = ref(null)
	const tagCurrentIndex = ref(0)
	const houseValue = ref('')
	const rentValue = ref('')
	const advancedValue = ref('')
	const emptyImageUrl =
		'https://ai-public.mastergo.com/ai/img_res/9b0f160e9353b302dd2284ffb651ec93.jpg'
	let houseOptions = [{ text: '全部房源', value: 'all' }]
	let houseOptions2 = [{ text: '全部房源', value: 'all' }]
	const rentStatusOptions = [
		{ text: '全部状态', value: 'all' },
		{ text: '已出租', value: 'rented' },
		{ text: '空闲中', value: 'vacant' },
		{ text: '占用', value: 'occupy' }
	]
	const advancedOptions = [
		{ text: '全部房型', value: 'all' },
		{ text: '单间', value: '单间' },
		{ text: '一房一厅', value: '1室1厅' },
		{ text: '两房一厅', value: '2室1厅' },
		{ text: '三房一厅', value: '3室1厅' },
		{ text: '三房两厅', value: '3室2厅' }
	]
	const eventOptions = [
		{
			text: '添加房源',
			image:
				'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/house.png'
		},
		{
			text: '新建房型',
			image:
				'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/note.png'
		},
		{
			text: '一键开门',
			image:
				'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/lock.png'
		},
		{
			text: '添加预定',
			image:
				'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/user_book.png'
		},
		{
			text: '群发消息',
			image:
				'https://mp-ea7a5bd8-4c6f-40b9-ad44-04bddfbf61bf.cdn.bspapp.com/fangke/message.png'
		}
	]
	let buildingList = ref([])
	let buildingList2 = ref([])

	const roomList = ref([])
	const roomList2 = ref([])

	// 用于z-paging的数据列表
	const currentDataList = ref([])
	const getRoomStatus = room => {
		let res = ''
		switch (room.rent_status) {
			case 0: //未出租
				res = 'vacant'
				break
			case 1: //已出租
				res = 'rented'
				break
			case 2: //占用
				res = 'occupy'
				break
		}
		return res
	}

	const getRoomStatusText = (status : number) => {
		let res = ''
		switch (status) {
			case 0:
				res = '房间空闲'
				break
			case 1:
				res = '已出租'
				break
			case 2:
				res = '占用'
				break
		}
		return res
	}

	const getRentStatus = (status : number) => {
		let res = ''
		switch (status) {
			case 0: //空闲
				res = '空'
				break
			case 1: //已缴费
				res = '完'
				break
			case 2: //待缴费
				res = '待'
				break
			case 3: //欠费超时
				res = '欠'
				break
			case 4: //租约即将到期
				res = '快'
				break
			case 5: //租约到期
				res = '逾'
				break
			case 6: //待结算
				res = '结'
				break
			case 7: //已预定
				res = '定'
				break
			case 8: //预定过期
				res = '过'
				break
			case 9: //待结算未预定
				res = '结未'
				break
			case 10: //待结算已预定
				res = '结定'
				break
			case 11: //租约待审核
				res = '审'
				break
			case 12: //退租待审核
				res = '退审'
				break
		}
		return res
	}
	const getStatusColor = (status) => {
		let res = ''
		res = 'status' + status
		return res
	}

	const getStatusNum = (room, index) => {
		let res = ''
		switch (room.room_status[index]) {
			case 0: //空闲
				if (room.idle_num > 99) {
					res = "99+"
				} else {
					res = room.idle_num || 99 + "天"
				}
				break
			case 3: //欠费超时
				if (room.arrears_num > 99) {
					res = "99+"
				} else {
					res = room.arrears_num || 0 + "天"
				}
				break

			case 5: //租约到期
				if (room.overday_num > 99) {
					res = "99+"
				} else {
					res = room.overday_num || 0 + "天"
				}
				break

		}
		return res
	}

	const queryList = () => {
		getRoom()
	}

	const onRefresh = () => {
		getRoom()
	}

	const handleAddBuilding = () => {
		if (isLogin.value) {
			uni.navigateTo({
				url: '/pagesB/add/addHouse'
			})
		} else {
			uni.navigateTo({
				url: '/unid/uni-id-pages/pages/login/login-withoutpwd'
			})
		}
	}
	const handleTag = (index : any) => {
		tagCurrentIndex.value = index
		filterRooms()
	}

	const handleRightEvent = () => {
		if (isLogin.value) {
			event.value.open('bottom')
		} else {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
		}
	}

	const handleEvent = (index : number) => {
		console.log('handleEvent', index)
		switch (index) {
			case 0:
				uni.navigateTo({
					url: '/pagesB/add/addHouse'
				})
				break
			case 1:
				uni.navigateTo({
					url: '/pagesB/layout/layout'
				})
				break
			case 2:
				uni.navigateTo({
					url: '/pagesB/doorLock/doorLock'
				})
				break
			case 3:
				uni.navigateTo({
					url: '/pagesB/prebook/prebook'
				})
				break
			case 4:
				break
		}
	}

	const houseChange = e => {
		console.log('房源选择变化:', e)
		houseValue.value = e
		filterRooms()
		calculateTagNumbers() // 重新计算标签数量
	}

	const rentChange = e => {
		console.log('出租状态变化:', e)
		rentValue.value = e
		filterRooms()
		calculateTagNumbers() // 重新计算标签数量
	}

	const selectChange = e => {
		console.log('房型筛选变化:', e)
		advancedValue.value = e
		filterRooms()
		calculateTagNumbers() // 重新计算标签数量
	}
	// 统一的筛选方法，同时考虑房源选择、出租状态、标签状态和房型筛选
	const filterRooms = () => {
		if (activeTab.value === '集中式') {
			// 首先根据房源ID筛选楼栋
			let filteredBuildings = buildingList.value

			if (houseValue.value && houseValue.value !== 'all') {
				filteredBuildings = buildingList.value.filter(item => {
					return item.id === houseValue.value
				})
			}

			// 然后根据出租状态筛选房间
			if (rentValue.value && rentValue.value !== 'all') {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.map(floor => {
							return {
								...floor,
								rooms: floor.rooms.filter(room => {
									if (rentValue.value === 'rented') return room.rent_status === 1 // 已出租
									if (rentValue.value === 'vacant') return room.rent_status === 0 // 未出租
									if (rentValue.value === 'occupy') return room.rent_status === 2 // 占用
									return false
								})
							}
						})
					}
				})
			}

			// 根据房型筛选房间
			if (advancedValue.value && advancedValue.value !== 'all') {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.map(floor => {
							return {
								...floor,
								rooms: floor.rooms.filter(room => {
									console.log('room.layout', room.layout)
									return room.layout.includes(advancedValue.value)
								})
							}
						})
					}
				})
			}

			// 最后根据标签状态筛选房间
			if (tagCurrentIndex.value > 0) {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.map(floor => {
							return {
								...floor,
								rooms: floor.rooms.filter(room => {
									return filterRoomByTag(room, tagCurrentIndex.value)
								})
							}
						})
					}
				})
			}

			roomList.value = filteredBuildings
			console.log('filterRooms 集中式', roomList.value)
		} else {
			// 分散式房源的筛选逻辑
			let filteredBuildings = buildingList2.value

			if (houseValue.value && houseValue.value !== 'all') {
				filteredBuildings = buildingList2.value.filter(item => {
					return item.id === houseValue.value
				})
			}

			// 根据出租状态筛选房间
			if (rentValue.value && rentValue.value !== 'all') {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.filter(room => {
							if (rentValue.value === 'rented') return room.rent_status === 1 // 已出租
							if (rentValue.value === 'vacant') return room.rent_status === 0 // 未出租
							if (rentValue.value === 'occupy') return room.rent_status === 2 // 占用
							return false
						})
					}
				})
			}

			// 根据房型筛选房间
			if (advancedValue.value && advancedValue.value !== 'all') {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.filter(room => {
							console.log('room.layout', room.layout)
							return room.layout.includes(advancedValue.value)
						})
					}
				})
			}

			// 根据标签状态筛选房间
			if (tagCurrentIndex.value > 0) {
				filteredBuildings = filteredBuildings.map(building => {
					return {
						...building,
						rooms: building.rooms.filter(room => {
							return filterRoomByTag(room, tagCurrentIndex.value)
						})
					}
				})
			}

			roomList2.value = filteredBuildings
			console.log('filterRooms 分散式', roomList2.value)
		}
	}

	// 根据标签筛选房间的辅助方法
	const filterRoomByTag = (room : any, tagIndex : number) => {
		switch (tagIndex) {
			case 0: // 全部
				return true
			case 1: // 已预定
				return room.room_status === 1 // 预约看房
			case 2: // 欠款
				return room.room_status === 3 // 欠费超时
			case 3: // 即将到期 - 在一个月内到期
				return room.room_status === 5 // 即将到期 - 在一个月内到期
			case 4: // 租约过期
				return room.room_status === 4 // 租约过期
			case 5:
				if (!room.end_time) return false
				const endDate = new Date(room.end_time)
				const currentDate = new Date()
				const oneMonthLater = new Date()
				oneMonthLater.setMonth(currentDate.getMonth() + 1)
				return endDate <= oneMonthLater && endDate >= currentDate
			default:
				return true
		}
	}

	const jumpToRoom = id => {
		uni.navigateTo({
			url: '/pagesB/roomInfo/roomInfo?id=' + id + '&uid=' + userInfo.value._id
		})
	}

	const jumpToSetting = id => {
		let type = 1
		if (activeTab.value === '集中式') {
			type = 1
		} else {
			type = 2
		}

		// 如果是分散式房源的特殊标识，跳转到分散式房源管理页面
		if (id === 'scattered') {
			uni.navigateTo({
				url: '/pagesB/updateHouse/updateHouse?type=' + type
			})
		} else {
			uni.navigateTo({
				url: '/pagesB/updateHouse/updateHouse?id=' + id + '&type=' + type
			})
		}
	}

	onLoad(() => {
		console.log('onload', isLogin.value)
		if (isLogin.value) {
			// if(!buildingList.value.length && !roomList.value.length){
			// 	hasRooms.value = false
			// }
			getRoom()
		} else {
			hasRooms.value = false
			currentDataList.value = []
		}
	})

	// 页面显示时刷新数据（从预定页面返回时会触发）
	onShow(() => {
		console.log('onShow - 页面显示，检查是否需要刷新数据')
		if (isLogin.value) {
			// 刷新房源数据以获取最新的房间状态
			getRoom()
		}
	})

	const getRoom = async () => {
		let params = {
			uid: userInfo.value._id
		}
		await uni.$lkj.api
			.getRoom(params)
			.then(res => {
				console.log('getRoom', res)
				if (res.errCode == 0) {
					let { building_list } = res.data
					console.log('building_list', building_list)
					if (building_list.length == 0) {
						hasRooms.value = false
						// 更新空数据
						currentDataList.value = []
						// 告诉z-paging没有数据
						if (paging.value) {
							paging.value.complete([])
						}
					} else {
						hasRooms.value = true
						buildingList.value = []
						buildingList2.value = []
						houseOptions = [{ text: '全部房源', value: 'all' }]
						houseOptions2 = [{ text: '全部房源', value: 'all' }]
						let tabActivte = false
						building_list.forEach(item => {
							if (item.type == 1) {
								//集中式
								tabActivte = true
								let list = item._id.fangke_room
								let rooms = []
								let id = ''
								if (item._id.fangke_room.length) {
									rooms = groupRoomsByFloor(list)
								}
								id = item._id._value
								buildingList.value.push({
									id: id,
									name: item.number,
									floor: item.floor,
									rooms: rooms
								})
								houseOptions.push({ text: item.number, value: id })
							} else {
								//分散式
								let rooms = []
								let id = ''
								rooms = item._id.fangke_room
								id = item._id._value
								buildingList2.value.push({
									id: id,
									name: item.number,
									floor: item.floor,
									rooms: rooms
								})
								houseOptions2.push({ text: item.number, value: id })
							}
						})
						if (tabActivte) {
							activeTab.value = '集中式'
						} else {
							activeTab.value = '分散式'
						}
						roomList.value = buildingList.value
						roomList2.value = buildingList2.value
						// 初始化完成后应用筛选
						filterRooms()
						// 计算各个标签的房间数量
						calculateTagNumbers()
						// 更新z-paging的数据列表
						currentDataList.value = building_list
						console.log('buildingList', buildingList)
						console.log('buildingList2', buildingList2)
						// 告诉z-paging数据加载完成
						if (paging.value) {
							paging.value.complete(building_list)
						}
					}
				} else {
					hasRooms.value = false
					currentDataList.value = []
					uni.showToast({
						title: res.errMsg || '获取房源数据失败',
						icon: 'none'
					})
					// 加载失败时也要告诉z-paging完成
					if (paging.value) {
						paging.value.complete([])
					}
				}
			})
			.catch(err => {
				hasRooms.value = false
				currentDataList.value = []
				uni.showToast({
					title: err.message || '网络请求失败',
					icon: 'none'
				})
				// 加载失败时告诉z-paging完成
				if (paging.value) {
					paging.value.complete([])
				}
			})
	}

	const groupRoomsByFloor = data => {
		// 检查数据格式是否正确
		if (!data || !Array.isArray(data)) {
			throw new Error('输入数据格式不正确，缺少room数组')
		}

		// 创建一个Map来按楼层分组房间
		const floorMap = new Map()

		// 遍历每个房间，将它们添加到对应的楼层组
		data.forEach(room => {
			const floor = room.floor
			if (!floorMap.has(floor)) {
				floorMap.set(floor, {
					floor,
					rooms: []
				})
			}
			floorMap.get(floor).rooms.push(room)
		})

		// 将Map转换为数组并按楼层排序
		const result = Array.from(floorMap.values()).sort((a, b) => a.floor - b.floor)
		console.log('groupRoomsByFloor', result)
		return result
	}

	// 计算各个标签状态的房间数量（根据当前筛选条件，但不包含标签筛选）
	const calculateTagNumbers = () => {
		let totalRooms = 0
		let reservedRooms = 0 // 已预定
		let arrearsRooms = 0 // 欠款
		let soonExpireRooms = 0 // 即将到期
		let expiredRooms = 0 // 租约过期

		// 获取当前应该统计的房间列表（应用房源、出租状态、房型筛选，但不应用标签筛选）
		let currentRoomList =
			activeTab.value === '集中式' ? buildingList.value : buildingList2.value

		// 应用房源筛选
		if (houseValue.value && houseValue.value !== 'all') {
			currentRoomList = currentRoomList.filter(item => {
				return item.id === houseValue.value
			})
		}

		// 应用出租状态和房型筛选
		currentRoomList = currentRoomList.map(building => {
			let filteredBuilding = { ...building }

			if (activeTab.value === '集中式') {
				filteredBuilding.rooms = building.rooms.map(floor => {
					let filteredRooms = floor.rooms

					// 出租状态筛选
					if (rentValue.value && rentValue.value !== 'all') {
						filteredRooms = filteredRooms.filter(room => {
							if (rentValue.value === 'rented') return room.rent_status === 1
							if (rentValue.value === 'vacant') return room.rent_status === 0
							if (rentValue.value === 'occupy') return room.rent_status === 2
							return false
						})
					}

					// 房型筛选
					if (advancedValue.value && advancedValue.value !== 'all') {
						filteredRooms = filteredRooms.filter(room => {
							console.log('room.layout', room.layout)
							return room.layout.includes(advancedValue.value)
						})
					}

					return { ...floor, rooms: filteredRooms }
				})
			} else {
				let filteredRooms = building.rooms

				// 出租状态筛选
				if (rentValue.value && rentValue.value !== 'all') {
					filteredRooms = filteredRooms.filter(room => {
						if (rentValue.value === 'rented') return room.rent_status === 1
						if (rentValue.value === 'vacant') return room.rent_status === 0
						if (rentValue.value === 'occupy') return room.rent_status === 2
						return false
					})
				}

				// 房型筛选
				if (advancedValue.value && advancedValue.value !== 'all') {
					filteredRooms = filteredRooms.filter(room => {
						console.log('room.layout', room.layout)
						return room.layout.includes(advancedValue.value)
					})
				}

				filteredBuilding.rooms = filteredRooms
			}

			return filteredBuilding
		})

		// 统计各种状态的房间数量
		currentRoomList.forEach(building => {
			if (activeTab.value === '集中式') {
				building.rooms.forEach(floor => {
					floor.rooms.forEach(room => {
						totalRooms++

						if (room.room_status.indexOf(7) !== -1) reservedRooms++
						if (room.room_status.indexOf(3) !== -1) arrearsRooms++
						if (room.room_status.indexOf(5) !== -1) expiredRooms++
						if (room.room_status.indexOf(4) !== -1) soonExpireRooms++
					})
				})
			} else {
				building.rooms.forEach(room => {
					totalRooms++

					if (room.room_status.indexOf(7) !== -1) reservedRooms++
					if (room.room_status.indexOf(3) !== -1) arrearsRooms++
					if (room.room_status.indexOf(5) !== -1) expiredRooms++
					if (room.room_status.indexOf(4) !== -1) soonExpireRooms++
				})
			}
		})

		// 更新tagStatus数组
		tagStatus.value = [
			{ text: '全部', value: 0, number: totalRooms },
			{ text: '已预定', value: 1, number: reservedRooms },
			{ text: '欠款', value: 2, number: arrearsRooms },
			{ text: '即将到期', value: 3, number: soonExpireRooms },
			{ text: '租约过期', value: 4, number: expiredRooms }
		]

		console.log('标签数量统计:', {
			总房间: totalRooms,
			已预定: reservedRooms,
			欠款: arrearsRooms,
			即将到期: soonExpireRooms,
			租约过期: expiredRooms
		})
	}
</script>
<style scoped>
	page {
		height: 100%;
	}

	.page {
		height: 100%;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}

	.filter-container {
		background-color: #ffffff;
		padding: 0 20rpx;
		flex-shrink: 0;
	}

	.tab-container {
		display: flex;
		margin-bottom: 15rpx;
	}

	.tab {
		flex: 1;
		text-align: center;
		padding: 10rpx 20rpx;
		font-size: 14px;
		color: #666666;
	}

	.tab.active {
		color: #00b578;
		position: relative;
	}

	.tab.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #00b578;
	}

	.dropdown-container {
		display: flex;
		margin-bottom: 10rpx;
	}

	.dropdown-item {
		flex: 1;
		margin: 0 10rpx;
	}

	.status-tabs {
		display: flex;
		overflow-x: auto;
		gap: 10rpx;
	}

	.status-tab {
		padding: 10rpx 20rpx;
		font-size: 14px;
		color: #666666;
		white-space: nowrap;
		margin-bottom: 10rpx;
		background-color: #f3f4f8;
		border-radius: 20rpx;
	}

	.status-tab.active {
		color: #00b578;
		background-color: #e6f7f1;
	}

	.floor-section {
		margin-bottom: 10rpx;
	}

	/* 楼房分组 */
	.building-section {
		margin-bottom: 32rpx;
	}

	.building-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 8rpx;
		border-bottom: 1rpx solid #4caf50;
		box-sizing: border-box;
	}

	.building-title {
		font-size: 36rpx;
		font-weight: 600;
		padding: 0 20rpx;
		color: #333;
	}

	.building-right {
		display: flex;
		gap: 10rpx;
	}

	.building-count {
		font-size: 28rpx;
		color: #666;
	}

	/* 楼层子分组 */
	.floor-subsection {
		margin-bottom: 10rpx;
	}

	.room-list {
		flex: 1;
		overflow: auto;
	}

	.floor-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 20rpx 10rpx 20rpx;
		font-size: 16px;
		font-weight: bold;
	}

	.manage-text {
		font-size: 14px;
		color: #666666;
		font-weight: bold;
	}

	.room-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30rpx;
		padding: 0 20rpx 20rpx 20rpx;
	}

	.room-card {
		border-radius: 16rpx;
		padding: 25rpx;
		position: relative;
	}

	.room-card:not(.hidden) {}

	.hidden {
		display: none;
	}

	.room-card.vacant {
		background: linear-gradient(135deg, #6A7692, #8591AA);
	}

	.room-card.reserved {
		background-color: #1989fa;
	}

	.room-card.expired {
		background-color: #ff9f43;
	}

	.room-card.rented {
		background: linear-gradient(135deg, #299980, #3CAA91);
	}

	.room-card.ready {
		background-color: #f27c2a;
	}

	.room-card.roomdue {
		background-color: #d94f1b;
	}

	.room-card.overdue {
		background-color: #ff5c5c;
	}

	.room-card.due {
		background-color: #95ec69;
	}

	.room-card.occupy {
		background: linear-gradient(135deg, #2196f3, #42a5f5);
	}

	.room-number {
		font-size: 24px;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 10rpx;
	}

	.room-info {
		font-size: 14px;
		color: #ffffff;
		margin-bottom: 10rpx;
	}

	.room-price {
		font-size: 16px;
		color: #ffffff;
		margin-bottom: 10rpx;
	}

	.room-status {
		font-size: 14px;
		color: #ffffff;
	}

	.status-tag {
		position: absolute;
		top: 25rpx;
		right: 25rpx;
		border-radius: 8rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 10rpx;
	}

	.status-item {
		display: flex;
		align-items: center;
		font-size: 12px;
		color: #666666;
		background-color: white;
		border-radius: 8rpx;
	}

	.status-item.status0 {
		border: #4A6693 solid 1rpx;
		color: #4A6693;
	}

	.status-item.status1 {
		border: #f6e58d solid 1rpx;
		color: #f6e58d;
	}

	.status-item.status2 {
		border: #ff8f40 solid 1rpx;
		color: #ff8f40;
	}

	.status-item.status3 {
		border: #ff4f2e solid 1rpx;
		color: #ff4f2e;
	}

	.status-item.status4 {
		border: #ff5e3a solid 1rpx;
		color: #ff5e3a;
	}

	.status-item.status5 {
		border: #ff6f61 solid 1rpx;
		color: #ff6f61;
	}

	.status-item.status6 {
		border: #f9ca24 solid 1rpx;
		color: #f9ca24;
	}

	.status-item.status7 {
		border: #7ab0e3 solid 1rpx;
		color: #7ab0e3;
	}

	.status-item.status8 {
		border: #4f8cd8 solid 1rpx;
		color: #4f8cd8;
	}

	.status-item.status9 {
		border: #1e6bc0 solid 1rpx;
		color: #1e6bc0;
	}

	.status-item.status10 {
		border: #1a3a78 solid 1rpx;
		color: #1a3a78;
	}

	.status-item.status11 {
		border: #1a3a78 solid 1rpx;
		color: #1a3a78;
	}

	.status-item.status12 {
		border: #a95eff solid 1rpx;
		color: #a95eff;
	}

	.status-item.status12 {
		border: #5e007f solid 1rpx;
		color: #5e007f;
	}

	.status-left {
		padding: 5rpx;
	}

	.status-middle {
		height: 40rpx;
		width: 2rpx;
		background-color: #666666;
	}

	.status-right {
		padding: 5rpx;
	}

	.empty-state {
		flex: 1;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		background-color: white;
	}

	.empty-image {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 40rpx;
	}

	.empty-text {
		font-size: 16px;
		color: #333333;
		margin-bottom: 20rpx;
	}

	.empty-desc {
		font-size: 14px;
		color: #999999;
		margin-bottom: 40rpx;
	}

	.add-button {
		width: 320rpx !important;
		background-color: #00b578 !important;
	}

	.event {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-content: center;
	}

	.event_bg {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding-top: 50rpx;
	}

	.event_icon {
		width: 80rpx;
		height: 80rpx;
	}

	.event_image {
		width: 100%;
		height: 100%;
	}

	.event_text {
		font-size: 28rpx;
		margin-top: 5rpx;
	}
</style>