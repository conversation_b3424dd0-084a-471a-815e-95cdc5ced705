<template>
	<view class="container">
		<uni-section title="房间信息" type="line" class="info">
			<template v-slot:right>
				<view class="bill">
					<view @click="goRoom">
						<text>修改房间</text>
					</view>
				</view>
			</template>
		</uni-section>
		<view class="box">
			<view class="head">
				<text>{{ info.building_name || '' }}-{{ info.name }}</text>
				<view class="state">
					<uni-tag :inverted="true" :text="getRoomTip(info)" :type="getColor(info.room_status)"
						size="mini" style="margin-right: 20rpx" v-if="info.room_status !== 0" />
					<uni-tag :inverted="true" :text="info.day + '号交租'" type="error" size="mini" v-if="info.day"
						style="margin-right: 20rpx" />
					<uni-tag :inverted="true" :text="getRentTip(info.rent_status)" :type="getColor(info.rent_status)"
						size="mini" />
				</view>
			</view>

			<view class="body">
				<view class="left">
					<view class="img">
						<image class="_image" src="../../static/area.png"></image>
					</view>
					<text>面积（m²）：</text>
					<text class="values">{{ info.area ? info.area / 100 : 0 }}</text>
				</view>
				<view style="width: 5rpx; height: 30rpx; background-color: gray"></view>
				<view class="right">
					<view class="img">
						<image class="_image" src="../../static/huxing.png"></image>
					</view>
					<text>户型：</text>
					<text class="values">{{ info.layout }}</text>
				</view>
			</view>
			<view class="body">
				<view class="left">
					<view class="img">
						<image class="_image" src="../../static/zufang.png"></image>
					</view>
					<text>付租方式：</text>
					<text class="values">{{ info.payment }}</text>
				</view>
				<view style="width: 5rpx; height: 30rpx; background-color: gray"></view>
				<view class="right">
					<view class="img">
						<image class="_image" src="../../static/zujin.png"></image>
					</view>
					<text>租金（元/月）：</text>
					<text class="values">{{ info.rent ? info.rent / 100 : 0 }}</text>
				</view>
			</view>
			<view class="body">
				<view class="left">
					<view class="img">
						<image class="_image" src="../../static/message_0.png"></image>
					</view>
					<text>所在楼层：</text>
					<text class="values">{{ info.floor ? info.floor + '楼' : '' }}</text>
				</view>
				<view style="width: 5rpx; height: 30rpx; background-color: gray"></view>
				<view class="right">
					<view class="img">
						<image class="_image" src="../../static/zujin.png"></image>
					</view>
					<text>押金（元）：</text>
					<text class="values">{{
            info.deposit ? info.deposit / 100 : 0
          }}</text>
				</view>
			</view>
		</view>
		<uni-section title="当月水电" type="line" class="info"></uni-section>
		<view class="ele_form">
			<view class="form_container">
				<view class="form_row">
					<view class="form_item_card">
						<view class="item_label">电表量</view>
						<view class="item_value">
							<view style="font-weight: 600">{{
                info.ele ? info.ele / 100 : 0
              }}</view>
							<uni-icons style="margin-left: 10rpx" type="right"></uni-icons>
						</view>
					</view>
					<view class="form_item_card">
						<view class="item_label">水表量</view>
						<view class="item_value">
							<view style="font-weight: 600">{{
                info.water ? info.water / 100 : 0
              }}</view>
							<uni-icons style="margin-left: 10rpx" type="right"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>
		<uni-section title="备注" type="line" class="info"></uni-section>
		<text style="
        font-size: 25rpx;
        color: gray;
        margin-left: 30rpx;
        margin-right: 30rpx;
      ">{{ info.desc === '' ? '无' : info.desc }}</text>
		<view class="facilities-section">
			<uni-section title="设备配置" type="line" class="info"></uni-section>
			<view class="facilities-grid">
				<view v-for="(item, index) in facilities" :key="index" class="facility-item"
					:class="{ active: selectedFacilities.includes(item.value) }">
					{{ item.name }}
				</view>
			</view>
		</view>

		<uni-section title="当月租金 :" type="circle" class="total">
			<template v-slot:right style="display: flex; align-content: center">
				<text class="money">{{ info.sum }}</text>
			</template>
		</uni-section>

		<view class="foot">
			<view v-if=" info.rent_status == 1 " class="foot_container">
				<view class="foot_btn_wrapper">
					<button class="foot_btn_left" @click="jumpContracts">查看租约</button>
					<button class="foot_btn_right" :type="writeEnable ? 'default' : 'warn'" @click="onUpload"
						:disabled="writeEnable">
						{{ uploadName }}
					</button>
				</view>
			</view>
			<view v-else-if="info.rent_status == 0 && info.room_status.some(item => item === 7 || item === 10 )" class="foot_container">
				<view class="foot_btn_wrapper">
					<button class="foot_btn_left" @click="jumpBooking">查看预约</button>
					<button class="foot_btn_right" :type="writeEnable ? 'default' : 'warn'" @click="onUpload"
						:disabled="writeEnable">
						{{ uploadName }}
					</button>
				</view>
			</view>
			<view v-else class="foot_container">
				<view class="foot_btn_wrapper">
					<button class="foot_btn_left" @click="jumpBooking">添加预定</button>
					<button class="foot_btn_right" type="primary" @click="checkin">
						办理入住
					</button>
				</view>
			</view>
		</view>

		<!-- <button type="warn" @click="onDelete" style="margin-top: 20rpx;">删除房间</button> -->
		<template>
			<uni-popup ref="popup" @close="close">
				<view style="padding: 20rpx; width: auto; height: auto">
					<image :src="imgUrl"></image>
				</view>
			</uni-popup>
		</template>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		dayjs
	} from '../../utils/dayjs.min'
	const selectedFacilities = ref([])
	const db = uniCloud.databaseForJQL()
	const facilities = [{
			name: '智能锁',
			value: 1
		},
		{
			name: '床',
			value: 2
		},
		{
			name: '衣柜',
			value: 3
		},
		{
			name: '书桌椅',
			value: 4
		},
		{
			name: '暖气',
			value: 5
		},
		{
			name: '天然气',
			value: 6
		},
		{
			name: '宽带',
			value: 7
		},
		{
			name: '电视',
			value: 8
		},
		{
			name: '冰箱',
			value: 9
		},
		{
			name: '洗衣机',
			value: 10
		},
		{
			name: '空调',
			value: 11
		},
		{
			name: '热水器',
			value: 12
		},
		{
			name: '微波炉',
			value: 13
		},
		{
			name: '油烟机',
			value: 14
		},
		{
			name: '电磁炉',
			value: 15
		},
		{
			name: '阳台',
			value: 16
		},
		{
			name: '可做饭',
			value: 17
		},
		{
			name: '独立卫生间',
			value: 18
		},
		{
			name: '沙发',
			value: 19
		},
		{
			name: '电梯',
			value: 20
		}
	]
	const rent = ref('')
	const info = ref({})
	const uploadName = ref('未出租')
	const writeEnable = ref(true)
	const _id = ref('')
	const _buildingId = ref('')
	const imgShow = ref(false)
	const imgUrl = ref('')
	const uid = ref('')
	// const address = ref('')
	// const floor  = ref(1)
	// 页面加载时处理参数
	onLoad(e => {
		console.log('onload', e)
		if (e.id && e.uid) {
			_id.value = e.id
			uid.value = e.uid
			getInfo()
		}
	})

	// 获取房间信息
	const getInfo = async () => {
		let params = {
			id: _id.value,
			uid: uid.value
		}
		uni.showLoading({
			title: '加载中'
		})
		await uni.$lkj.api.getRoomInfo(params).then(res => {
			console.log('getRoomInfo', res)
			updateInfo(res.data)
			uni.hideLoading()
		})
	}

	// 更新房间信息
	const updateInfo = data => {
		console.log('更新房子', data)
		info.value = data
		selectedFacilities.value = info.value.devices || []
		_buildingId.value = info.value.building_id
		if (info.value.agreement) {
			imgUrl.value = info.value.agreement.url || ''
		}
		if (data.room_status == 0) {
			uploadName.value = '未出租'
			writeEnable.value = true
		} else if (data.room_status == 1) {
			uploadName.value = '已交租'
			writeEnable.value = true
		} else if (data.room_status == 2 || data.room_status == 3) {
			uploadName.value = '确认交租'
			writeEnable.value = false
		}
	}

	const checkin = () => {
		uni.navigateTo({
			url: '/pagesB/checkin/checkin?room_id=' +
				info.value._id +
				'&uid=' +
				info.value.uid +
				'&building_id=' +
				info.value.building_id +
				'&room_name=' +
				info.value.building_name +
				'-' +
				info.value.name
		})
	}

	const jumpContracts = () => {
		uni.navigateTo({
			url: '/pagesB/leaseInfo/leaseInfo?room_id=' + info.value._id
		})
	}

	// 查看预约
	const jumpBooking = () => {
		console.log("预约",info.value);
		let isBook = info.value.room_status.some(item => item ===7 || item ===10)
		let status = 0
		if(isBook){
			status = 1
		}
		uni.navigateTo({
			url: '/pagesB/addBooking/addBooking?room_id=' + info.value._id + '&status='+status+'&name='+info.value.building_name+'-'+info.value.name
		})
	}

	// 确认交租
	const onUpload = () => {
		console.log('onUpload', info.room_status)
		if (!_buildingId.value) {
			uni.showToast({
				title: '匹配不到楼房',
				icon: 'none'
			})
			return
		}
		let _id_val = _id.value
		let _buildingId_val = _buildingId.value
		let sum = info.value.sum
		uni.showModal({
			title: '温馨提示',
			content: '确认是否已交租?',
			success: res => {
				console.log('用户点击确定', sum)
				if (res.confirm) {
					let desc = ''
					if (roomFormData.value.desc.length != 0) {
						desc = roomFormData.value.desc
					}
					api
						.commitRent(
							_id_val,
							_buildingId_val,
							sum,
							desc,
							mode.value,
							address.value
						)
						.then(res => {
							console.log('提交成功', res)
							writeEnable.value = true
							if (res.code) {
								uni.showToast({
									title: res.msg
								})
								isFinish.value = true
							}
						})
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
		if (isFinish.value) {
			uploadName.value = '已交租'
		} else {
			uploadName.value = '确认交租'
		}
	}

	// 查看合同
	const agreement = () => {
		if (imgUrl.value) {
			$refs.popup.open()
		} else {
			uni.showToast({
				title: '无合同图片，请去修改房间页添加',
				icon: 'error'
			})
		}
	}

	// 获取缴费状态提示
	const getRoomTip = info => {
		console.log("getRoomTip",info);
		let tip = ''
		if(info != {} && info.room_status && info.room_status.length >0){
			switch (info.room_status[0]) {
				case 0:
					tip = '空闲'
					break
				case 7:
					tip = '已预定'
					break
				case 2:
					tip = '待缴费'
					break
				case 3:
					tip = '欠费超时'
					break
				case 5:
					tip = '租约到期'
					break
				case 6:
					tip = '待结算'
					break
			}
		}
		
		return tip
	}

	// 获取出租状态提示
	const getRentTip = status => {
		let tip = ''
		switch (status) {
			case 0:
				tip = '未出租'
				break
			case 1:
				tip = '已出租'
				break
			case 2:
				tip = '占用'
				break
		}
		return tip
	}

	// 获取状态颜色
	const getColor = status => {
		let tip = ''
		switch (status) {
			case 0:
				tip = 'default'
				break
			case 1:
				tip = 'primary'
				break
			case 2:
				tip = 'warning'
				break
			case 3:
				tip = 'error'
				break
			case 4:
				tip = 'success'
				break
			default:
				tip = 'primary'
				break
		}
		return tip
	}

	// 前往修改房间
	const goRoom = () => {
		uni.navigateTo({
			url: '/pagesB/add/addRoom?room_id=' + info.value._id
		})
	}

</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		box-sizing: border-box;
		border-radius: 10rpx;
		padding-bottom: 100rpx;

		.bill {
			color: #65a548;
			display: flex;
		}

		.facilities-section {}

		.section-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 32rpx;
			display: block;
		}

		.facilities-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 16rpx;
		}

		.facility-item {
			height: 76rpx;
			line-height: 76rpx;
			text-align: center;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			font-size: 26rpx;
			color: #666;
			border: 1px solid #e8e8e8;
			transition: all 0.2s ease;
		}

		.facility-item:active {
			transform: scale(0.95);
		}

		.facility-item.active {
			background-color: #e8f5e8;
			color: #07c160;
			border-color: #07c160;
			font-weight: 500;
		}

		.box {
			display: flex;
			flex-direction: column;
			border: 1rpx solid #eee;
			padding: 20rpx;
			border-radius: 10rpx;
			box-shadow: 1px 1px 5px gray;
			margin: 0 0 10rpx 0;

			.head {
				display: flex;
				font-size: 30rpx;
				justify-content: space-between;
				padding: 0 20rpx;
				font-weight: 600;
				font-size: 35rpx;
			}

			.body {
				display: flex;
				justify-content: space-around;
				align-items: center;
			}

			.left {
				display: flex;
				align-items: center;
				font-size: 25rpx;
				width: 100%;

				.img {
					width: 30rpx;
					height: 30rpx;
					margin: 10rpx;

					._image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.right {
				display: flex;
				align-items: center;
				font-size: 25rpx;
				width: 100%;

				.img {
					width: 30rpx;
					height: 30rpx;
					margin: 10rpx;

					._image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}

		.pic {
			width: 100%;
			height: 100rpx;
		}

		.form_item {
			align-content: center;
			display: flex;
			padding-left: 20rpx;
			padding-right: 20rpx;

			.content {
				font-size: 30rpx;
				display: flex;
				align-content: center;
				justify-self: center;
			}
		}

		.input {
			display: flex;
			align-items: center;
		}

		.ele_form {}

		.form_container {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
		}

		.form_row {
			display: flex;
			gap: 20rpx;
		}

		.form_item_card {
			flex: 1;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			padding: 24rpx;
			border: 1px solid #e8e8e8;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			text-align: center;
			transition: all 0.3s ease;
		}

		.form_item_card:hover {
			transform: translateY(-2rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		}

		.item_label {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 12rpx;
			font-weight: 500;
		}

		.item_value {
			font-size: 32rpx;
			color: #333;
			display: flex;
			align-content: center;
		}

		.total {
			align-self: center;

			.money {
				font-size: 60rpx;
				font-weight: bold;
				color: red;
			}
		}

		.foot {
			display: flex;
			position: fixed;
			font-size: 30rpx;
			font-weight: bold;
			bottom: 0;
			left: 0;
			right: 0;
			width: 100%;
			background-color: #fff;
			border-top: 1px solid #e8e8e8;
			box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
			padding: 20rpx;
			box-sizing: border-box;
		}

		.foot_container {
			display: flex;
			justify-content: space-between;
			width: 100%;
		}

		.foot_btn_wrapper {
			display: flex;
			width: 100%;
			gap: 20rpx;
		}

		.foot_btn_left,
		.foot_btn_right {
			flex: 1;
			height: 80rpx !important;
			line-height: 80rpx !important;
			border-radius: 12rpx !important;
			font-size: 28rpx !important;
			font-weight: 500 !important;
			border: none !important;
			transition: all 0.2s ease;
		}

		.foot_btn_left {
			background-color: #f8f9fa !important;
			color: #666 !important;
			border: 1px solid #e8e8e8 !important;
		}

		.foot_btn_left:active {
			background-color: #e9ecef !important;
			transform: scale(0.98);
		}

		.foot_btn_right {
			background-color: #07c160 !important;
			color: #fff !important;
		}

		.foot_btn_right:active {
			background-color: #06a84e !important;
			transform: scale(0.98);
		}

		.foot_btn_right[disabled] {
			background-color: #d0d0d0 !important;
			color: #999 !important;
			cursor: not-allowed;
		}

		.upload {
			width: 100%;
			height: 120rpx;
			color: #fff;
			background: red;
			display: flex;
			position: fixed;
			justify-content: center;
			align-items: center;
			bottom: 0;
			margin: 10rpx;
		}

		view[disabled='true'] {
			background-color: gray;
		}

		.form {
			display: flex;
		}
	}
</style>