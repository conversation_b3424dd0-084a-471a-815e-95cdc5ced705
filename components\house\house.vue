<template>
	<view class="house">
		<text style="padding-left: 20rpx;padding-right: 20rpx;">{{address}}</text>
		<view v-for="(item, floorIndex) in floor" :key="floorIndex">
			<floor :floor="item" :type="type" @callback="getResult" :address="address"></floor>
		</view>
		<view style="display: flex;flex-direction: column;">
			<view v-if="floor.length&&list.length" style="font-size: 30rpx;font-weight: 550;margin: 10rpx 20rpx;">
				其他层
			</view>
			<view class="rooms" v-for="(item, roomIndex) in list" :key="item._id">
				<room :info="item" :type="type" @callback="getResult" :address="address">
				</room>
			</view>
		</view>

	</view>
</template>

<script setup>
	import {
		defineProps,
		defineEmits
	} from 'vue';

	const props = defineProps({
		list: {
			type: Array,
			default: () => []
		},
		address: {
			type: String,
			default: ''
		},
		floor: {
			type: Array,
			default: () => []
		},
		type: {
			type: Number,
			default: 0
		}
	});

	const emit = defineEmits(['callback']);

	const getResult = (e) => {
		emit('callback', e);
	};
</script>

<style lang="scss" scoped>
	.house {
		display: flex;
		/* 使用flex布局 */
		flex-direction: column;
		/* 列方向排列 */
		background-color: #eee;
	}
</style>