const {
	result
} = require('result');
const db = uniCloud.database()
const dbCmd = db.command
let dbJQL;
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function() { // 通用预处理器
		// this.params = this.getParams()[0]
		// dbJQL = uniCloud.databaseForJQL()
		this.startTime = Date.now();
		this.params = this.getHttpInfo()
		console.log("httpMethod", this.params.httpMethod);
		console.log("httpInfo", this.params);
		dbJQL = uniCloud.databaseForJQL({ // 获取JQL database引用，此处需要传入云对象的clientInfo
			clientInfo: this.getClientInfo()
		})
		let info = this.getClientInfo()
		console.log("this.getClientInfo", info);
		if (this.params.httpMethod == "POST") {
			//post请求
			// queryStringParameters: { uid: 'ddaadw' },	//参数在form-data时
			let body = this.getHttpInfo().body;
			if (!body) throw result(400, "required");
			this.params = JSON.parse(this.getHttpInfo().body)
		} else {
			//get请求
			this.params = this.getParams()[0]
		}
	},

	async get() {	
		console.log("get", this.params);
		let {
			uid
		} = this.params
		dbJQL.setUser({ //设置用户状态
			uid: uid,
			role: [],
			permission: []
		})
		let data = []
		let res = await dbJQL.collection("fangke_room_model").where(`uid == "${uid}"`).field(
			"_id,name").get()
		console.log("res", res);
		if (res.data.length > 0) {
			let list = res.data
			for (const item of list) {	//for循环里有序执行
				let count =  await dbJQL.collection("fangke_room").where(`config_layout == "${item.name}"`).count()
				console.log("getFangkeRoom", count.total);
				num = count.total
				let info = {
					...item,
					count: num
				}
				data.push(info)
			}
		}
		return result(0, "success", data)

	},

	async add() {
		console.log("add", this.params);
		let {
			uid
		} = this.params
		let list = []
		let building_list = []
		let building_list2 = []
		dbJQL.setUser({ //设置用户状态
			uid: uid,
			role: [],
			permission: []
		})
		let res = await dbJQL.collection("fangke_room_model").add(this.params).then(
			res => {
				console.log("添加户型", res);
				return res
			}) //获取自己楼房

		return result(0, "success", res)

	},

	async del (){
		let {
			uid,mode_id
		} = this.params
		dbJQL.setUser({ //设置用户状态
			uid: uid,
			role: [],
			permission: []
		})
		await dbJQL.collection("fangke_room_model").doc(mode_id).remove().then(
			res => {
				console.log("删除成功", res);
			})
		
		return result(0, "success", "")
	},
	
	async update(){
		console.log("update", this.params);
		let {
			uid,
			_id
		} = this.params
		dbJQL.setUser({ //设置用户状态
			uid: uid,
			role: [],
			permission: []
		})
		
		// 构建更新数据，移除 _id 字段
		let updateData = { ...this.params };
		delete updateData._id;
		
		let res = await dbJQL.collection("fangke_room_model").doc(_id).update(updateData).then(
			res => {
				console.log("修改户型", res);
				return res
			})
		
		return result(0, "success", res)
		
	},


	_after: function(error, result) {
		if (error) {
			throw error // 如果方法抛出错误，也直接抛出不处理
		}
		console.log("_after", result);
		result.total = Date.now() - this.startTime;
		return result
	}

}