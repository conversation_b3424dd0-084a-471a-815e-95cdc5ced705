<template>
	<view class="gridItem" @click="tap">
		<view class="badge">
			<uni-badge :text="bag.count"></uni-badge>
		</view>
		<view class="content">

			<view class="img" style="justify-content: center;display: flex;">
				<image class="image" :src="bag.icon" style="width: 80rpx; height: 80rpx;justify-items: center;"
					mode="aspectFill" />
			</view>
			<view class="title" style="justify-content: center;display: flex;">
				<text style="font-size: 25rpx;margin-top: 10rpx;">{{bag.name}}</text>
			</view>

		</view>
	</view>
</template>

<script setup>
	const props = defineProps({
		bag: {
			type: Object,
			required: true,
			default: () => ({})
		}
	});

	const emit = defineEmits(['onCallback']);

	const tap = () => {
		console.log('click', props.bag.name);
		emit('onCallback', props.bag.name);
	};
</script>

<style lang="scss" scoped>
	.gridItem {
		width: 100rpx;
		position: relative;

		.badge {
			display: flex;
			position: absolute;
			right: 0;
			top: -10%;
		}

		.content {
			display: flex;
			left: 50%;
			top: 50%;
			flex-direction: column;

		}
	}
</style>