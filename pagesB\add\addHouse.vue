<template>
    <view class="container">
        <uni-section title="房子信息" type="line">
            <view class="example">
                <!-- 展示不同的排列方式 -->
                <uni-forms ref="houseForm" :modelValue="houseFormData" :rules="rules">
                    <uni-forms-item label="小区/村名" name="name">
                        <uni-easyinput v-model="houseFormData.name" placeholder="请输入小区/村名,用于给租客看" />
                    </uni-forms-item>
                    <uni-forms-item label="楼栋名" required name="number">
                        <uni-easyinput v-model="houseFormData.number" placeholder="请输入楼栋名,用于方便自己查看" />
                    </uni-forms-item>

                    <uni-forms-item label="省市区/街道" required>
                        <view>
                            <uni-data-picker
                                placeholder="请选择地址"
                                popup-title="请选择城市"
                                collection="opendb-city-china"
                                field="code as value, name as text"
                                orderby="value asc"
                                :step-searh="true"
                                self-field="code"
                                parent-field="parent_code"
                                @change="onchange"
                                @nodeclick="onnodeclick"
                                v-model="selectedRegion"
                            ></uni-data-picker>
                        </view>
                    </uni-forms-item>

                    <uni-forms-item label="详细地址" required name="address">
                        <uni-easyinput
                            v-model="houseFormData.address"
                            :placeholder="isRegionSelected ? '请输入详细地址' : '请先选择省市区'"
                            :disabled="!isRegionSelected"
                            @blur="getAddress"
                        />
                    </uni-forms-item>
                    <uni-forms-item label="定位">
                        <map class="map" :latitude="latitude" :longitude="longitude" :markers="covers"></map>
                    </uni-forms-item>
                    <uni-forms-item label="描述" name="desc">
                        <uni-easyinput type="textarea" v-model="houseFormData.desc" placeholder="请输入房子的介绍" />
                    </uni-forms-item>
                </uni-forms>
            </view>
        </uni-section>

        <uni-section title="房间与楼层" type="line">
            <view class="line_raw">
                <view>是否为集中式房源</view>
                <switch :checked="isSwitch" color="#007AFF" style="transform: scale(0.7)" @change="switchChange"></switch>
            </view>
        </uni-section>
        <view v-if="isSwitch">
            <view class="line_raw">
                <view class="left">
                    <text>添加楼层</text>
                </view>
                <picker :value="floor" @change="bindFloor" class="pick" :range="floors">
                    <view class="right">
                        <view class="item"></view>
                        <view style="display: flex">
                            <text style="width: max-content">共{{ floor }}层</text>
                            <view class="arrow">
                                <image src="../../static/arrow_right.png" style="width: 100%; height: 100%; padding: 5rpx" mode="aspectFit"></image>
                            </view>
                        </view>
                    </view>
                </picker>
            </view>
            <view class="line_raw">
                <view class="left">
                    <text>添加房间</text>
                </view>
                <picker :value="num" @change="bindRoom" class="pick" :range="nums">
                    <view class="right">
                        <view class="item"></view>
                        <view style="display: flex">
                            <text style="width: max-content">一层{{ num }}间房</text>
                            <view class="arrow">
                                <image src="../../static/arrow_right.png" style="width: 100%; height: 100%; padding: 5rpx" mode="aspectFit"></image>
                            </view>
                        </view>
                    </view>
                </picker>
            </view>
            <view class="line_raw" @click="editRoom(floor, num)">
                <view class="left">
                    <text>编辑房间</text>
                </view>
                <view class="right_text">
                    <view>共{{ roomLists.length }}间</view>
                    <view class="arrow">
                        <image src="../../static/arrow_right.png" style="width: 100%; height: 100%; padding: 5rpx" mode="aspectFit"></image>
                    </view>
                </view>
            </view>
        </view>
        <view v-else>
            <view class="line_raw">
                <view class="left">
                    <text>添加房间</text>
                </view>
                <view class="right">
                    <button type="primary" size="mini" @click="add">新增房间</button>
                </view>
            </view>
            <view class="example">
                <!-- 动态表单校验 -->
                <uni-forms ref="roomFrom" :modelValue="roomFormData">
                    <uni-forms-item v-for="(item, index) in roomFormData.room" :key="item.id" :label="item.label + '' + (index + 1)" required :name="roomFormData.room[index].id">
                        <view style="display: flex; align-items: center">
                            <uni-easyinput v-model="roomFormData.room[index].name" placeholder="请输入房间号" />
                            <button class="button" size="mini" type="default" @click="del(item.id)">删除</button>
                        </view>
                    </uni-forms-item>
                </uni-forms>
            </view>
        </view>

        <button type="primary" @click="submit" style="margin-top: 20rpx">提交</button>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { userPhone } from '../utils/validate.js';
// 引入SDK核心类，js文件根据自己业务，位置可自行放置
// import QQMapWX from '../utils/qqmap-wx-jssdk.min.js'
const fangdong = computed(() => JSON.parse(uni.getStorageSync('fangdong')));

const qqMapSDK = ref(null);
const db = uniCloud.database();
const buildingObj = uniCloud.importObject('fangke_building');
const houseForm = ref(null);
const longitude = ref('113.255879'); //经度
const latitude = ref('23.172938'); //纬度
const covers = ref([
    {
        latitude: 23.172938,
        longitude: 113.255879,
        iconPath: '../../static/location.png'
    }
]); //标记点
// 响应式数据
const houseFormData = ref({
    name: '',
    number: '',
    address: '',
    desc: ''
});

// 地区选择相关数据
const selectedRegion = ref('');
const selectedRegionList = ref([]);
const selectedRegionText = ref('');
const isRegionSelected = ref(false);

const userFormData = ref({
    u_name: '',
    code: '',
    tel: ''
});
const userForm = ref(null);
const tel = ref('');
const current = ref(0);
const items = ref(['左对齐', '顶部对齐']);
const rules = ref({
    title: {
        rules: [
            {
                required: true,
                errorMessage: '标题不能为空'
            }
        ]
    },
    name: {
        rules: [
            {
                required: true,
                errorMessage: '姓名不能为空'
            }
        ]
    },
    u_name: {
        rules: [
            {
                required: true,
                errorMessage: '姓名不能为空'
            }
        ]
    },
    address: {
        rules: [
            {
                required: true,
                errorMessage: '地址不能为空'
            }
        ]
    },
    tel: {
        rules: [
            {
                required: true,
                errorMessage: '手机号码不能为空'
            },
            {
                format: 'number'
            },
            {
                validateFunction: function (rule, value, data, callback) {
                    let phone = /^(13[0-9]|14[1579]|15[0-3,5-9]|16[6]|17[0123456789]|18[0-9]|19[89])\d{8}$/;
                    if (userPhone('' + value)) {
                        return true;
                    }
                    callback('请输入有效电话号码');
                    return false;
                }
            }
        ]
    },
    code: {
        rules: [
            {
                required: true,
                errorMessage: '验证码不能为空'
            }
        ]
    }
});

const roomFormData = ref({
    room: []
});

const roomLists = ref([]);
const building_id = ref('');
const info = ref({});
const uid = ref('');
const floor = ref(0);
const num = ref(0);
const floors = ref([]);
const nums = ref([]);
const value = ref(0);
const floor_v = ref(false);
const floor_v2 = ref(false);
const isSwitch = ref(true);
const isManager = ref(false);
const desc = ref('');

// 初始化楼层和房间数选项
for (let i = 1; i <= 99; i++) {
    floors.value.push(i);
    nums.value.push(i);
}

// 页面加载后初始化
onMounted(() => {
    // 设置表单验证规则（需要在模板中引用表单组件）
    // userForm.value.setRules(rules.value)
});

// 页面加载时处理参数
onLoad((e) => {
    console.log('addHouse', e);
    uid.value = uniCloud.getCurrentUserInfo().uid;

    // 监听房间列表更新
    uni.$on('roomList', (res) => {
        console.log('监听的列表是：', res);
        let list = [];
        floor.value = res.length;

        res.forEach(function (element) {
            element.forEach((item) => {
                item.rent_status = 0;
                list.push(item);
            });
        });

        roomLists.value = list;
    });
});

const getAddress = () => {
    if (!houseFormData.value.address || !isRegionSelected.value) {
        return;
    }

    let data = {
        address: selectedRegionText.value + houseFormData.value.address
    };
    console.log('address', data.address);

    buildingObj
        .getMap(data)
        .then((res) => {
            console.log('获取地址的经纬度', res);
            if (res.data) {
                if (res.data.data.status == 0) {
                    let { lat, lng } = res.data.data.result.location;
                    // 更新经纬度
                    latitude.value = lat.toString();
                    longitude.value = lng.toString();
                    // 更新地图标记
                    covers.value = [
                        {
                            latitude: lat,
                            longitude: lng,
                            iconPath: '../../static/location.png'
                        }
                    ];
                } else {
                    if (res.data.message == '参数错误') {
                        uni.showToast({
                            title: '请输入正确的具体地址',
                            icon: 'error'
                        });
                    } else {
                        uni.showToast({
                            title: '地址解析失败',
                            icon: 'error'
                        });
                    }
                }
            }
        })
        .catch((err) => {
            console.error('地址解析错误:', err);
            uni.showToast({
                title: '地址解析失败',
                icon: 'error'
            });
        });
};

// 获取地区大概位置
const getRegionLocation = () => {
    if (!selectedRegionText.value) {
        return;
    }

    let data = {
        address: selectedRegionText.value
    };
    console.log('获取地区位置:', data.address);

    buildingObj
        .getMap(data)
        .then((res) => {
            console.log('获取地区的经纬度', res);
            if (res.data) {
                if (res.data.data.status == 0) {
                    let { lat, lng } = res.data.data.result.location;
                    // 更新经纬度
                    latitude.value = lat.toString();
                    longitude.value = lng.toString();
                    // 更新地图标记
                    covers.value = [
                        {
                            latitude: lat,
                            longitude: lng,
                            iconPath: '../../static/location.png'
                        }
                    ];

                    console.log('地区定位成功:', selectedRegionText.value);
                }
            }
        })
        .catch((err) => {
            console.error('地区定位错误:', err);
        });
};

// 提交表单
const submit = async () => {
    let sum = 0;
    let data = {};
    data.name = houseFormData.value.name;
    // 验证房子信息表单
    await houseForm.value
        .validate()
        .then((res) => {
            console.log('房子信息', res);
            sum = sum + 1;
            setBuildingData(data, res);
        })
        .catch((err) => {
            console.log('err', err);
        });


		// 验证地区选择
		if (!isRegionSelected.value) {
			uni.showToast({
				title: "请选择省市区",
				icon: 'none'
			});
			return;
		}
		
		// 处理房间信息
		if (!isSwitch.value) {
			roomLists.value = [];
			let isFinish = true;

        for (let i = 0; i < roomFormData.value.room.length; i++) {
            console.log('item', roomFormData.value.room[i]);
            if (!roomFormData.value.room[i].name.length) {
                isFinish = false;
                break;
            }

            let room = {
                floor: 1,
                name: roomFormData.value.room[i].name,
                rent_status: 0
            };

            roomLists.value.push(room);
        }

        if (!isFinish) {
            uni.showToast({
                title: '房间名不能为空',
                icon: 'none'
            });
            return;
        }
    }

    // 提交数据
    if ((sum == 2 && isManager.value) || (sum == 1 && !isManager.value)) {
        data.update_time = Date.now();

        // 保存经纬度信息
        if (longitude.value && latitude.value) {
            data.location = [longitude.value, latitude.value];
        }
        console.log('房间信息', roomLists.value);
        await addBuilding(data, roomLists.value);
    } else {
        uni.showToast({
            title: '请完成爆红的选项',
            icon: 'none'
        });
    }
};

// 设置楼房数据
const setBuildingData = (data, res) => {
    data.number = res.number;

    // 组合完整地址：地区 + 详细地址
    let fullAddress = '';
    if (selectedRegionText.value) {
        fullAddress = selectedRegionText.value;
    }
    if (res.address) {
        fullAddress += res.address;
    }
    data.address = fullAddress;
    data.desc = res.desc;
    console.log('selectedRegion.value', selectedRegion.value);
    // 保存省市区信息
    if (selectedRegionList.value && selectedRegionList.value.length > 0) {
        // 根据选择的级别保存对应信息
        const regionLevels = selectedRegionList.value;

        if (regionLevels.length >= 1) {
            // 省份信息
            data.province_code = regionLevels[0].value;
            data.province_name = regionLevels[0].text;
        }

        if (regionLevels.length >= 2) {
            // 城市信息
            data.city_code = regionLevels[1].value;
            data.city_name = regionLevels[1].text;
        }

        if (regionLevels.length >= 3) {
            // 区县信息
            data.district_code = regionLevels[2].value;
            data.district_name = regionLevels[2].text;
        }
    }

    return data;
};

// 设置管理员信息
const setManagerInfo = (data, res) => {
    data.manager = res.u_name;
    data.tel = res.tel;
    return data;
};

// 添加楼房
const addBuilding = async (data, rooms) => {
    data.sum = 0; // 初始化总数
    data.uid = uid.value;
    data.room_num = roomLists.value.length;
    data.floor = floor.value;

    if (isSwitch.value) {
        data.type = 1;
    } else {
        data.type = 2;
    }
    console.log('rooms', rooms);
    let message_count = fangdong.value.message_max;
    let contract_count = fangdong.value.contract_max;
    let params = {
        data,
        room_list: rooms,
        message_count,
        contract_count
    };
    console.log('上传数据data', params);
    await uni.$lkj.api
        .addBuilding(params)
        .then((res) => {
            console.log('请求成功', res);
            building_id.value = res.data;
            if (res.errCode == 0) {
                uni.showToast({
                    title: '添加成功',
                    icon: 'none'
                });
                setTimeout(() => {
                    uni.reLaunch({
                        url: '/pages/house/house'
                    });
                }, 800);
            } else {
                uni.showToast({
                    title: '添加失败：' + res.errMsg,
                    icon: 'none'
                });
            }
        })
        .catch((err) => {
            uni.showToast({
                title: err,
                icon: 'none'
            });
        });
};

// 添加楼层房间
const addBulid = () => {
    console.log('addBulid：', floor.value);
    let total = 0;
    total = floor.value * num.value;
    console.log('addBulid：', total + '_' + floor.value + '_' + num.value);

    for (let i = 0; i < total; i++) {
        let name = '';
        let ceng = parseInt(i / num.value) + 1;
        let numVal = parseInt(i % num.value) + 1;
        name = ceng + '0' + numVal;
        console.log('添加房间：', name);

        let room = {
            floor: ceng,
            name: name,
            rent_status: 0
        };

        roomLists.value.push(room);
    }
};

// 编辑房间
const editRoom = (floor, num) => {
    uni.navigateTo({
        url: '/pagesB/buildingRoom/buildingRoom?floor=' + floor + '&num=' + num
    });
};

// 编辑楼层
const editFloor = (type) => {
    console.log('点击楼层', type);
    if (type == 1) {
        floor_v.value = true;
    } else {
        floor_v2.value = true;
    }
};

// 绑定楼层选择
const bindFloor = (e) => {
    roomLists.value = [];
    console.log('bindFloor', e.detail.value);
    let value = e.detail.value;
    floor.value = floors.value[Number(value)];
    addBulid();
};

// 绑定房间数选择
const bindRoom = (e) => {
    roomLists.value = [];
    console.log('bindRoom', e.detail.value);
    let value = e.detail.value;
    num.value = nums.value[Number(value)];
    addBulid();
};

// 切换自动生成房间
const switchChange = () => {
    isSwitch.value = !isSwitch.value;
    roomLists.value = [];
    floor.value = 0;
    num.value = 0;
};

// 切换是否有管理员
const switchManager = () => {
    isManager.value = !isManager.value;
};

// 添加房间
const add = () => {
    let room = {
        label: '房间',
        id: Date.now().toString(),
        name: ''
    };

    roomFormData.value.room.push(room);
    console.log('roomlist=', roomFormData.value.room);
};

// 删除房间
const del = (id) => {
    let index = roomFormData.value.room.findIndex((v) => v.id === id);
    roomFormData.value.room.splice(index, 1);
};

// 城市选择变化
const onchange = (e) => {
    const value = e.detail.value;
    console.log('城市选择数据:', value);
    selectedRegionList.value = value;
    if (value && value.length > 0) {
        // 构建完整的地区文本
        selectedRegionText.value = value.map((item) => item.text).join('');

        // 更新选择状态
        isRegionSelected.value = true;

        // 如果用户重新选择了地区，清空详细地址
        if (houseFormData.value.address) {
            // 如果地址中包含之前的地区信息，则清空重新输入
            houseFormData.value.address = '';
        }

        console.log('选择的地区:', selectedRegionText.value);

        // 自动获取地区的大概位置
        getRegionLocation();
    } else {
        // 清空选择
        selectedRegionText.value = '';
        isRegionSelected.value = false;
        houseFormData.value.address = '';
    }
};

// 节点点击事件
const onnodeclick = (node) => {
    console.log('onnodeclick', node);
};
</script>

<style lang="scss" scoped>
.container {
    margin-left: 15rpx;
    margin-right: 15rpx;
    margin-bottom: 30rpx;
}

.example {
    padding-left: 15px;
    padding-right: 15px;
    background-color: #fff;

    .map {
        width: 100%;
        height: 300rpx;
    }
}

// .button {
// 	display: flex;
// 	align-items: center;
// 	height: 35px;
// 	margin-left: 10px;
// }

.verify {
    display: flex;
    justify-content: space-around;
    align-items: center;

    .inputCode {
        margin-right: 10rpx;
    }
}

.uploadBtn {
    margin-top: 10rpx;
}

.addBuild {
    display: flex;
    padding: 10rpx;
    align-items: center;
}

.line_raw {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    padding-left: 15px;
    padding-right: 15px;
    background-color: #fff;
    justify-content: space-between;
    align-content: center;
    align-items: center;

    .left {
        display: flex;
        font-size: 14px;
        color: #606266;
        width: 200rpx;
    }

    .pick {
        width: 100%;

        .right {
            display: flex;

            .item {
                display: flex-end;
                width: 100%;
            }

            .arrow {
                width: 30rpx;
            }
        }
    }

    .right_text {
        display: flex;

        .arrow {
            width: 30rpx;
        }
    }
}
</style>
