<template>
    <view class="layout">
        <view :class="['navbar',isTransparent?'active':'']">
            <view class="statusBar" :style="{height: getStatusBarHeight + 'px'}"></view>
            <view class="titleBar" :style="{height: getTitleBarHeight + 'px'}">
                <view class="location" @click="handleLeftClick" v-if="showLeftIcon">
                    <uni-icons :type="leftIconType" size="20" color="#333" ></uni-icons>
					<view class="logo">{{logo}}</view>
                </view>
				 <text class="title" v-if="showTitle">{{ title }}</text>
                <navigator url="/pages/goodSearch/goodSearch" class="search" v-if="showSearch">
					<uni-icons class="icon" type="search" color="#888" size="18"></uni-icons>
					<text class="text">{{ placeholder }}</text>
                </navigator>
                <uni-icons :type="rightIconType" size="20" color="#333" v-if="showRightIcon" @click="handleRightClick"></uni-icons>
                <!-- <view style="height: 50rpx;width: 50rpx;margin-left: 20rpx;display: flex;align-self: center;align-content: center;align-items: center;"
                    @click="toggle('top')">
                    <uni-icons class="icon" type="list" color="#888" size="18"></uni-icons>
                </view> -->
            </view>
        </view>

        <view class="fill" :style="{height: getNavBarHeight + 'px'}">

        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, defineProps,onMounted, nextTick } from 'vue';
// // 定义 props 的类型
// interface NavBarProps {
//     title: string;
//     placeholder: string;
//     showLeftIcon:boolean | false;
// }

// 仅使用类型参数
const props = defineProps<{
	logo:string;
	title: string;
	placeholder: string;
	showLeftIcon:boolean | true;
	leftIconType:string | 'location';
	showRightIcon:boolean | false;
	rightIconType:string | 'notification';
	showSearch:boolean | false;
	isTransparent:boolean;
	showTitle:boolean;
}>();
// const leftType = ref('location')
const emit = defineEmits(['onLeft','onRight'])

// 计算状态栏高度
const getStatusBarHeight = computed(() => uni.getSystemInfoSync().statusBarHeight || 15);

// 计算标题栏高度
const getTitleBarHeight = computed(() => {
    if (uni.getMenuButtonBoundingClientRect) {
        const { top, height } = uni.getMenuButtonBoundingClientRect();
        return height + (top - getStatusBarHeight.value) * 2;
    } else {
        return 40;
    }
});

// 计算导航栏高度
const getNavBarHeight = computed(() => getStatusBarHeight.value + getTitleBarHeight.value);

// 计算左侧图标左边距
const getLeftIconLeft = computed(() => {
    // #ifdef MP-TOUTIAO
    const { leftIcon: { left, width } } = tt.getCustomButtonBoundingClientRect();
    return left + parseInt(width);
    // #endif

    // #ifndef MP-TOUTIAO
    return 0;
    // #endif
});

// 处理位置点击事件
const handleLeftClick = (): void => {
    // 这里可以添加具体的点击逻辑
	emit("onLeft")
};

const handleRightClick = (): void => {
    // 这里可以添加具体的点击逻辑
	emit("onRight")
};
// 处理 toggle 事件
// const toggle = (res: string): void => {
//     console.log("toggle");
//     emit("toggle", res);
// };
</script>

<style lang="scss" scoped>
.layout {
    .navbar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 10;
        background:
            linear-gradient(to bottom, transparent, #fff 400rpx),
            linear-gradient(to right, #beecd8 20%, #F4E2D8);
		
        .statusBar {}

        .titleBar {
            display: flex;
            align-items: center;
            padding: 0 20rpx;

			.logo{
				font-size: 22px;
				font-weight: 700;
				color: #000;
				padding: 0;
				margin: 0;
			}
            .title {
				flex: 1;
                font-size: 30rpx;
				display: flex;
				justify-content: center;
                color: #000;
				padding: 0;
				margin: 0;
            }

            .search {
                width: 300rpx;
                height: 50rpx;
                border-radius: 60rpx;
                background: rgba(255, 255, 255, 0.4);
                border: 1px solid #fff;
                color: #999;
                font-size: 28rpx;
                display: flex;
                align-items: center;
				margin:  0 20rpx;
                .icon {
                    margin-left: 10rpx;
                }

                .text {
                    padding-left: 10rpx;
					font-size: 20rpx;
                }
            }
        }
    }
	.navbar.active{
		background: none;
	}
    .fill {}
}
</style>    